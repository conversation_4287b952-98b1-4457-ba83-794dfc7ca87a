/**
*** <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tote labels for Apollo printer .
*** File name is hard coded in lbl_prnt.w .
**/


/* Version control Id */
DEFINE VARIABLE SCCS_ID AS CHARACTER
    NO-UNDO
    INITIAL "@(#) $Header: /pdb/9.01/remote/RCS/apl_pllt.p,v 1.2 2000/02/24 20:08:48 tanya Exp $~n"
.

DEF INPUT PARAMETER ch_type    AS CHAR NO-UNDO .
DEF INPUT PARAMETER i_lbl_size AS INT  NO-UNDO .
DEF INPUT PARAMETER i_qty      AS INT  NO-UNDO .
DEF INPUT PARAMETER i_exact    AS INT  NO-UNDO .
DEF INPUT PARAMETER ch_printer AS CHAR NO-UNDO .


DEF VAR ch_file    AS CHAR NO-UNDO .
DEF VAR ch_command AS CHAR NO-UNDO .
DEF VAR ch_label   AS CHAR NO-UNDO .
DEF VAR ch_text    AS CHAR NO-UNDO .
DEF VAR i_count    AS INT  NO-UNDO .



/*
** Read parameters from the environment
*/

MESSAGE "Starting Printing..." .


/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .

OUTPUT TO VALUE ( ch_file ).    

/* Main block */
   CASE ch_type :
      WHEN 'P' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Pallet' .

      WHEN 'C' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Carton' .

      WHEN 'T' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Tote' .

      OTHERWISE
      DO:
        MESSAGE "Invalid type passed".
        return error .
      END.

   END CASE .

   IF i_exact > 0 THEN
   DO: /* Exact item - only print it... */
      ASSIGN
        ch_text = ch_type + STRING ( i_exact, "999999999" ) .

      RUN do_label ( ch_label , ch_text ) .
   END.
   ELSE
   DO: /* Print as many specified... */
      ASSIGN 
         i_count = 1 .

      printmanyloop:
      DO WHILE TRUE:
         IF ( i_count > i_qty ) THEN 
            LEAVE printmanyloop.
         
         CASE ch_type :
            WHEN 'P' THEN
               ASSIGN
                  ch_text = 'P' + STRING ( 
                         NEXT-VALUE ( print_pallet ) , '999999999' ) .

            WHEN 'C' THEN
               ASSIGN
                  ch_text = 'C' + STRING ( 
                         NEXT-VALUE ( cartonmst_carton_id ) , '999999999' ) .

            WHEN 'T' THEN
               ASSIGN
                  ch_text = 'T' + STRING (
                         NEXT-VALUE ( print_pallet ) , '999999999' ) .
         END CASE .

         RUN do_label ( ch_label , ch_text ) .
         
         i_count = i_count + 1 . 
      END.
   END.

/* Print Carton or Pallet Label to file in temp dir. */

OUTPUT CLOSE . 

if (opsys eq "UNIX")
then do:
    ASSIGN
    ch_command = "lp -c -d" + ch_printer + " " + ch_file .
    message ch_command .
    OS-COMMAND SILENT VALUE ( ch_command ) .

    OS-DELETE         VALUE ( ch_file    ) .
end .
else do: /*NT*/

      /***************** old code using spooler *******************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .
    *****************************************************************/
    
    
       message "copying file  " ch_file "to printer " ch_printer .

       OS-COPY VALUE( ch_file ) VALUE( ch_printer) .

       OS-DELETE  VALUE ( ch_file  ) .   



end .


message "Done!" .
return .

/* End Of Program */

                             
/************************ Do Label *************************/
PROCEDURE do_label:
   DEF INPUT PARAMETER ch_label    AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_text     AS CHAR NO-UNDO .

   DEF VAR ch_prnt_label AS CHAR NO-UNDO .
   DEF VAR ch_prnt_text  AS CHAR NO-UNDO .


     PUT UNFORMATTED 
         "m i~n"
         "J~n"
         "H 5.2,5,T,R0~n" .

     IF i_lbl_size = 0 THEN  /* 4X6 */

        PUT UNFORMATTED
           "O R~n"
           "Sl1;.0,.0,6,6.10,4.0~n"   
           "G 2.80,0.15,270;  L:5.80,0.02~n"
           "G 0.25,0.15,0;    R:3.75,5.8,0.02,0.02~n"
           "B 2.50,0.80,270,code128,2.0,0.04;" + ch_text + "~n" 
           "T 3.80,0.25,270,5,0.18,h.14;" + ch_label + " ID:~n"
           "T 3.00,0.70,270,5,0.90,h.60;" + ch_text + "~n"
           "A 1~n" .

     ELSE  /* 2X4 */
     
       PUT UNFORMATTED
         "Sl1;0,0.05,2.00,2.10,4.0~n"
         "G 0.0,0.8,0;   L:3.80,0.02~n"
         "G 0,0,0;    R:3.8,1.85,0.02,0.02~n"
         "B 0.40,0.9,0,code128,0.8,0.028;" + ch_text + "~n"
         "T 0.05,0.15,0,3,pt09;" + ch_label + " ID:~n"       
         "T 0.55,0.60,0,5,0.65,h.35;" + ch_text + "~n"
         "A 1~n" .
 

END PROCEDURE.
