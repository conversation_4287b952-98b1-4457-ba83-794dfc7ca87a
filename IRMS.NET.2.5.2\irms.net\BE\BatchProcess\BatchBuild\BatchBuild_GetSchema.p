/**=================================================================**
* S:\IRMS.NET.2.5.2\irms.net\BE\BatchProcess\BatchBuild\BatchBuild_GetSchema.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 08/19/09, 16:30 PM
**=================================================================**/


/* Business Entity Definintions */
{BatchProcess/BatchBuild/BatchBuild_ds.i}
{BatchProcess/BatchBuild/BatchBuild_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Schema.


    RUN Schema_Fill .


/**************************** END OF FILE ****************************/


