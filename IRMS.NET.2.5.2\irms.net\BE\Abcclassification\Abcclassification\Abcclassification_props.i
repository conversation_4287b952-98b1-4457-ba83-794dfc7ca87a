/**=================================================================**
* Y:\BE_Area\src\be\Abcclassification\Abcclassification\Abcclassification_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/10/06, 16:46 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qABC FOR ABC SCROLLING . 
QUERY qABC:QUERY-PREPARE("FOR EACH ABC").
QUERY qABC:QUERY-OPEN.


DEF  QUERY qABC_BEFORE FOR ABC_BEFORE SCROLLING . 
QUERY qABC_BEFORE:QUERY-PREPARE("FOR EACH ABC_BEFORE").
QUERY qABC_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "ABC" */
DEFINE BUFFER ABC_1 FOR irms.ABC.
DEFINE QUERY qSrcABC
    FOR ABC_1
        SCROLLING.
DEFINE DATA-SOURCE ABC
    FOR QUERY qSrcABC
        ABC_1 KEYS (co_num,wh_num,recalc_last)        .
DATA-SOURCE ABC:PREFER-DATASET = no.
DATA-SOURCE ABC:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ABC                          AS  HANDLE              
    FIELD   hQry_ABC                         AS  HANDLE              
    FIELD   hQry_ABC_BEFORE                  AS  HANDLE              
    FIELD   ABC_DataSourceHdl                AS  HANDLE              
    FIELD   ABC_BatchSize                    AS  INTEGER             
                                                 INIT 50
    FIELD   ABC_Fill                         AS  LOGICAL             
                                                 INIT yes
    FIELD   ABC_CanRead                      AS  LOGICAL             
                                                 INIT yes
    FIELD   ABC_CanCreate                    AS  LOGICAL             
                                                 INIT yes
    FIELD   ABC_CanUpdate                    AS  LOGICAL             
                                                 INIT yes
    FIELD   ABC_CanDelete                    AS  LOGICAL             
                                                 INIT yes
    FIELD   ABC_Src_Names                    AS  CHARACTER           
                                                 INIT ""
    FIELD   ABC_Src_Hdls                     AS  CHARACTER           
                                                 INIT ""
    FIELD   ABC_CurrentSource                AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   ABC_UniqueKey                    AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ABC_ABC_Map                      AS  CHARACTER           
                                                 INIT ""
    FIELD   ABC_ABC_CF                       AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ABC_ABC_NoP                      AS  CHARACTER           
                                                 INIT ""
    FIELD   ABC_hdl                          AS  HANDLE              
    FIELD   ABC_UseQuery                     AS  LOGICAL             
                                                 INIT yes
    FIELD   ABC_PostTable                    AS  CHARACTER           
                                                 INIT "ABC_1"
    FIELD   ABC_qhdl                         AS  HANDLE              
    FIELD   ABC_ABC_1_W                      AS  CHARACTER           
                                                 INIT ""
    FIELD   ABC_ABC_1_S                      AS  CHARACTER           
                                                 INIT ""
    FIELD   ABC_Buffs                        AS  CHARACTER           
                                                 INIT "ABC_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "ABC,ABC"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "ABC,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsAbcclassification:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_ABC                          = QUERY qABC:HANDLE
       htt_ABC                           = TEMP-TABLE ABC:HANDLE
       hQry_ABC_BEFORE                   = QUERY qABC_BEFORE:HANDLE
       ABC_src_Names                     = 'ABC,Default'
       ABC_src_Hdls                      =         STRING(DATA-SOURCE ABC:HANDLE)
                                           + ',' + STRING(DATA-SOURCE ABC:HANDLE)
       ABC_ABC_Map                       =         'A_count_interval,ABC_1.A_count_interval'
                                           + ',' + 'a_count_loc,ABC_1.a_count_loc'
                                           + ',' + 'A_count_percent,ABC_1.A_count_percent'
                                           + ',' + 'A_dollar_percent,ABC_1.A_dollar_percent'
                                           + ',' + 'B_count_interval,ABC_1.B_count_interval'
                                           + ',' + 'b_count_loc,ABC_1.b_count_loc'
                                           + ',' + 'B_count_percent,ABC_1.B_count_percent'
                                           + ',' + 'B_dollar_percent,ABC_1.B_dollar_percent'
                                           + ',' + 'count_type,ABC_1.count_type'
                                           + ',' + 'co_num,ABC_1.co_num'
                                           + ',' + 'C_count_interval,ABC_1.C_count_interval'
                                           + ',' + 'c_count_loc,ABC_1.c_count_loc'
                                           + ',' + 'C_count_percent,ABC_1.C_count_percent'
                                           + ',' + 'C_dollar_percent,ABC_1.C_dollar_percent'
                                           + ',' + 'GUID,ABC_1.GUID'
                                           + ',' + 'O_count_interval,ABC_1.O_count_interval'
                                           + ',' + 'o_count_loc,ABC_1.o_count_loc'
                                           + ',' + 'O_count_percent,ABC_1.O_count_percent'
                                           + ',' + 'O_dollar_percent,ABC_1.O_dollar_percent'
                                           + ',' + 'recalc_interval,ABC_1.recalc_interval'
                                           + ',' + 'recalc_last,ABC_1.recalc_last'
                                           + ',' + 'recalc_timeframe,ABC_1.recalc_timeframe'
                                           + ',' + 'recalc_type,ABC_1.recalc_type'
                                           + ',' + 'wh_num,ABC_1.wh_num'
       ABC_hdl                           = DATA-SOURCE ABC:HANDLE
       ABC_qhdl                          = QUERY qSrcABC:HANDLE
       TopLevelTables                    = 'ABC'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/ABCClass_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAbcclassification .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAbcclassification BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAbcclassification .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAbcclassification BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ABC_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAbcclassification .
     RUN ABC_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAbcclassification BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ABC_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAbcclassification .
     RUN ABC_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAbcclassification BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_ABC_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAbcclassification .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('ABC') NO-ERROR .
     RUN ABC_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAbcclassification BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ABC_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAbcclassification .
     RUN AfterRowFill  IN THIS-PROCEDURE ('ABC') NO-ERROR .
     RUN ABC_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAbcclassification BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


