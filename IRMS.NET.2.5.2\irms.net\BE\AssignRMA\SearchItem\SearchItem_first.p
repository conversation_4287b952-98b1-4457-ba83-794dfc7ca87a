/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\SearchItem\SearchItem_first.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:39 PM
**=================================================================**/


/* Business Entity Definintions */
{AssignRMA/SearchItem/SearchItem_ds.i}
{AssignRMA/SearchItem/SearchItem_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF       OUTPUT PARAM DATASET FOR dsSearchItem .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN
        CREATE ds_Control.
    ASSIGN ds_Control.PropName = 'COMMAND'
           ds_Control.PropValue = 'FILL'.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


