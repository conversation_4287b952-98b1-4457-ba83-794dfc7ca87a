/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\SearchReturnQuantity\SearchReturnQuantity_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:39 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qttrtdetSrch FOR ttrtdetSrch SCROLLING . 
QUERY qttrtdetSrch:QUERY-PREPARE("FOR EACH ttrtdetSrch").
QUERY qttrtdetSrch:QUERY-OPEN.


DEF  QUERY qttrtdetSrch_BEFORE FOR ttrtdetSrch_BEFORE SCROLLING . 
QUERY qttrtdetSrch_BEFORE:QUERY-PREPARE("FOR EACH ttrtdetSrch_BEFORE").
QUERY qttrtdetSrch_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "srcttrtdetsh" */
DEFINE BUFFER rtdet_1 FOR irms.rtdet.
DEFINE QUERY qSrcsrcttrtdetsh
    FOR rtdet_1
        SCROLLING.
DEFINE DATA-SOURCE srcttrtdetsh
    FOR QUERY qSrcsrcttrtdetsh
        rtdet_1 KEYS (rt_id,line_num)        .
DATA-SOURCE srcttrtdetsh:PREFER-DATASET = no.
DATA-SOURCE srcttrtdetsh:MERGE-BY-FIELD = yes.

/* DATA-SOURCE: "srcttorddtlsh" */
DEFINE BUFFER orddtl_1 FOR irms.orddtl.
DEFINE BUFFER item_1 FOR irms.item.
DEFINE QUERY qSrcsrcttorddtlsh
    FOR orddtl_1,
        item_1
        SCROLLING.
DEFINE DATA-SOURCE srcttorddtlsh
    FOR QUERY qSrcsrcttorddtlsh
        orddtl_1 KEYS (id,line,line_sequence),
        item_1 KEYS (co_num,wh_num,abs_num)        .
DATA-SOURCE srcttorddtlsh:PREFER-DATASET = no.
DATA-SOURCE srcttorddtlsh:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttrtdetSrch                  AS  HANDLE              
    FIELD   hQry_ttrtdetSrch                 AS  HANDLE              
    FIELD   hQry_ttrtdetSrch_BEFORE          AS  HANDLE              
    FIELD   ttrtdetSrch_DataSourceHdl        AS  HANDLE              
    FIELD   ttrtdetSrch_BatchSize            AS  INTEGER             
                                                 INIT 50
    FIELD   ttrtdetSrch_Fill                 AS  LOGICAL             
                                                 INIT yes
    FIELD   ttrtdetSrch_CanRead              AS  LOGICAL             
                                                 INIT yes
    FIELD   ttrtdetSrch_CanCreate            AS  LOGICAL             
                                                 INIT no
    FIELD   ttrtdetSrch_CanUpdate            AS  LOGICAL             
                                                 INIT no
    FIELD   ttrtdetSrch_CanDelete            AS  LOGICAL             
                                                 INIT no
    FIELD   ttrtdetSrch_Src_Names            AS  CHARACTER           
                                                 INIT ""
    FIELD   ttrtdetSrch_Src_Hdls             AS  CHARACTER           
                                                 INIT ""
    FIELD   ttrtdetSrch_CurrentSource        AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   ttrtdetSrch_UniqueKey            AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ttrtdetSrch_srcttrtdetsh_Map     AS  CHARACTER           
                                                 INIT ""
    FIELD   ttrtdetSrch_srcttrtdetsh_CF      AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ttrtdetSrch_srcttrtdetsh_NoP     AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttrtdetsh_hdl                 AS  HANDLE              
    FIELD   srcttrtdetsh_UseQuery            AS  LOGICAL             
                                                 INIT yes
    FIELD   srcttrtdetsh_PostTable           AS  CHARACTER           
                                                 INIT "rtdet_1"
    FIELD   srcttrtdetsh_qhdl                AS  HANDLE              
    FIELD   srcttrtdetsh_rtdet_1_W           AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttrtdetsh_rtdet_1_S           AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttrtdetsh_Buffs               AS  CHARACTER           
                                                 INIT "rtdet_1"
    FIELD   srcttorddtlsh_hdl                AS  HANDLE              
    FIELD   srcttorddtlsh_UseQuery           AS  LOGICAL             
                                                 INIT yes
    FIELD   srcttorddtlsh_PostTable          AS  CHARACTER           
                                                 INIT "orddtl_1"
    FIELD   srcttorddtlsh_qhdl               AS  HANDLE              
    FIELD   srcttorddtlsh_orddtl_1_W         AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_orddtl_1_S         AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_item_1_W           AS  CHARACTER           
                                                 INIT "item_1.co_num = orddtl_1.co_num and item_1.wh_num = orddtl_1.wh_num and  item_1.abs_num = orddtl_1.abs_num"
    FIELD   srcttorddtlsh_item_1_S           AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_Buffs              AS  CHARACTER           
                                                 INIT "orddtl_1,item_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "rtdet,ttrtdetSrch,orddtl,ttorddtlSrch,item,ttorddtlSrch"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "ttrtdetSrch,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsSearchReturnQuantity:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_ttrtdetSrch                  = QUERY qttrtdetSrch:HANDLE
       htt_ttrtdetSrch                   = TEMP-TABLE ttrtdetSrch:HANDLE
       hQry_ttrtdetSrch_BEFORE           = QUERY qttrtdetSrch_BEFORE:HANDLE
       ttrtdetSrch_src_Names             = 'srcttrtdetsh,Default'
       ttrtdetSrch_src_Hdls              =         STRING(DATA-SOURCE srcttrtdetsh:HANDLE)
                                           + ',' + STRING(DATA-SOURCE srcttrtdetsh:HANDLE)
       ttrtdetSrch_srcttrtdetsh_Map      =         'abs_num,rtdet_1.abs_num'
                                           + ',' + 'act_quantity,rtdet_1.act_quantity'
                                           + ',' + 'asn_flag,rtdet_1.asn_flag'
                                           + ',' + 'bo_count,rtdet_1.bo_count'
                                           + ',' + 'bo_quantity,rtdet_1.bo_quantity'
                                           + ',' + 'case_qty,rtdet_1.case_qty'
                                           + ',' + 'comments,rtdet_1.comments'
                                           + ',' + 'co_num,rtdet_1.co_num'
                                           + ',' + 'delivery,rtdet_1.delivery'
                                           + ',' + 'exp_quantity,rtdet_1.exp_quantity'
                                           + ',' + 'GUID,rtdet_1.GUID'
                                           + ',' + 'host_origin,rtdet_1.host_origin'
                                           + ',' + 'item_cost,rtdet_1.item_cost'
                                           + ',' + 'item_desc,rtdet_1.item_desc'
                                           + ',' + 'item_num,rtdet_1.item_num'
                                           + ',' + 'line_indicator,rtdet_1.line_indicator'
                                           + ',' + 'line_num,rtdet_1.line_num'
                                           + ',' + 'line_sequence,rtdet_1.line_sequence'
                                           + ',' + 'lot,rtdet_1.lot'
                                           + ',' + 'ordered_qty,rtdet_1.ordered_qty'
                                           + ',' + 'packer,rtdet_1.packer'
                                           + ',' + 'packing_list,rtdet_1.packing_list'
                                           + ',' + 'percent_fill,rtdet_1.percent_fill'
                                           + ',' + 'pool,rtdet_1.pool'
                                           + ',' + 'po_line,rtdet_1.po_line'
                                           + ',' + 'po_number,rtdet_1.po_number'
                                           + ',' + 'po_suffix,rtdet_1.po_suffix'
                                           + ',' + 'po_type,rtdet_1.po_type'
                                           + ',' + 'prod_desc,rtdet_1.prod_desc'
                                           + ',' + 'qty_unavail,rtdet_1.qty_unavail'
                                           + ',' + 'quantity,rtdet_1.quantity'
                                           + ',' + 'rd_po_type,rtdet_1.rd_po_type'
                                           + ',' + 'receiver_num,rtdet_1.receiver_num'
                                           + ',' + 'return_fl,rtdet_1.return_fl'
                                           + ',' + 'ret_line,rtdet_1.ret_line'
                                           + ',' + 'ret_line_sequence,rtdet_1.ret_line_sequence'
                                           + ',' + 'row_status,rtdet_1.row_status'
                                           + ',' + 'rtn_order,rtdet_1.rtn_order'
                                           + ',' + 'rtn_order_suffix,rtdet_1.rtn_order_suffix'
                                           + ',' + 'rt_id,rtdet_1.rt_id'
                                           + ',' + 'special_handling,rtdet_1.special_handling'
                                           + ',' + 'uom,rtdet_1.uom'
                                           + ',' + 'vendor_id,rtdet_1.vendor_id'
                                           + ',' + 'vend_item,rtdet_1.vend_item'
                                           + ',' + 'wh_num,rtdet_1.wh_num'
       srcttrtdetsh_hdl                  = DATA-SOURCE srcttrtdetsh:HANDLE
       srcttrtdetsh_qhdl                 = QUERY qSrcsrcttrtdetsh:HANDLE
       srcttorddtlsh_hdl                 = DATA-SOURCE srcttorddtlsh:HANDLE
       srcttorddtlsh_qhdl                = QUERY qSrcsrcttorddtlsh:HANDLE
       TopLevelTables                    = 'ttrtdetSrch'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/SearchARRetQty_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchReturnQuantity .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchReturnQuantity BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchReturnQuantity .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchReturnQuantity BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttrtdetSrch_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchReturnQuantity .
     RUN ttrtdetSrch_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchReturnQuantity BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttrtdetSrch_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchReturnQuantity .
     RUN ttrtdetSrch_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchReturnQuantity BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_ttrtdetSrch_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchReturnQuantity .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('ttrtdetSrch') NO-ERROR .
     RUN ttrtdetSrch_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchReturnQuantity BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttrtdetSrch_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchReturnQuantity .
     RUN AfterRowFill  IN THIS-PROCEDURE ('ttrtdetSrch') NO-ERROR .
     RUN ttrtdetSrch_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchReturnQuantity BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


