/**=================================================================**
* Y:\irms.net.1.3.1\irms.net\BE\Audit\Audit\Audit_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 02/26/14, 15:56 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qAuditLog FOR AuditLog SCROLLING . 
QUERY qAuditLog:QUERY-PREPARE("FOR EACH AuditLog").
QUERY qAuditLog:QUERY-OPEN.


DEF  QUERY qAuditLog_BEFORE FOR AuditLog_BEFORE SCROLLING . 
QUERY qAuditLog_BEFORE:QUERY-PREPARE("FOR EACH AuditLog_BEFORE").
QUERY qAuditLog_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "AuditLog" */
DEFINE BUFFER AuditLog_1 FOR irms.AuditLog.
DEFINE QUERY qSrcAuditLog
    FOR AuditLog_1
        SCROLLING.
DEFINE DATA-SOURCE AuditLog
    FOR QUERY qSrcAuditLog
        AuditLog_1 KEYS (trans_num)        .
DATA-SOURCE AuditLog:PREFER-DATASET = no.
DATA-SOURCE AuditLog:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.07.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_AuditLog                     AS  HANDLE              
    FIELD   hQry_AuditLog                    AS  HANDLE              
    FIELD   hQry_AuditLog_BEFORE             AS  HANDLE              
    FIELD   AuditLog_DataSourceHdl           AS  HANDLE              
    FIELD   AuditLog_BatchSize               AS  INTEGER             
                                                 INIT 50
    FIELD   AuditLog_Fill                    AS  LOGICAL             
                                                 INIT yes
    FIELD   AuditLog_CanRead                 AS  LOGICAL             
                                                 INIT yes
    FIELD   AuditLog_CanCreate               AS  LOGICAL             
                                                 INIT yes
    FIELD   AuditLog_CanUpdate               AS  LOGICAL             
                                                 INIT yes
    FIELD   AuditLog_CanDelete               AS  LOGICAL             
                                                 INIT yes
    FIELD   AuditLog_Src_Names               AS  CHARACTER           
                                                 INIT ""
    FIELD   AuditLog_Src_Hdls                AS  CHARACTER           
                                                 INIT ""
    FIELD   AuditLog_CurrentSource           AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   AuditLog_UniqueKey               AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   AuditLog_AuditLog_Map            AS  CHARACTER           
                                                 INIT ""
    FIELD   AuditLog_AuditLog_CF             AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   AuditLog_AuditLog_NoP            AS  CHARACTER           
                                                 INIT ""
    FIELD   AuditLog_hdl                     AS  HANDLE              
    FIELD   AuditLog_UseQuery                AS  LOGICAL             
                                                 INIT yes
    FIELD   AuditLog_PostTable               AS  CHARACTER           
                                                 INIT "AuditLog_1"
    FIELD   AuditLog_qhdl                    AS  HANDLE              
    FIELD   AuditLog_AuditLog_1_W            AS  CHARACTER           
                                                 INIT ""
    FIELD   AuditLog_AuditLog_1_S            AS  CHARACTER           
                                                 INIT ""
    FIELD   AuditLog_Buffs                   AS  CHARACTER           
                                                 INIT "AuditLog_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "AuditLog,AuditLog"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "AuditLog,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsAudit:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_AuditLog                     = QUERY qAuditLog:HANDLE
       htt_AuditLog                      = TEMP-TABLE AuditLog:HANDLE
       hQry_AuditLog_BEFORE              = QUERY qAuditLog_BEFORE:HANDLE
       AuditLog_src_Names                = 'AuditLog,Default'
       AuditLog_src_Hdls                 =         STRING(DATA-SOURCE AuditLog:HANDLE)
                                           + ',' + STRING(DATA-SOURCE AuditLog:HANDLE)
       AuditLog_AuditLog_Map             =         'abs_num,AuditLog_1.abs_num'
                                           + ',' + 'action_code,AuditLog_1.action_code'
                                           + ',' + 'adj_code,AuditLog_1.adj_code'
                                           + ',' + 'batch,AuditLog_1.batch'
                                           + ',' + 'bin_from,AuditLog_1.bin_from'
                                           + ',' + 'bin_num,AuditLog_1.bin_num'
                                           + ',' + 'bin_to,AuditLog_1.bin_to'
                                           + ',' + 'cancelled,AuditLog_1.cancelled'
                                           + ',' + 'cancelled_at,AuditLog_1.cancelled_at'
                                           + ',' + 'cancelled_by,AuditLog_1.cancelled_by'
                                           + ',' + 'cargo_control,AuditLog_1.cargo_control'
                                           + ',' + 'carton_id,AuditLog_1.carton_id'
                                           + ',' + 'case_qty,AuditLog_1.case_qty'
                                           + ',' + 'cc_string,AuditLog_1.cc_string'
                                           + ',' + 'cc_type,AuditLog_1.cc_type'
                                           + ',' + 'comments,AuditLog_1.comments'
                                           + ',' + 'co_num,AuditLog_1.co_num'
                                           + ',' + 'date_time,AuditLog_1.date_time'
                                           + ',' + 'dept_num,AuditLog_1.dept_num'
                                           + ',' + 'doc_id,AuditLog_1.doc_id'
                                           + ',' + 'emp_num,AuditLog_1.emp_num'
                                           + ',' + 'exp_abs,AuditLog_1.exp_abs'
                                           + ',' + 'extdCost,AuditLog_1.extdCost'
                                           + ',' + 'GUID,AuditLog_1.GUID'
                                           + ',' + 'ifaces_file,AuditLog_1.ifaces_file'
                                           + ',' + 'ifaces_time,AuditLog_1.ifaces_time'
                                           + ',' + 'item_num,AuditLog_1.item_num'
                                           + ',' + 'item_qty,AuditLog_1.item_qty'
                                           + ',' + 'item_type,AuditLog_1.item_type'
                                           + ',' + 'line_sequence,AuditLog_1.line_sequence'
                                           + ',' + 'lot,AuditLog_1.lot'
                                           + ',' + 'mach_type,AuditLog_1.mach_type'
                                           + ',' + 'msg_status,AuditLog_1.msg_status'
                                           + ',' + 'ns_comment,AuditLog_1.ns_comment'
                                           + ',' + 'oldextdCost,AuditLog_1.oldextdCost'
                                           + ',' + 'oldUnitCost,AuditLog_1.oldUnitCost'
                                           + ',' + 'old_stock_stat,AuditLog_1.old_stock_stat'
                                           + ',' + 'packer,AuditLog_1.packer'
                                           + ',' + 'pallet_id,AuditLog_1.pallet_id'
                                           + ',' + 'pallet_id_from,AuditLog_1.pallet_id_from'
                                           + ',' + 'pool,AuditLog_1.pool'
                                           + ',' + 'po_line,AuditLog_1.po_line'
                                           + ',' + 'po_number,AuditLog_1.po_number'
                                           + ',' + 'po_suffix,AuditLog_1.po_suffix'
                                           + ',' + 'proc_created,AuditLog_1.proc_created'
                                           + ',' + 'qa_release_id,AuditLog_1.qa_release_id'
                                           + ',' + 'record_type,AuditLog_1.record_type'
                                           + ',' + 'release_id,AuditLog_1.release_id'
                                           + ',' + 'result_code,AuditLog_1.result_code'
                                           + ',' + 'result_msg,AuditLog_1.result_msg'
                                           + ',' + 'row_status,AuditLog_1.row_status'
                                           + ',' + 'rt_num,AuditLog_1.rt_num'
                                           + ',' + 'serial_num,AuditLog_1.serial_num'
                                           + ',' + 'shf_num,AuditLog_1.shf_num'
                                           + ',' + 'stock_stat,AuditLog_1.stock_stat'
                                           + ',' + 'substat_code,AuditLog_1.substat_code'
                                           + ',' + 'sugg_qty,AuditLog_1.sugg_qty'
                                           + ',' + 'task_id,AuditLog_1.task_id'
                                           + ',' + 'transmission,AuditLog_1.transmission'
                                           + ',' + 'trans_link,AuditLog_1.trans_link'
                                           + ',' + 'trans_num,AuditLog_1.trans_num'
                                           + ',' + 'trans_sec_time,AuditLog_1.trans_sec_time'
                                           + ',' + 'trans_type,AuditLog_1.trans_type'
                                           + ',' + 'truck_id,AuditLog_1.truck_id'
                                           + ',' + 'UnitCost,AuditLog_1.UnitCost'
                                           + ',' + 'uom,AuditLog_1.uom'
                                           + ',' + 'void,AuditLog_1.void'
                                           + ',' + 'wh_num,AuditLog_1.wh_num'
       AuditLog_hdl                      = DATA-SOURCE AuditLog:HANDLE
       AuditLog_qhdl                     = QUERY qSrcAuditLog:HANDLE
       TopLevelTables                    = 'AuditLog'
       DataSetOneTransaction             = no
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/Audit_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAudit .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAudit BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAudit .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAudit BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AuditLog_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAudit .
     RUN AuditLog_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAudit BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AuditLog_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAudit .
     RUN AuditLog_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAudit BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_AuditLog_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAudit .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('AuditLog') NO-ERROR .
     RUN AuditLog_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAudit BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AuditLog_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAudit .
     RUN AfterRowFill  IN THIS-PROCEDURE ('AuditLog') NO-ERROR .
     RUN AuditLog_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAudit BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


