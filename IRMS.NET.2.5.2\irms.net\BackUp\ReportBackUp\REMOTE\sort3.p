/*
** glauber 04oct95
** sort3.p -- order 3 numbers, from smallest to largest
*/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/rf/RCS/sort3.p,v 1.1 1996/07/19 17:35:07 fwang Exp $~n"
.

define input-output parameter num1 as decimal no-undo .
define input-output parameter num2 as decimal no-undo .
define input-output parameter num3 as decimal no-undo .

define variable thefirst as decimal no-undo .
define variable thesecond as decimal no-undo .
define variable thethird as decimal no-undo .


if (num1 le num2)
then do:

    if (num2 le num3)
    then do:
        /* they are already in order */
        return .
    end .
    else do:
        if (num1 le num3)
        then do:
            assign 
                thefirst = num1
                thesecond = num3
                thethird = num2
            .
        end .
        else do:
            assign
                thefirst = num3
                thesecond = num1
                thethird = num2
            .
        end .
    end .

end .
else do: /* num1 gt num2 */

    if (num2 ge num3)
    then do:
        /* they are in reverseorder */
        assign
            thefirst = num3
            thesecond = num2
            thethird = num1
        .
    end .
    else do: /* num1 gt num2, num3 gt num2 */
        if (num1 ge num3)
        then do:
            assign 
                thefirst = num2
                thesecond = num3
                thethird = num1
            .
        end .
        else do:
            assign
                thefirst = num2
                thesecond = num1
                thethird = num3
            .
        end .
    end .

end .


assign
    num1 = thefirst
    num2 = thesecond
    num3 = thethird
.

return .
