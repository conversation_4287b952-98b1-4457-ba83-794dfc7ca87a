/**
*** <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tote labels for Intermec printer .
*** File name is hard coded in lbl_prnt.w .
**/


/* Version control Id */
DEFINE VARIABLE SCCS_ID AS CHARACTER
    NO-UNDO
    INITIAL "@(#) $Header: /pdb/9.01/remote/RCS/int_pllt.p,v 1.5 2000-02-24 11:43:38-06 tanya Exp $~n"
.

DEF INPUT PARAMETER ch_type    AS CHAR NO-UNDO .
DEF INPUT PARAMETER i_lbl_size AS INT  NO-UNDO .
DEF INPUT PARAMETER i_qty      AS INT  NO-UNDO .
DEF INPUT PARAMETER i_exact    AS INT  NO-UNDO .
DEF INPUT PARAMETER ch_printer AS CHAR NO-UNDO .


DEF VAR ch_file    AS CHAR NO-UNDO .
DEF VAR ch_command AS CHAR NO-UNDO .
DEF VAR ch_label   AS CHAR NO-UNDO .
DEF VAR ch_text    AS CHAR NO-UNDO .
DEF VAR i_count    AS INT  NO-UNDO .



/*
** Read parameters from the environment
*/

MESSAGE "Starting Printing..." .


/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .

OUTPUT TO VALUE ( ch_file ).    

/* Main block */
   CASE ch_type :
      WHEN 'P' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Pallet' .

      WHEN 'C' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Carton' .

      WHEN 'T' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Tote' .

      OTHERWISE
      DO:
        MESSAGE "Invalid type passed".
        return error .
      END.

   END CASE .

   IF i_exact > 0 THEN
   DO: /* Exact item - only print it... */
      ASSIGN
        ch_text = ch_type + STRING ( i_exact, "999999999" ) .

      RUN do_label ( ch_label , ch_text ) .
   END.
   ELSE
   DO: /* Print as many specified... */
      ASSIGN 
         i_count = 1 .

      printmanyloop:
      DO WHILE TRUE:
         IF ( i_count > i_qty ) THEN 
            LEAVE printmanyloop.
         
         CASE ch_type :
            WHEN 'P' THEN
               ASSIGN
                  ch_text = 'P' + STRING ( 
                         NEXT-VALUE ( print_pallet ) , '999999999' ) .

            WHEN 'C' THEN
               ASSIGN
                  ch_text = 'C' + STRING ( 
                         NEXT-VALUE ( cartonmst_carton_id ) , '999999999' ) .

            WHEN 'T' THEN
               ASSIGN
                  ch_text = 'T' + STRING (
                         NEXT-VALUE ( print_pallet ) , '999999999' ) .
         END CASE .

         RUN do_label ( ch_label , ch_text ) .
         
         i_count = i_count + 1 . 
      END.
   END.

/* Print Carton or Pallet Label to file in temp dir. */

OUTPUT CLOSE . 

if (opsys eq "UNIX")
then do:
    ASSIGN
    ch_command = "lp -c -d" + ch_printer + " " + ch_file .
    message ch_command .
    OS-COMMAND SILENT VALUE ( ch_command ) .

    OS-DELETE         VALUE ( ch_file    ) .
end .
else do: /*NT*/

   /************************ old code, sends file to spooler ****************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .
   ********************************************************************/
   

       message "copying file  " ch_file "to printer " ch_printer .

       OS-COPY VALUE( ch_file ) VALUE( ch_printer) .

       OS-DELETE  VALUE ( ch_file  ) .   
   
end .


message "Done!" .
return .

/* End Of Program */

                             
/************************ Do Label *************************/
PROCEDURE do_label:
   DEF INPUT PARAMETER ch_label    AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_text     AS CHAR NO-UNDO .

   DEF VAR ch_prnt_label AS CHAR NO-UNDO .
   DEF VAR ch_prnt_text  AS CHAR NO-UNDO .


   PUT UNFORMATTED

      "<STX><ESC>C<ETX>"
      "<STX><ESC>P;E3;F3;~<ETX>~n"  .

      IF i_lbl_size = 0 THEN    /* 4x6 Standard Label */
          PUT UNFORMATTED
             
             "<STX>L50;o0000,000;l1170;f0;w4;~<ETX>~n"
             "<STX>L51;o0000,796;l1170;f0;w4;~<ETX>~n"
             "<STX>L52;o0003,000;l0796;f3;w4;~<ETX>~n"
             "<STX>L53;o1170,000;l0796;f3:w4:~<ETX>~n"
             "<STX>L53;o1170,000;l0796;f3;w4;~<ETX>~n"
             "<STX>L54;o0000,250;l1170;f0;w4;~<ETX>~n"
          
             "<STX>H00;o0010,000;f0;h1;w1;c22;d0,10;~<ETX>~n"
             "<STX>H01;o0090,050;f0;h3;w3;c22;d0,10;~<ETX>~n"

             "<STX>B80;o110,325,c6,0;f0;h400;w5;i0;d0,10;p@;~<ETX>~n" .
           
      ELSE    /* 2x4 standard Label */
          PUT UNFORMATTED

             "<STX>L50;o003,000;l796;f3;w4;~<ETX>~n"
             "<STX>L51;o003,000;l350;f0;w4;~<ETX>~n"
             "<STX>L52;o353,000;l796;f3;w4;~<ETX>~n"
             "<STX>L53;o003,792;l350;f0;w4;~<ETX>~n"
             "<STX>L54;o150,000;l796;f3;w4;~<ETX>~n"

             "<STX>H00;o000,780;f1;h1;w1;c21;d0,10;~<ETX>~n"
             "<STX>H01;o015,725;f1;h2;w2;c22;d0,10;~<ETX>~n"

             "<STX>B70;o165,735;c6,0;f1;h170;w6;i0;d0,10;p@;~<ETX>~n" .

      

      PUT UNFORMATTED

         "<STX>R<ETX>~n"
         "<STX><ESC>E3<ETX>~n"
         "<STX><CAN><ETX>~n"
         "<STX>" + ch_label + " ID:" "<CR><ETX>~n"
         "<STX>" + ch_text + "<CR><ETX>~n"
         "<STX>" + ch_text + "<CR><ETX>~n"
         "<STX><ETB><ETX>" .


END PROCEDURE.
