/**
*** John Oct. 1, 1998 8:52 
*** file name --- pic_pom.p               
**/
/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/pic_pom.p,v 1.18 2001/03/23 19:26:58 jason Exp $~n"
.

    define input parameter ch_co             as character no-undo .
    define input parameter ch_wh             as character no-undo .
    define input parameter i_wave            as integer   no-undo .
    define input parameter ord_id            as integer   no-undo .
    define input parameter ch_unuse          as character no-undo .
    
    define stream out_to_file . 
/**
*** the current printer should be setup in printer master
*** with print_form = "PK", type = true (for report) .
**/
/**
*** Globals parameters
**/
define new Global shared variable apage as character no-undo .

define temp-table tt_print_image no-undo
    field printimage as character 
.

define temp-table tt_print_hdr no-undo 
    field count_hdr as integer 
    field tt_co_num as character
    field tt_wh_num as character
    field tt_wh_desc as character
    field tt_wave   as integer   
    field cust_id as character 
    field cust_name as character 
    field order as character 
    field sales_ord as character 
    field cust_po   as character 
    field ord_date  as date    
    field emp_num as character
    field sent_time as character
    field date_time as character
    field branch_id as character
    field terms     as character 
    field wh_addr as character 
    field ord_carrier as character 
    field wh_addr1 as character 
    field wh_city  as character 
    field wh_country as character
    field shp_name as character 
    field shp_addr as character 
    field shp_addr1 as character 
    field shp_city  as character 
    field shp_country as character
    field line_comm   as integer    
    field comm        as character extent 30 
.
    
define temp-table tt_print_dtl no-undo
    field count_hdr as integer 
    field line_num  as integer 
    field gift      as character format "X" 
    field count_dtl as integer label "Item" 
    field Item as character label "Part Number"
    field ch_desc as character label "Description"
    field d_cube as decimal label "Cube"
    field sn  as character  Label  "SN"
    field req_qty as decimal label  "Pick_Qty"
.
define temp-table tt_print_pic no-undo
    field count_hdr as integer 
    field count_dtl as integer
    field count_pic as integer
    field bin_num as character 
    field req_qty as decimal 
.

define variable printer_driver as handle .
define variable errproc as handle .

/**
*** buffer defination 
**/
define buffer bf_ordhdr for irms.ordhdr.
define buffer bf_whmst  for irms.whmst . 


/**
*** Loacl variable 
**/
define variable p_num as integer initial 1 no-undo .  /* page number */
define variable hdr_num as integer no-undo .
define variable dtl_num as integer no-undo .
define variable pic_num as integer no-undo .

/**
*** Load external programe
**/
    run lprintor.p PERSISTENT SET printer_driver.
/**
*** Main block 
**/
    /* check for the order */
        
    if ( ord_id EQ ? ) OR (ord_id EQ 0)
    then do:
        /* check the input */
        if ( (ch_co EQ "") OR (ch_co EQ ?) OR
            (ch_wh EQ "") OR (ch_wh EQ ?) OR
            (i_wave LE 0) )
        then do :
            message
                "The input error. Please check input" .
            return no-apply.
        end .
        for each bf_ordhdr no-lock 
            where 
                bf_ordhdr.co_num = ch_co and 
                bf_ordhdr.wh_num = ch_wh and 
                bf_ordhdr.batch  = i_wave
        :
            run cleanup .
            assign 
                ord_id = bf_ordhdr.id 
                ch_co = bf_ordhdr.co_num 
                ch_wh = bf_ordhdr.wh_num .
            /* check company and warehous */
            if not can-find (
                    bf_whmst
                        where
                            bf_whmst.co_num = ch_co and 
                            bf_whmst.wh_num = ch_wh
                        )
            
            then do:
                message "There is no such warehouse and company" .
                return no-apply .
            end .

            run reset .
            run universfont in printer_driver(8) .
            run gen_tmp_table (output hdr_num, output dtl_num, output pic_num) .
            run print_pom (input hdr_num, input dtl_num, pic_num)  .
            run reset .
        end .
        return .
    end .
    else do:
        find first bf_ordhdr no-lock 
            where 
                bf_ordhdr.id = ord_id 
            no-error .
    end .
    if not available (bf_ordhdr)
    then do:
        message
            "The order is not exist. " .     
        return no-apply.
    end .
    run cleanup .
    assign 
        ord_id = bf_ordhdr.id 
        ch_co = bf_ordhdr.co_num 
        ch_wh = bf_ordhdr.wh_num .
    /* check company and warehous */
    if not can-find (
            bf_whmst
                where
                    bf_whmst.co_num = ch_co and 
                    bf_whmst.wh_num = ch_wh
                )
    
    then do:
        message "There is no such warehouse and company" .
        return no-apply .
    end .

    /* Reset the printer */
    
    run reset .
    
    /* set the font for printer */
/*    
    run littlefont in printer_driver .
*/
    run universfont in printer_driver(8) .
    
    /*  generate the temp table */ 
    
    run gen_tmp_table (output hdr_num, output dtl_num, output pic_num) .

    /* beginning print */
    
    run print_pom (input hdr_num, input dtl_num, pic_num)  .
        
    /* finish the print Job */
    
    run reset .

/* end Job */


procedure reset:   /* reset the printer */
    
    assign 
        apage = ""
        apage = apage + "~EE~E&10L~E&148D~E&k0S~E&12A~E&l1E" .

end procedure .
procedure cleanup :
    for each tt_print_hdr
    :
        delete tt_print_hdr .
    end .
    for each tt_print_dtl
    :
        delete tt_print_dtl .
    end .
    for each tt_print_pic
    :
        delete tt_print_pic .
    end .
    for each tt_print_image
    :
        delete tt_print_image .
    end .

end procedure .
procedure gen_tmp_table:  /* generat the temp table for print */ 
    
define output parameter count_1 as integer initial 0 no-undo .
define output parameter count_2 as integer initial 0 no-undo .
define output parameter count_3 as integer initial 0 no-undo .

define buffer bf_ordhdr for irms.ordhdr.
define buffer bf_orddtl for irms.orddtl.
define buffer bf_whmst  for irms.whmst . 
define buffer bf_pick   for irms.pick .
define buffer bf_item   for irms.item .
define buffer bf_comm   for irms.comment .

def var stor-empnum like AuditLog.emp_num.
def var stor-datetime as char.
def var stor-branchid like ordhdr.branch_id.

    assign 
        count_1 = 0
        count_2 = 0
        count_3 = 0
    .

    for each bf_ordhdr no-lock 
        where 
            bf_ordhdr.id = ord_id 
    :
        find first bf_whmst no-lock 
            where 
                bf_whmst.co_num = ch_co and 
                bf_whmst.wh_num = ch_wh 
            no-error
        .
        if not available (bf_whmst)
        then do:
            message "Wrong warehouse." .
            return .
        end .
        assign count_1 = count_1 + 1 .  
               
        find last AuditLog no-lock
            where
                AuditLog.co_num = bf_whmst.co_num and
                AuditLog.wh_num = bf_whmst.wh_num and
                AuditLog.po_number = bf_ordhdr.order and
                AuditLog.trans_type = "OD" no-error.
                
        if not available AuditLog then
        do:
          assign
            stor-empnum = " "
            stor-datetime = " "
            stor-branchid = " ".
        end.  
        else
        do:
          assign
            stor-empnum = AuditLog.emp_num
            stor-datetime = substr(AuditLog.date_time,5,2,"char") + "/" +
                            substr(AuditLog.date_time,7,2,"char") + "/" +
                            substr(AuditLog.date_time,1,4,"char") + " " +
                            substr(AuditLog.date_time,9,4,"char") 
            stor-branchid = bf_ordhdr.branch_id.
        end.          
                                
        create tt_print_hdr no-error.
        assign
            tt_print_hdr.count_hdr   = count_1
            tt_print_hdr.tt_co_num   = ch_co 
            tt_print_hdr.tt_wh_num   = ch_wh
            tt_print_hdr.tt_wh_desc  = bf_whmst.wh_desc 
            tt_print_hdr.tt_wave     = bf_ordhdr.batch
            tt_print_hdr.cust_id     = bf_ordhdr.ship_cust_code
            tt_print_hdr.cust_name   = bf_ordhdr.ship_name
            tt_print_hdr.order       = bf_ordhdr.order + bf_ordhdr.order_suffix
/*            tt_print_hdr.sales_ord   = bf_ordhdr.custom_data[1] */
            tt_print_hdr.terms       = " " /*bf_ordhdr.custom_data[3]*/                                             /*using custom field for a time*/
            tt_print_hdr.ord_carrier = bf_ordhdr.carrier + " " +
                                       bf_ordhdr.service 
            tt_print_hdr.emp_num     = stor-empnum
/*            tt_print_hdr.sent_time   = bf_ordhdr.custom_data[3]*/
            tt_print_hdr.date_time   = stor-datetime
            tt_print_hdr.branch_id   = stor-branchid 
            tt_print_hdr.cust_po     = bf_ordhdr.customer_po
            tt_print_hdr.ord_date    = bf_ordhdr.order_date
            tt_print_hdr.wh_addr     = bf_whmst.addr1
            tt_print_hdr.wh_addr1    = bf_whmst.addr2
            tt_print_hdr.wh_city     = bf_whmst.city + ", " +
                                       bf_whmst.state + " " +
                                       bf_whmst.zip
            tt_print_hdr.wh_country  = bf_whmst.country
            tt_print_hdr.shp_name    = bf_ordhdr.ship_name 
            tt_print_hdr.shp_addr    = bf_ordhdr.ship_addr1
            tt_print_hdr.shp_addr1   = bf_ordhdr.ship_addr2
            tt_print_hdr.shp_city    = bf_ordhdr.ship_city + ", " +
                                       bf_ordhdr.ship_state + " " +
                                       bf_ordhdr.ship_zip
            tt_print_hdr.shp_country = bf_ordhdr.ship_country 
        .
        assign 
            tt_print_hdr.line_comm = 0 .
        for each bf_comm no-lock
            where
                bf_comm.type = "O" and
                bf_comm.id = bf_ordhdr.id
        :
            if tt_print_hdr.line_comm GT 29
            then do:
                message "Too many comments." .
                leave .                    
            end .
            assign
                /* generate the comment for testing */
                tt_print_hdr.line_comm   = tt_print_hdr.line_comm + 1  
                tt_print_hdr.comm[tt_print_hdr.line_comm]     = 
                        bf_comm.comment_text 
            .
        end .
        release tt_print_hdr no-error.
        /* generate the body table */
        find first bf_orddtl no-lock
            where 
                bf_orddtl.id = bf_ordhdr.id 
            no-error
        .
        if not available (bf_orddtl) 
        then do:
            message 
                "ERROR ! No order detail for the order.".
            return error .
        end .
        assign count_2 = 0 . 
        for each bf_orddtl /*of bf_ordhdr */  no-lock
            where 
                bf_orddtl.id     = bf_ordhdr.id AND
                bf_orddtl.co_num = bf_ordhdr.co_num AND 
                bf_orddtl.wh_num = bf_ordhdr.wh_num
            /* by bf_orddtl.gift_wrap  descending change to custom_data[2] **/  
/*            by bf_orddtl.custom_data[2] descending  */
            by bf_orddtl.line
        :       
            find first bf_item no-lock
                where 
                    bf_item.co_num = ch_co and 
                    bf_item.wh_num = ch_wh and 
                    bf_item.abs_num = bf_orddtl.abs_num
                no-error
            .
            if not available (bf_item)
            then do:
                message
                    "The item:" + bf_orddtl.abs_num + " doesn't exist.".
                return error .
            end .
            assign count_2 = count_2 + 1 .
            create tt_print_dtl no-error .
            assign
                tt_print_dtl.count_hdr = count_1
                tt_print_dtl.count_dtl = count_2
            /*    tt_print_dtl.gift      = if bf_orddtl.gift_wrap EQ true  */
/*                tt_print_dtl.gift   = if trim(bf_orddtl.custom_data[2]) EQ "Y" 
                                         then "X" 
                                         else "" */
                tt_print_dtl.line_num  = bf_orddtl.line
                tt_print_dtl.item      = bf_orddtl.abs_num 
                tt_print_dtl.ch_desc   = bf_item.item_desc
                tt_print_dtl.d_cube    = bf_item.cube
                tt_print_dtl.req_qty   = bf_orddtl.req_qty
            .
            if bf_item.serial_flag EQ YES 
            then  assign tt_print_dtl.sn = "Y" .
            else  assign tt_print_dtl.sn = "N" .
            
            release tt_print_dtl no-error . 
            
            assign count_3 = 0 . 
            for each bf_pick no-lock
                where 
                    bf_pick.co_num        = ch_co and 
                    bf_pick.wh_num        = ch_wh and 
                    bf_pick.order         = bf_ordhdr.order and 
                    bf_pick.order_suffix  = bf_ordhdr.order_suffix and 
                    bf_pick.abs_num       = bf_orddtl.abs_num and 
                    bf_pick.line          = bf_orddtl.line and 
                    bf_pick.line_sequence = bf_orddtl.line_sequence
                by bf_pick.bin_num
            :
                assign count_3 = count_3 + 1 .
                create tt_print_pic no-error .
                assign 
                    tt_print_pic.count_hdr = count_1
                    tt_print_pic.count_dtl = count_2
                    tt_print_pic.count_pic = count_3
                    tt_print_pic.bin_num   = bf_pick.bin_num
                    tt_print_pic.req_qty   = bf_pick.qty
                .
                release tt_print_pic no-error .
              end .
        end .          
    end .
end procedure .

procedure print_pom:    /* print the pomeroy */

define input parameter hdr_num as integer no-undo .
define input parameter dtl_num as integer no-undo .
define input parameter pic_num as integer no-undo .

define variable num_1 as integer initial 1 no-undo .
define variable num_2 as integer initial 1 no-undo .
define variable num_3 as integer initial 1 no-undo .

define variable row_num as decimal no-undo .
define variable page_num as integer initial 1 no-undo .

define buffer b_hdr for tt_print_hdr .
define buffer b_dtl for tt_print_dtl .
define buffer b_pic for tt_print_pic .
    
define variable xpos as decimal no-undo .

define variable ch_file as character no-undo .
define variable prn_command as character no-undo .
define variable ch_printer as character no-undo .

/*
    if (i_wave NE 0) AND (i_wave NE ?)
    then do:
        assign ch_file = session:temp-directory + 
                    string ( i_wave , "9999999999") + ".out" .   
    end .
    else do:
        assign ch_file = session:temp-directory + 
                    string ( ord_id , "9999999999") + ".out" .   
    end .
*/
    assign ch_file = session:temp-directory + 
                string ( ord_id , "9999999999") + ".out" .   
    output stream out_to_file to value(ch_file) .

/* loop for print */
    for each b_hdr no-lock
    :
        run prn_hdr (input b_hdr.count_hdr, input page_num, output row_num ) .
        create tt_print_image no-error.
        assign 
            tt_print_image.printimage = apage 
            apage = "" 
        .
        release tt_print_image no-error.
        assign 
            xpos = row_num .

        for each b_dtl no-lock
            where b_dtl.count_hdr = b_hdr.count_hdr
            by b_dtl.d_cube descending 
        :
            run prn_dtl (input b_dtl.count_hdr,
                         input b_dtl.count_dtl,
                         input xpos) .
            create tt_print_image no-error.
            assign
                tt_print_image.printimage = apage 
                apage = "" 
            .
            release tt_print_image no-error.
            if xpos >  10.0
            then do:
                /* put to file */
                create tt_print_image no-error.
                assign 
                    tt_print_image.printimage = apage + "~f"  
                    apage = "" 
                .
                release tt_print_image no-error.
                assign page_num = page_num + 1 .
                run prn_hdr (input b_dtl.count_hdr,
                             input page_num, output row_num ) .
                assign xpos = row_num .
            end .
            assign xpos = xpos + 0.20 .
            for each b_pic no-lock    
                where b_pic.count_hdr = b_dtl.count_hdr and 
                      b_pic.count_dtl = b_dtl.count_dtl
                    
            :
                if xpos >  10.0
                then do:
                    /* put to file */
                    create tt_print_image no-error.
                    assign 
                        tt_print_image.printimage = apage + "~f"  
                        apage = "" 
                    .
                    release tt_print_image no-error.
                    assign page_num = page_num + 1 .
                    run prn_hdr (input b_pic.count_hdr,
                                 input page_num, output row_num ) .
                    assign xpos = row_num .
                end .
                create tt_print_image no-error.
                assign 
                    tt_print_image.printimage = apage 
                    apage = "" 
                .
                release tt_print_image no-error.
                run prn_pic (input b_pic.count_hdr,
                             input b_pic.count_dtl,
                             input b_pic.count_pic,
                             input xpos) .
                assign xpos = xpos + 0.10.       
            end .
            assign xpos = xpos + 0.10 .
        end .
        if length(apage) GT 0
        then do:
            /* put to file */
            create tt_print_image no-error.
            assign 
                tt_print_image.printimage = apage + "~f"  
                apage = "" 
            .
            release tt_print_image no-error.
        end .
    end .
    run formfeed in printer_driver .

    for each tt_print_image 
    :
        put stream out_to_file unformatted 
            tt_print_image.printimage + "~n" .
    end .
    output stream out_to_file close .
    
    /* look a printer for pick printing */
    
    /***********************************************************************
     ***** Adding this code to allow user to select a printer vs ***********          ***** Alternating the Pick_Ticket printing--03-17-2000      ***********
     ***********************************************************************/
     
    RUN get_printer (output ch_printer) .
       
    IF ERROR-STATUS:ERROR 
    then do:
        message "The printer setup error" .    
        return .
    end .
    define var path_dir as character no-undo .
    define var move_command as character no-undo .
    define var zip_command as character no-undo .
    assign
        path_dir   = "/irms/wave/"  /* the directory of file to move */
        prn_command = "lp -c -d " + ch_printer + " " + ch_file 
        zip_command = "gzip -9 " + ch_file  
    .
    /*
    if (i_wave NE 0) AND (i_wave NE ?)
    then do:
        assign
            move_command = "mv " + ch_file + ".gz " +  path_dir     
                            + string ( i_wave , "9999999999") + ".out" + ".gz"    
        .
    end .
    else do:
        assign
            move_command = "mv " + ch_file + ".gz " +  path_dir     
                            + string ( ord_id , "9999999999") + ".out" + ".gz"    
        .
    end .
    */
        assign move_command = "mv " + ch_file + ".gz " +  path_dir     
                    + string ( ord_id , "9999999999") + ".out" + ".gz"
        . 
    /**** for different system ***/
    if opsys EQ "UNIX" 
    then do:
        OS-COMMAND silent  VALUE ( prn_command ) .     
    end .
    else do:
        define variable finaldir as character no-undo .

        assign
            finaldir = trim(os-getenv("IRMS_SPOOLER")) +
                                "/" + trim(ch_printer) .
        os-copy value(ch_file) value(finaldir) .   
    end .
    /*
    OS-COMMAND no-wait VALUE ( zip_command ) .     
    OS-COMMAND no-wait VALUE ( move_command ) .     
    */
end procedure .

procedure prn_hdr:      /* print the header */

define input parameter num as integer no-undo .
define input parameter page_num as integer no-undo .
define output parameter row_num as decimal no-undo .

define buffer b_prn_hdr for tt_print_hdr .

define variable ypos as decimal initial 0.0 no-undo .

define variable pos as decimal initial 0.0 no-undo .

    find first b_prn_hdr no-lock
        where
            b_prn_hdr.count_hdr = num
        no-error
    .
    if not available (b_prn_hdr)
    then do:
        message "No order for print.".
        return error .
    end .
   /* the title */ 
    assign ypos = 0.5 .

    if ch_unuse EQ "R"
    then do:
        run universfont in printer_driver(12) .
        assign apage = apage + "~E(s3B" .   
        run printat in printer_driver (6.00, 0.60, "* REPRINT *") .
        run universfont in printer_driver(8) .
    end .
    run printat in printer_driver (3.20, ypos,
                    string(trim(b_prn_hdr.tt_wh_desc) + "~n")) .
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (3.40, ypos, "Warehouse Pick Ticket~n") .
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (3.40, ypos, "===========~n") .
    
    assign ypos = ypos + 0.3 .
    /* second column */      

    run printat in printer_driver (2.80, ypos,
                    "Warehouse:") . 
    run printat in printer_driver (3.60, ypos,
                    string(trim(b_prn_hdr.tt_wh_num))).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (3.60, ypos,
                    string(trim(b_prn_hdr.wh_addr  ))).
    assign ypos = ypos + 0.1 .
    if length(trim(b_prn_hdr.wh_addr1)) NE 0
    then do:
        run printat in printer_driver (3.60, ypos ,
                        string(trim(b_prn_hdr.wh_addr1 ))).
        assign ypos = ypos + 0.10 . 
    end .
    run printat in printer_driver (3.60, ypos ,
                    string(trim(b_prn_hdr.wh_city  ))).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (3.60, ypos ,
                    string(trim(b_prn_hdr.wh_country))) .
    /* Third column */
    assign ypos = 1.00 .      
    run printat in printer_driver (6.00, ypos,
                    "Date:") .
    run printat in printer_driver (6.60, ypos,
                    string(string(b_prn_hdr.ord_date) + "~n")) . 
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (6.00, ypos,
                    "Cust Po:" ) . 
    run printat in printer_driver (6.60, ypos,
                    string(trim(b_prn_hdr.cust_po) + "~n") ) . 
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (6.00, ypos,
                    "Wave:"). 
    run printat in printer_driver (6.60, ypos,
                    string(string(b_prn_hdr.tt_wave)  + "~n")).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (6.00, ypos,
                    "Terms:") .
    run printat in printer_driver (6.60, ypos,
                    string(b_prn_hdr.terms    + "~n")).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (6.00, ypos,
                    "Ship Via:"). 
    run printat in printer_driver (6.60, ypos,
                    string(b_prn_hdr.ord_carrier + "~n" )).
    assign ypos = ypos + 0.1.
    run printat in printer_driver (6.00, ypos,
                    "A Time:").
    run printat in printer_driver (6.60, ypos,
                    string(b_prn_hdr.sent_time) + "~n").
    assign ypos = ypos + 0.1.                
    run printat in printer_driver (6.00, ypos,
                    "I Time:").                
    run printat in printer_driver (6.60, ypos,
                    string(b_prn_hdr.date_time + "~n")).
    assign ypos = ypos + 0.1.                
    run printat in printer_driver (6.00, ypos,
                    "Dropped:").
    run printat in printer_driver (6.60, ypos,
                    string(b_prn_hdr.emp_num + "~n")).
    assign ypos = ypos + 0.1.
    run printat in printer_driver (6.00, ypos,
                    "Branch:").
    run printat in printer_driver (6.60, ypos,
                    string(b_prn_hdr.branch_id + "~n")).                             /* first column */

    assign ypos = 1.0 . 
    run printat in printer_driver (0.35, ypos,
                    "Page:" ).
    run printat in printer_driver (1.10, ypos,
                    string(page_num) ).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (0.35, ypos,
                    "Customer ID:") .
    run printat in printer_driver (1.10, ypos,
                     string(b_prn_hdr.cust_id) ).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (0.35, ypos,
                    "Name:") .
    run printat in printer_driver (1.10, ypos,
                    string(trim(b_prn_hdr.shp_name ))).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (0.35, ypos,
                    "Sales Order:") .
    run printat in printer_driver (1.10, ypos,
                    string(trim(b_prn_hdr.sales_ord ))).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (0.35, ypos,
                    "Order Num:") .
    run printat in printer_driver (1.10, ypos,
                    string(trim(b_prn_hdr.order))).
    assign ypos = ypos + 0.05.
    
    run setbar in printer_driver (input 0.40, input 16.0) .

    run barat in printer_driver (1.10, ypos,
                    string(trim(b_prn_hdr.order))) .
    assign ypos = ypos + 0.5 .
    
    run printat in printer_driver (0.10, ypos,
        "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------~n") .

    /* shipping infomation */
    assign ypos =  ypos + 0.1 .
    run printat in printer_driver (0.35, ypos,
                    "Ship To :") .
    run printat in printer_driver (1.10, ypos,
                    string(trim(b_prn_hdr.shp_name) + "~n")).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (1.10, ypos,
                    string(trim(b_prn_hdr.shp_addr) + "~n")).
    assign ypos = ypos + 0.1 .
    if length(trim(b_prn_hdr.shp_addr1)) NE 0
    then do:
        run printat in printer_driver (1.10, ypos,
                        string(trim(b_prn_hdr.shp_addr1) + "~n")).
        assign ypos = ypos + 0.1 .
    end .
    run printat in printer_driver (1.10, ypos ,
                    string(trim(b_prn_hdr.shp_city ) + "~n")).
    assign ypos = ypos + 0.1 .
    run printat in printer_driver (1.10, ypos ,
                    string(trim(b_prn_hdr.shp_country) + "~n")).
    /* comments */
    assign ypos = ypos + 0.10.
    if b_prn_hdr.line_comm GT 0
    then do:
        run printat in printer_driver (0.35, ypos ,
                        "Order Remarks:~n") .
        define variable i as integer initial 1 no-undo .
        do while i LE b_prn_hdr.line_comm
        :
            assign ypos = ypos  +  0.10 .
            run printat in printer_driver (1.10, ypos,
                        string(trim(b_prn_hdr.comm[i]) + "~n")) .
            assign i = i + 1 .
        end .
    end .
    assign ypos = ypos  +  0.20 .
                    
    run printat in printer_driver (0.45, ypos ,
                    "Line") .
    run printat in printer_driver (1.25, ypos ,
                    "Part Number").
    run printat in printer_driver (2.50, ypos ,
                    "Description") .
    run printat in printer_driver (4.60, ypos ,
                    "Cube") .
    run printat in printer_driver (5.10, ypos ,
                    "SN" ) .  
    run printat in printer_driver (5.50, ypos ,
                    "Pick Qty" ).
    run printat in printer_driver (6.40, ypos,
                    "People" ).
    run printat in printer_driver (7.00, ypos ,
                    "Qty Picked~n" ). 
    assign ypos = ypos  +  0.10 .

    run printat in printer_driver (0.10, ypos ,
        "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------~n") .
    assign row_num = ypos + 0.20 .   /* may be chang */    

end procedure.

procedure prn_dtl:      /* print the body   */

define input parameter num  as integer no-undo .
define input parameter num1 as integer no-undo .
define input parameter xpos as decimal no-undo .

define buffer b_prn_dtl for tt_print_dtl .

define var lv-people as char no-undo.
 
    find first b_prn_dtl no-lock
        where
            b_prn_dtl.count_hdr = num and 
            b_prn_dtl.count_dtl = num1
    .
    if not available (b_prn_dtl)
    then do:
        return .
    end .

    find first item where item.co_num = ch_co
                      and item.wh_num = ch_wh
                      and item.abs_num = b_prn_dtl.item
                      no-lock no-error.

    if avail item then 
    do:
        if (item.weight * b_prn_dtl.req_qty) < 100 then 
            lv-people = "1".
        else 
        if (item.weight * b_prn_dtl.req_qty) < 200 then
            lv-people = "2".
        else
            lv-people = "Multi".
    end.
    else 
        lv-people = "N/A".

    run printat in printer_driver(0.35, xpos,
                    b_prn_dtl.gift ).
    run printat in printer_driver(0.50, xpos,
                    b_prn_dtl.line_num ).
    run printat in printer_driver(1.25, xpos,
                    b_prn_dtl.item).     
    run printat in printer_driver(2.25, xpos,
                    b_prn_dtl.ch_desc). 
    run printat in printer_driver(4.60, xpos,
                    b_prn_dtl.d_cube ).
    run printat in printer_driver(5.10, xpos,
                    b_prn_dtl.sn ).
    run printat in printer_driver(5.65, xpos,
                    b_prn_dtl.req_qty ).
    run printat in printer_driver(6.50, xpos,
                    lv-people).
    run printat in printer_driver(7.00, xpos,
                    "_________/EA ~n" ).    
end procedure.

procedure prn_pic:      /* print the pick   */

define input parameter num  as integer no-undo .
define input parameter num1 as integer no-undo .
define input parameter num2 as integer no-undo .
define input parameter xpos as decimal no-undo .

define buffer b_prn_pic for tt_print_pic .
   
    find first b_prn_pic no-lock
        where
            b_prn_pic.count_hdr = num and 
            b_prn_pic.count_dtl = num1 and 
            b_prn_pic.count_pic = num2
        no-error
    .
    if not available (b_prn_pic)
    then do:
        return .
    end .
    run printat in printer_driver(1.40, xpos,
                    string( b_prn_pic.bin_num + "~n" )).
/*
    run printat in printer_driver(5.65, xpos,
                    b_prn_pic.req_qty  ).
*/

end procedure .
procedure get_printer:      /* get a printer    */

define output parameter ch_printer as character .

define variable sdate as character no-undo .
define variable stime as character no-undo .

define buffer b_printmst for irms.printmst .

    for each b_printmst 
        where 
            b_printmst.co_num     = ch_co and 
            b_printmst.wh_num     = ch_wh and 
            b_printmst.type       = true  and 
            b_printmst.print_form = "PK" 
        exclusive-lock 
        by b_printmst.last_time_used
    :            
        if available (b_printmst)
        then do:
            assign sdate = string( today, "99/99/9999")
                stime = string( time , "HH:MM")
            .
            update 
                b_printmst.last_time_used = substring(sdate, 7, 4) +
                                            substring(sdate, 1, 2) +
                                            substring(sdate, 4, 2) +
                                            substring(stime, 1, 2) + 
                                            substring(stime, 4, 2)
            .
            assign 
                ch_printer = b_printmst.printer .
            return .                                
        end .
    end .
    return error .
end procedure .
