"ttTablePropsCREATE"
"ttTablePropsTblTableNamettBackdoor"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKey"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettBackdoor"
"ttFieldPropsFldNameco_desc"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelco_desc"
"ttFieldPropsFldColLabelco_desc"
"ttFieldPropsFldHelpEnter the co_desc"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettBackdoor"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the company number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettBackdoor"
"ttFieldPropsFldNamewh_desc"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelWarehouse Description"
"ttFieldPropsFldColLabelWarehouse Name"
"ttFieldPropsFldHelpEnter this warehouse's description."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettBackdoor"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNamesrcBackdoor"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTablewhmst_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcBackdoor"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.whmst"
"ttDataSourceJoinscBufNamewhmst_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcBackdoor"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.cmpmst"
"ttDataSourceJoinscBufNamecmpmst_1"
"ttDataSourceJoinscDBWherecmpmst_1.co_num = whmst_1.co_num"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameblp/backdoorlogin_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextyes"
"ttOptionsmakePrevyes"
"ttOptionsmakeLastyes"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionyes"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTablettBackdoor"
"ttAttachSourcecSrcNamesrcBackdoor"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldco_num"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettBackdoor"
"ttAttachDtlcSrcNamesrcBackdoor"
"ttAttachDtlMappedFieldsco_num,whmst_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettBackdoor"
"ttAttachDtlcSrcNamesrcBackdoor"
"ttAttachDtlMappedFieldswh_desc,whmst_1.wh_desc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettBackdoor"
"ttAttachDtlcSrcNamesrcBackdoor"
"ttAttachDtlMappedFieldswh_num,whmst_1.wh_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettBackdoor"
"ttAttachDtlcSrcNamesrcBackdoor"
"ttAttachDtlMappedFieldsco_desc,cmpmst_1.co_name"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"