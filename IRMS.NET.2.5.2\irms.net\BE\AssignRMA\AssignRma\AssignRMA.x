"ttTablePropsCREATE"
"ttTablePropsTblTableNamettordhdrr"
"ttTablePropsTblBatchSize1"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKeyGUID"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameactual_freight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>>9.99"
"ttFieldPropsFldSideLabelActual Freight"
"ttFieldPropsFldColLabelActual Freight"
"ttFieldPropsFldHelpActual freight charges."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameassigned"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelAssigned for Pick"
"ttFieldPropsFldColLabelAssigned for Pick"
"ttFieldPropsFldHelpThis order has been assigned for picking"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebatch"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelOrder Batch"
"ttFieldPropsFldColLabelOrder Batch"
"ttFieldPropsFldHelpDon't change this field."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_addr1"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelBilling Address1"
"ttFieldPropsFldColLabelBilling Address1"
"ttFieldPropsFldHelpEnter the Bill To"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_addr2"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelBilling Address2"
"ttFieldPropsFldColLabelBilling Address2"
"ttFieldPropsFldHelpEnter the Bill To"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_addr_ext1"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelBilling Add Ext1"
"ttFieldPropsFldColLabelBilling Add Ext1"
"ttFieldPropsFldHelpEnter the Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_addr_ext2"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelBilling Add Ext2"
"ttFieldPropsFldColLabelBilling Add Ext2"
"ttFieldPropsFldHelpEnter the Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_addr_ext3"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelBilling Add Ext3"
"ttFieldPropsFldColLabelBilling Add Ext3"
"ttFieldPropsFldHelpEnter the Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_city"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(20)"
"ttFieldPropsFldSideLabelBilling City"
"ttFieldPropsFldColLabelBilling City"
"ttFieldPropsFldHelpBill-to city."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_country"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitUSA"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelBilling Country"
"ttFieldPropsFldColLabelBilling Country"
"ttFieldPropsFldHelpCountry for the billing address."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_email"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelBilling Email"
"ttFieldPropsFldColLabelBilling Email"
"ttFieldPropsFldHelpEnter the Bill-To Email"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_name"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelBilling Name"
"ttFieldPropsFldColLabelBilling Name"
"ttFieldPropsFldHelpName of the person who gets the bad news."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_phone"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelBilling Phone Number"
"ttFieldPropsFldColLabelBilling Phone"
"ttFieldPropsFldHelpEnter this Billing phone number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_state"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(2)"
"ttFieldPropsFldSideLabelBilling State"
"ttFieldPropsFldColLabelBilling State"
"ttFieldPropsFldHelpBill-to state."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebill_zip"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelBilling ZIP"
"ttFieldPropsFldColLabelBilling ZIP"
"ttFieldPropsFldHelpBill-to ZIP."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebox_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(8)"
"ttFieldPropsFldSideLabelCarton Size"
"ttFieldPropsFldColLabelCarton Size"
"ttFieldPropsFldHelpEnter the Carton Size"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamebranch_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelBranch Id"
"ttFieldPropsFldColLabelBranch Id"
"ttFieldPropsFldHelpBranch Id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecancelled_by"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(6)"
"ttFieldPropsFldSideLabelCancelled By"
"ttFieldPropsFldColLabelCancelled By"
"ttFieldPropsFldHelpEnter the Cancelled By"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecancel_date_time"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat9999-99-99 99:99"
"ttFieldPropsFldSideLabelDate and Time"
"ttFieldPropsFldColLabelDate and Time"
"ttFieldPropsFldHelpEnter the Date and Time"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecancel_flag"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelCanceled?"
"ttFieldPropsFldColLabelCanceled?"
"ttFieldPropsFldHelpEnter the cancel_flag"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecard"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelCard"
"ttFieldPropsFldColLabelCard"
"ttFieldPropsFldHelpEnter the card"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecard_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(6)"
"ttFieldPropsFldSideLabelcard_type"
"ttFieldPropsFldColLabelCard Type"
"ttFieldPropsFldHelpEnter the card_type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecarrier"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelCarrier"
"ttFieldPropsFldColLabelCarrier"
"ttFieldPropsFldHelpEnter the carrier for this order."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecharges"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelTotal charges"
"ttFieldPropsFldColLabelTotal charges"
"ttFieldPropsFldHelpOrder total."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecharge_type"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelcharge type"
"ttFieldPropsFldColLabelCharge Type"
"ttFieldPropsFldHelpEnter the charge type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameclass"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitSO"
"ttFieldPropsFldFormatXX"
"ttFieldPropsFldSideLabelClass"
"ttFieldPropsFldColLabelClass"
"ttFieldPropsFldHelpOrder Class"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameclearance_code"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelClearance Code"
"ttFieldPropsFldColLabelClearance Code"
"ttFieldPropsFldHelpClearance Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameclearance_required"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelclearance required"
"ttFieldPropsFldColLabelClearance Required?"
"ttFieldPropsFldHelpEnter the clearance_required"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_addr1"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Address"
"ttFieldPropsFldColLabelCOD Address"
"ttFieldPropsFldHelpEnter the COD Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_addr2"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Address"
"ttFieldPropsFldColLabelCOD Address"
"ttFieldPropsFldHelpEnter the COD Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_addr3"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Address"
"ttFieldPropsFldColLabelCOD Address"
"ttFieldPropsFldHelpEnter the COD Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_addr4"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Address"
"ttFieldPropsFldColLabelCOD Address"
"ttFieldPropsFldHelpEnter the COD Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_addr5"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Address"
"ttFieldPropsFldColLabelCOD Address"
"ttFieldPropsFldHelpEnter the COD Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_amount"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelCOD amount"
"ttFieldPropsFldColLabelCOD amount"
"ttFieldPropsFldHelpAdditional COD charges per carton"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_charge"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitC"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelCharge COD to"
"ttFieldPropsFldColLabelCharge COD to"
"ttFieldPropsFldHelpCharge COD to Consignee or Shipper?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_city"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelCOD City"
"ttFieldPropsFldColLabelCOD City"
"ttFieldPropsFldHelpEnter the COD City"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_country"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelCOD Country"
"ttFieldPropsFldColLabelCOD Country"
"ttFieldPropsFldHelpEnter the COD Country"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_email"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Email"
"ttFieldPropsFldColLabelCOD Email"
"ttFieldPropsFldHelpEnter the COD Email"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_flag"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelCOD"
"ttFieldPropsFldColLabelCOD"
"ttFieldPropsFldHelpIs this a COD order?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_name"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelCOD Name"
"ttFieldPropsFldColLabelCOD Name"
"ttFieldPropsFldHelpEnter the COD Name"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_phone"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelCod Phone Number"
"ttFieldPropsFldColLabelCOD Phone Number"
"ttFieldPropsFldHelpEnter this Cod phone number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_state"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(2)"
"ttFieldPropsFldSideLabelCOD State"
"ttFieldPropsFldColLabelCOD State"
"ttFieldPropsFldHelpEnter the COD State"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecod_zip"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelCOD Postal Code"
"ttFieldPropsFldColLabelCOD Postal Code"
"ttFieldPropsFldHelpEnter the COD Postal Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecomment"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelcomment"
"ttFieldPropsFldColLabelComment"
"ttFieldPropsFldHelpEnter the comment"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameconsignee_attn"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(50)"
"ttFieldPropsFldSideLabelconsignee_attn"
"ttFieldPropsFldColLabelConsignee Attn"
"ttFieldPropsFldHelpEnter the consignee_attn"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the company number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecustomer_freight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>>9.99"
"ttFieldPropsFldSideLabelCustomer Freight"
"ttFieldPropsFldColLabelCustomer Freight"
"ttFieldPropsFldHelpWhat the customer paid for S/H."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecustomer_po"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(21)"
"ttFieldPropsFldSideLabelCustomer's PO #"
"ttFieldPropsFldColLabelCustomer's PO #"
"ttFieldPropsFldHelpCustomer's Purchase Order Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustomFields"
"ttFieldPropsFldColLabelCustomFields"
"ttFieldPropsFldHelpEnter the CustomFields"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecustom_selector"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelCustom Selector"
"ttFieldPropsFldColLabelCustom Selector"
"ttFieldPropsFldHelpEnter the Custom Selector"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamecust_code"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelCustomer Code"
"ttFieldPropsFldColLabelCustomer"
"ttFieldPropsFldHelpCustomer code (for billing)."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamedel_route"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(3)"
"ttFieldPropsFldSideLabelRoute Id"
"ttFieldPropsFldColLabelRoute Id"
"ttFieldPropsFldHelpThe id for this delivery route in IRMS."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamedept_num"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>>>9"
"ttFieldPropsFldSideLabelDepartment"
"ttFieldPropsFldColLabelDepartment"
"ttFieldPropsFldHelpDepartment Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamediscount"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>9.99%"
"ttFieldPropsFldSideLabelDiscount"
"ttFieldPropsFldColLabelDiscount"
"ttFieldPropsFldHelpDiscount Amount."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameDiscountAmt"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$->>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelDiscount Amt"
"ttFieldPropsFldColLabelDiscount Amt"
"ttFieldPropsFldHelpEnter the Discount Amt"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamedoc_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelDocument ID"
"ttFieldPropsFldColLabelDocument ID"
"ttFieldPropsFldHelpEnter the Document ID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamedrop_cube"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabeldrop_cube"
"ttFieldPropsFldColLabelDrop Cube"
"ttFieldPropsFldHelpEnter the drop_cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamedrop_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelDrop Type"
"ttFieldPropsFldColLabelDrop Type"
"ttFieldPropsFldHelpEnter the Drop Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamedrop_weight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelDrop Weight"
"ttFieldPropsFldColLabelDrop Weight"
"ttFieldPropsFldHelpWeight calculated at order drop time."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameexp_ship_date"
"ttFieldPropsFldDataTypedate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelShip Date"
"ttFieldPropsFldColLabelShip Date"
"ttFieldPropsFldHelpExpected Shipping Date, from host."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamefreight_terms"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelFreight Terms"
"ttFieldPropsFldColLabelFreight Terms"
"ttFieldPropsFldHelpEnter the Freight Terms"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamegift"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelGift"
"ttFieldPropsFldColLabelGift"
"ttFieldPropsFldHelpIs this order a gift?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamegift_wrap"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelGift Wrap"
"ttFieldPropsFldColLabelGift Wrap"
"ttFieldPropsFldHelpGift wrap whole order?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamegift_wrap_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(6)"
"ttFieldPropsFldSideLabelgift_wrap_type"
"ttFieldPropsFldColLabelGift Wrap Type"
"ttFieldPropsFldHelpEnter the gift_wrap_type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameguaranteed_del_time"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit23:59"
"ttFieldPropsFldFormat99:99"
"ttFieldPropsFldSideLabelGuaranteed Delivery Time"
"ttFieldPropsFldColLabelGuaranteed Delivery Time"
"ttFieldPropsFldHelpEnter the Guaranteed Delivery Time"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamehold_reason"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(20)"
"ttFieldPropsFldSideLabelHold Reason"
"ttFieldPropsFldColLabelHold Reason"
"ttFieldPropsFldHelpReason why this order is on hold"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamehost_batch"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelHost Wave"
"ttFieldPropsFldColLabelHost Wave"
"ttFieldPropsFldHelpHost-assigned wave id."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamehost_origin"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(25)"
"ttFieldPropsFldSideLabelHost Origin"
"ttFieldPropsFldColLabelHost Origin"
"ttFieldPropsFldHelpEnter the Host Origin"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamehost_selector"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelHost selector"
"ttFieldPropsFldColLabelHost selector"
"ttFieldPropsFldHelpEnter the Host selector"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamehost_sequence"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelHost Sequence"
"ttFieldPropsFldColLabelHost Sequence"
"ttFieldPropsFldHelpSequence of this order within the host-assigned wave."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameid"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>>>>9"
"ttFieldPropsFldSideLabelId"
"ttFieldPropsFldColLabelId"
"ttFieldPropsFldHelpDO NOT EDIT THIS FIELD."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameimage_name"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(20)"
"ttFieldPropsFldSideLabelimage_name"
"ttFieldPropsFldColLabelImage Name"
"ttFieldPropsFldHelpEnter the image_name"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameinternational"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelInternational"
"ttFieldPropsFldColLabelInternational"
"ttFieldPropsFldHelpInternational order."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamekit_build_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelKit Build Type"
"ttFieldPropsFldColLabelKit Build Type"
"ttFieldPropsFldHelpUsed for work orders."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameline_count"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelLine Count"
"ttFieldPropsFldColLabelLine Count"
"ttFieldPropsFldHelpNumber of lines in this order."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamelot"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelLot"
"ttFieldPropsFldColLabelLot"
"ttFieldPropsFldHelpLot Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamemax_days"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelMaximum Days"
"ttFieldPropsFldColLabelMaximum Days"
"ttFieldPropsFldHelpEnter the Maximum Days"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamemy_desc1"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelmy_desc1"
"ttFieldPropsFldColLabelmy_desc1"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamemy_desc2"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelmy_desc2"
"ttFieldPropsFldColLabelmy_desc2"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameno_detail"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelNo Details?"
"ttFieldPropsFldColLabelNo Details?"
"ttFieldPropsFldHelpEnter the CFno_detail"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamenum_cartons"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelNumber of Cartons"
"ttFieldPropsFldColLabelNumber of Cartons"
"ttFieldPropsFldHelpEnter the Number of Cartons"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorder"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelOrder"
"ttFieldPropsFldColLabelOrder"
"ttFieldPropsFldHelpPlease enter an order number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorder_date"
"ttFieldPropsFldDataTypedate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelOrder Date"
"ttFieldPropsFldColLabelOrder Date"
"ttFieldPropsFldHelpDate when order was received by IRMS."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorder_status"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitO"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelStatus"
"ttFieldPropsFldColLabelStatus"
"ttFieldPropsFldHelpEnter the status for this order."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorder_suffix"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelOrder Suffix"
"ttFieldPropsFldColLabelOrder Suffix"
"ttFieldPropsFldHelpEnter the Order Suffix"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorig_cube"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelorig_cube"
"ttFieldPropsFldColLabelOrig Cube"
"ttFieldPropsFldHelpEnter the orig_cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorig_order_date"
"ttFieldPropsFldDataTypedate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelOrder Date"
"ttFieldPropsFldColLabelOrder Date"
"ttFieldPropsFldHelpOrder date as received from the host."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameorig_weight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelOrig. Calc. Weight"
"ttFieldPropsFldColLabelOrig. Calc. Weight"
"ttFieldPropsFldHelpOriginal Calculated Weight"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamepartial"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelPartial Shipment"
"ttFieldPropsFldColLabelPartial Shipment"
"ttFieldPropsFldHelpPartial Shipment."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamepay_method"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelPay Method"
"ttFieldPropsFldColLabelPay Method"
"ttFieldPropsFldHelpPay method"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamepool"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelPool"
"ttFieldPropsFldColLabelPool"
"ttFieldPropsFldHelpEnter the Pool"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameprinted"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelPrinted"
"ttFieldPropsFldColLabelPrinted"
"ttFieldPropsFldHelpTrue if this order has been printed."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamepriority"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999"
"ttFieldPropsFldSideLabelPriority"
"ttFieldPropsFldColLabelPriority"
"ttFieldPropsFldHelpOrder Priority."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameproduct"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelFinished Product"
"ttFieldPropsFldColLabelFinished Product"
"ttFieldPropsFldHelpThe finished product for this work-order"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameproduct_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>,>>9.99"
"ttFieldPropsFldSideLabelProduction Quantity"
"ttFieldPropsFldColLabelProduction Quantity"
"ttFieldPropsFldHelpQuantity to be produced by this work-order"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamepro_number"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelPro Number"
"ttFieldPropsFldColLabelPro Number"
"ttFieldPropsFldHelpEnter the Pro Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamerate_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelRate Type"
"ttFieldPropsFldColLabelRate Type"
"ttFieldPropsFldHelpRate type."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamerma_num"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelRMA Number"
"ttFieldPropsFldColLabelRMA Number"
"ttFieldPropsFldHelpEnter the rma_num"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamerow_status"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelActive"
"ttFieldPropsFldColLabelActive"
"ttFieldPropsFldHelpOrder active?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameservice"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelService"
"ttFieldPropsFldColLabelService"
"ttFieldPropsFldHelpService."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_addr1"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelShipping Address1"
"ttFieldPropsFldColLabelShipping Address1"
"ttFieldPropsFldHelpEnter the Ship To"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_addr2"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelShipping Address2"
"ttFieldPropsFldColLabelShipping Address2"
"ttFieldPropsFldHelpEnter the Ship To"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_addr_ext1"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelShipping Add Ext1"
"ttFieldPropsFldColLabelShipping Add Ext1"
"ttFieldPropsFldHelpEnter the Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_addr_ext2"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelShipping Add Ext2"
"ttFieldPropsFldColLabelShipping Add Ext2"
"ttFieldPropsFldHelpEnter the Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_addr_ext3"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelShipping Add Ext3"
"ttFieldPropsFldColLabelShipping Add Ext3"
"ttFieldPropsFldHelpEnter the Address"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_city"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(20)"
"ttFieldPropsFldSideLabelShipping City"
"ttFieldPropsFldColLabelShipping City"
"ttFieldPropsFldHelpShip-To city."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_country"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitUSA"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelShipping Country"
"ttFieldPropsFldColLabelShipping Country"
"ttFieldPropsFldHelpCountry for the shipping address."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_cube"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelShip Cube"
"ttFieldPropsFldColLabelShip Cube"
"ttFieldPropsFldHelpEnter the ship_cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_cust_code"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelShipping Customer"
"ttFieldPropsFldColLabelShipping Customer"
"ttFieldPropsFldHelpCustomer code (for shipping)."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_date"
"ttFieldPropsFldDataTypedate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelShip Date"
"ttFieldPropsFldColLabelShip Date"
"ttFieldPropsFldHelpDate when order shipped."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_email"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelShipping Email"
"ttFieldPropsFldColLabelShipping Email"
"ttFieldPropsFldHelpEnter the Ship-To Email"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_msg"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelShipping Messages"
"ttFieldPropsFldColLabelShipping Messages"
"ttFieldPropsFldHelpEnter the Shipping Messages"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_name"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelShipping Name"
"ttFieldPropsFldColLabelShipping Name"
"ttFieldPropsFldHelpPerson who gets the toy."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_phone"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelShipping Phone"
"ttFieldPropsFldColLabelShipping Phone"
"ttFieldPropsFldHelpEnter this Shipping phone number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_state"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(2)"
"ttFieldPropsFldSideLabelShipping State"
"ttFieldPropsFldColLabelShipping State"
"ttFieldPropsFldHelpShip-To state."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_weight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelShip Weight"
"ttFieldPropsFldColLabelShip Weight"
"ttFieldPropsFldHelpWeight calculated or input at ship time."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameship_zip"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelShipping ZIP"
"ttFieldPropsFldColLabelShipping ZIP"
"ttFieldPropsFldHelpShip-to ZIP."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNameshp_by_irms"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInityes"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelShipped By IRMS"
"ttFieldPropsFldColLabelShipped By IRMS"
"ttFieldPropsFldHelpOrder was shipped by IRMS"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamestore"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelStore"
"ttFieldPropsFldColLabelStore"
"ttFieldPropsFldHelpEnter the store"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNametax"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>,>>9.99"
"ttFieldPropsFldSideLabelTax"
"ttFieldPropsFldColLabelTax"
"ttFieldPropsFldHelpOrder tax."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamethe_rt"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat"
"ttFieldPropsFldSideLabelRT Number"
"ttFieldPropsFldColLabelRT Number"
"ttFieldPropsFldHelpEnter the the_rt"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNametype"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitR"
"ttFieldPropsFldFormatXX"
"ttFieldPropsFldSideLabelOrder Type"
"ttFieldPropsFldColLabelOrder Type"
"ttFieldPropsFldHelpEnter the type for this order."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettordhdrr"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNamesrcordhdrs"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTableordhdr_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcordhdrs"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.ordhdr"
"ttDataSourceJoinscBufNameordhdr_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsid"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameY:\BE_Area\src\blp\AssignRMA_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstno"
"ttOptionsmakeNextno"
"ttOptionsmakePrevno"
"ttOptionsmakeLastno"
"ttOptionsmakepostno"
"ttOptionsmakeLoadyes"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionyes"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTablettordhdrr"
"ttAttachSourcecSrcNamesrcordhdrs"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsactual_freight,ordhdr_1.actual_freight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsassigned,ordhdr_1.assigned"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbatch,ordhdr_1.batch"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_addr1,ordhdr_1.bill_addr1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_addr2,ordhdr_1.bill_addr2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_addr_ext1,ordhdr_1.bill_addr_ext1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_addr_ext2,ordhdr_1.bill_addr_ext2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_addr_ext3,ordhdr_1.bill_addr_ext3"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_city,ordhdr_1.bill_city"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_country,ordhdr_1.bill_country"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_email,ordhdr_1.bill_email"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_name,ordhdr_1.bill_name"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_phone,ordhdr_1.bill_phone"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_state,ordhdr_1.bill_state"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbill_zip,ordhdr_1.bill_zip"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbox_id,ordhdr_1.box_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsbranch_id,ordhdr_1.branch_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscancelled_by,ordhdr_1.cancelled_by"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscancel_date_time,ordhdr_1.cancel_date_time"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscancel_flag,ordhdr_1.cancel_flag"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscard,ordhdr_1.card"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscard_type,ordhdr_1.card_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscarrier,ordhdr_1.carrier"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscharges,ordhdr_1.charges"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscharge_type,ordhdr_1.charge_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsclass,ordhdr_1.class"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsclearance_code,ordhdr_1.clearance_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsclearance_required,ordhdr_1.clearance_required"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_addr1,ordhdr_1.cod_addr1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_addr2,ordhdr_1.cod_addr2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_addr3,ordhdr_1.cod_addr3"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_addr4,ordhdr_1.cod_addr4"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_addr5,ordhdr_1.cod_addr5"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_amount,ordhdr_1.cod_amount"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_charge,ordhdr_1.cod_charge"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_city,ordhdr_1.cod_city"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_country,ordhdr_1.cod_country"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_email,ordhdr_1.cod_email"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_flag,ordhdr_1.cod_flag"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_name,ordhdr_1.cod_name"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_phone,ordhdr_1.cod_phone"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_state,ordhdr_1.cod_state"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscod_zip,ordhdr_1.cod_zip"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscomment,ordhdr_1.comment"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsconsignee_attn,ordhdr_1.consignee_attn"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsco_num,ordhdr_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscustomer_freight,ordhdr_1.customer_freight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscustomer_po,ordhdr_1.customer_po"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscustom_selector,ordhdr_1.custom_selector"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldscust_code,ordhdr_1.cust_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdel_route,ordhdr_1.del_route"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdept_num,ordhdr_1.dept_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdiscount,ordhdr_1.discount"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsDiscountAmt,ordhdr_1.DiscountAmt"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdoc_id,ordhdr_1.doc_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdrop_cube,ordhdr_1.drop_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdrop_type,ordhdr_1.drop_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsdrop_weight,ordhdr_1.drop_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsexp_ship_date,ordhdr_1.exp_ship_date"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsfreight_terms,ordhdr_1.freight_terms"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsgift,ordhdr_1.gift"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsgift_wrap,ordhdr_1.gift_wrap"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsgift_wrap_type,ordhdr_1.gift_wrap_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsguaranteed_del_time,ordhdr_1.guaranteed_del_time"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsGUID,ordhdr_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldshold_reason,ordhdr_1.hold_reason"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldshost_batch,ordhdr_1.host_batch"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldshost_origin,ordhdr_1.host_origin"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldshost_selector,ordhdr_1.host_selector"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldshost_sequence,ordhdr_1.host_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsid,ordhdr_1.id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsimage_name,ordhdr_1.image_name"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsinternational,ordhdr_1.international"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldskit_build_type,ordhdr_1.kit_build_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsline_count,ordhdr_1.line_count"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldslot,ordhdr_1.lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsmax_days,ordhdr_1.max_days"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsmy_desc1,ordhdr_1.my_desc1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsmy_desc2,ordhdr_1.my_desc2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsnum_cartons,ordhdr_1.num_cartons"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorder,ordhdr_1.order"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorder_date,ordhdr_1.order_date"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorder_status,ordhdr_1.order_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorder_suffix,ordhdr_1.order_suffix"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorig_cube,ordhdr_1.orig_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorig_order_date,ordhdr_1.orig_order_date"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsorig_weight,ordhdr_1.orig_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldspartial,ordhdr_1.partial"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldspay_method,ordhdr_1.pay_method"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldspool,ordhdr_1.pool"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsprinted,ordhdr_1.printed"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldspriority,ordhdr_1.priority"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsproduct,ordhdr_1.product"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsproduct_qty,ordhdr_1.product_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldspro_number,ordhdr_1.pro_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsrate_type,ordhdr_1.rate_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsrow_status,ordhdr_1.row_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsservice,ordhdr_1.service"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_addr1,ordhdr_1.ship_addr1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_addr2,ordhdr_1.ship_addr2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_addr_ext1,ordhdr_1.ship_addr_ext1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_addr_ext2,ordhdr_1.ship_addr_ext2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_addr_ext3,ordhdr_1.ship_addr_ext3"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_city,ordhdr_1.ship_city"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_country,ordhdr_1.ship_country"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_cube,ordhdr_1.ship_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_cust_code,ordhdr_1.ship_cust_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_date,ordhdr_1.ship_date"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_email,ordhdr_1.ship_email"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_msg,ordhdr_1.ship_msg"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_name,ordhdr_1.ship_name"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_phone,ordhdr_1.ship_phone"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_state,ordhdr_1.ship_state"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_weight,ordhdr_1.ship_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsship_zip,ordhdr_1.ship_zip"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsshp_by_irms,ordhdr_1.shp_by_irms"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldsstore,ordhdr_1.store"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldstax,ordhdr_1.tax"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldstype,ordhdr_1.type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettordhdrr"
"ttAttachDtlcSrcNamesrcordhdrs"
"ttAttachDtlMappedFieldswh_num,ordhdr_1.wh_num"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"