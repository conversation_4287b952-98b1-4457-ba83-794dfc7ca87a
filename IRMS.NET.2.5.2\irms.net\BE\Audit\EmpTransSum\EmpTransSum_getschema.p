/**=================================================================**
* S:\IRMS.NET.2.5.2\irms.net\BE\Audit\EmpTransSum\EmpTransSum_getschema.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 09/10/15, 13:41 PM
**=================================================================**/


/* Business Entity Definintions */
{Audit/EmpTransSum/EmpTransSum_ds.i}
{Audit/EmpTransSum/EmpTransSum_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Schema.


    RUN Schema_Fill .


/**************************** END OF FILE ****************************/


