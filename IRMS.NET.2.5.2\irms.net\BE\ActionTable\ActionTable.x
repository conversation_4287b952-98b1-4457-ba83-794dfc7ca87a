"ttTablePropsCREATE"
"ttTablePropsTblTableNamettAction"
"ttTablePropsTblBatchSize30"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKey"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettAction"
"ttFieldPropsFldNameActionName"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelActionName"
"ttFieldPropsFldColLabelActionName"
"ttFieldPropsFldHelpEnter the ActionName"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettAction"
"ttFieldPropsFldNameActionValue"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelAction Value"
"ttFieldPropsFldColLabelAction Value"
"ttFieldPropsFldHelpEnter the Action Value"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettAction"
"ttFieldPropsFldNameFormName"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(100)"
"ttFieldPropsFldSideLabelFormName"
"ttFieldPropsFldColLabelFormName"
"ttFieldPropsFldHelpEnter the FormName"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettAction"
"ttFieldPropsFldNameScreenName"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelScreenName"
"ttFieldPropsFldColLabelScreenName"
"ttFieldPropsFldHelpEnter the ScreenName"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNamey:\BE_Area\src\blp\ActionTable_blp.p"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameS:\IRMS.NET.2.6.0\irms.net\Custom\SNS_Base\BLP\ActionTableCst_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyyes"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextno"
"ttOptionsmakePrevno"
"ttOptionsmakeLastno"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionyes"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnotecustom Actions based on system parameters are kept here. Front end will disable/enable or change labels etc based on these system values."
"ttNotesCREATE"
"ttNotesseq1"
"ttNotesnote"