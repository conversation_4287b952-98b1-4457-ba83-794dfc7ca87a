/**=================================================================**
* Y:\BE_Area\src\be\Audit\EmpTransSum\EmpTransSum_first.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:41 PM
**=================================================================**/


/* Business Entity Definintions */
{Audit/EmpTransSum/EmpTransSum_ds.i}
{Audit/EmpTransSum/EmpTransSum_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF       OUTPUT PARAM DATASET FOR dsEmpTransSum .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN 
        CREATE ds_Control.
    ASSIGN ds_Control.PropName = 'COMMAND'
           ds_Control.PropValue = 'PREV'.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


