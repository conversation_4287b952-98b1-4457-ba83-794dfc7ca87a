"ttTablePropsCREATE"
"ttTablePropsTblTableNameAuditLog"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKeyGUID"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameabs_num"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelAbsolute Item Number"
"ttFieldPropsFldColLabelItem Number"
"ttFieldPropsFldHelpEnter the Absolute Item Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameaction_code"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(8)"
"ttFieldPropsFldSideLabelAction Code"
"ttFieldPropsFldColLabelAction Code"
"ttFieldPropsFldHelpEnter the Action Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameadj_code"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(6)"
"ttFieldPropsFldSideLabelAdjustment Code"
"ttFieldPropsFldColLabelAdj code"
"ttFieldPropsFldHelpEnter the Adjustment Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamebatch"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelWave"
"ttFieldPropsFldColLabelWave"
"ttFieldPropsFldHelpEnter the Wave"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamebin_from"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelFrom Location"
"ttFieldPropsFldColLabelFrom Location"
"ttFieldPropsFldHelpEnter the From Location"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamebin_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelLocation"
"ttFieldPropsFldColLabelLocation"
"ttFieldPropsFldHelpEnter the Location"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamebin_to"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelTo Location"
"ttFieldPropsFldColLabelTo Location"
"ttFieldPropsFldHelpEnter the To Location"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecancelled"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelCancelled"
"ttFieldPropsFldColLabelCancelled"
"ttFieldPropsFldHelpEnter the Cancelled"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecancelled_at"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat9999-99-99 99:99"
"ttFieldPropsFldSideLabelCancelled At"
"ttFieldPropsFldColLabelCancelled At"
"ttFieldPropsFldHelpEnter the Cancelled At"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecancelled_by"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(6)"
"ttFieldPropsFldSideLabelCancelled By"
"ttFieldPropsFldColLabelCancelled By"
"ttFieldPropsFldHelpEnter the Cancelled By"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecargo_control"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelCargo Control"
"ttFieldPropsFldColLabelCargo Control"
"ttFieldPropsFldHelpEnter the Cargo Control"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecarton_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(17)"
"ttFieldPropsFldSideLabelCarton ID"
"ttFieldPropsFldColLabelCarton ID"
"ttFieldPropsFldHelpEnter the Carton ID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecase_qty"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>,>>9"
"ttFieldPropsFldSideLabelCase Quantity"
"ttFieldPropsFldColLabelCase Quantity"
"ttFieldPropsFldHelpEnter the Case Quantity"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecc_string"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelLevel of Cycle Count"
"ttFieldPropsFldColLabelLevel of Cycle Count"
"ttFieldPropsFldHelpEnter the Level of Cycle Count"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecc_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(8)"
"ttFieldPropsFldSideLabelType of Cycle Count"
"ttFieldPropsFldColLabelType of Cycle Count"
"ttFieldPropsFldHelpEnter the Type of Cycle Count"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechbinfrom"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelCF-From"
"ttFieldPropsFldColLabelFrom Location"
"ttFieldPropsFldHelpEnter the CF-From"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechbinnum"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelCF-Location"
"ttFieldPropsFldColLabelLocation"
"ttFieldPropsFldHelpEnter the CF-Location"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechbinto"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelCF-To"
"ttFieldPropsFldColLabelTo Location"
"ttFieldPropsFldHelpEnter the CF-To"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechorder"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(15)"
"ttFieldPropsFldSideLabelCF-PO/Order"
"ttFieldPropsFldColLabelPO/Order"
"ttFieldPropsFldHelpEnter the CF-PO/Order"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechordersuffix"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(15)"
"ttFieldPropsFldSideLabelCF-Suf"
"ttFieldPropsFldColLabelSuf"
"ttFieldPropsFldHelpEnter the CF-Suf"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechpalletid"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(17)"
"ttFieldPropsFldSideLabelCF-To Pallet"
"ttFieldPropsFldColLabelTo Pallet"
"ttFieldPropsFldHelpEnter the CF-To Pallet"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechpalletidfrom"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(17)"
"ttFieldPropsFldSideLabelCF-From Pallet"
"ttFieldPropsFldColLabelFrom Pallet"
"ttFieldPropsFldHelpEnter the CF-From Pallet"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamechtransname"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(25)"
"ttFieldPropsFldSideLabelCF-Transaction Name"
"ttFieldPropsFldColLabelTransaction Type"
"ttFieldPropsFldHelpEnter the CF-Transaction Name"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecomments"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelcomments"
"ttFieldPropsFldColLabelComments"
"ttFieldPropsFldHelpEnter the comments"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the Company"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamecustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatyes/No"
"ttFieldPropsFldSideLabelcustomFields"
"ttFieldPropsFldColLabelcustomFields"
"ttFieldPropsFldHelpEnter the customFields"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamedactqty"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->,>>>,>>9.99"
"ttFieldPropsFldSideLabelCF-Act. Qty"
"ttFieldPropsFldColLabelAct. Qty"
"ttFieldPropsFldHelpEnter the CF-Act. Qty"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamedate_time"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat9999-99-99 99:99"
"ttFieldPropsFldSideLabelDate and Time"
"ttFieldPropsFldColLabelDateTime"
"ttFieldPropsFldHelpEnter the Date and Time"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamedept_num"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>>>9"
"ttFieldPropsFldSideLabelDepartment Number"
"ttFieldPropsFldColLabelDepartment Number"
"ttFieldPropsFldHelpEnter the Department Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamedexpqty"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->,>>>,>>9.99"
"ttFieldPropsFldSideLabelCF-Exp. Qty"
"ttFieldPropsFldColLabelExp. Qty"
"ttFieldPropsFldHelpEnter the CF-Exp. Qty"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamedoc_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelDocument ID"
"ttFieldPropsFldColLabelDocument ID"
"ttFieldPropsFldHelpEnter the Document ID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamed_act_qty"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>>,>>9.99"
"ttFieldPropsFldSideLabelCF-Act Qty"
"ttFieldPropsFldColLabelAct Qty"
"ttFieldPropsFldHelpEnter the CF-Act Qty"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamed_exp_qty"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>>,>>9.99"
"ttFieldPropsFldSideLabelCF-Exp Qty"
"ttFieldPropsFldColLabelExp Qty"
"ttFieldPropsFldHelpEnter the CF-Exp Qty"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameemp_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelEmployee Number"
"ttFieldPropsFldColLabelEmployee Number"
"ttFieldPropsFldHelpEnter the Employee Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameexp_abs"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelExpected Item"
"ttFieldPropsFldColLabelExpected Item"
"ttFieldPropsFldHelpEnter the Expected Item"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameextdCost"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$>>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelExtended Cost"
"ttFieldPropsFldColLabelExtended Cost"
"ttFieldPropsFldHelpEnter the Extended Cost"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameifaces_file"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(20)"
"ttFieldPropsFldSideLabelIfaces File"
"ttFieldPropsFldColLabelIfaces File"
"ttFieldPropsFldHelpEnter the Ifaces File"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameifaces_time"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat9999-99-99 99:99"
"ttFieldPropsFldSideLabelifaces_time"
"ttFieldPropsFldColLabelIfaces Time"
"ttFieldPropsFldHelpEnter the ifaces_time"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameitem_desc"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelItem Description"
"ttFieldPropsFldColLabelItem Description"
"ttFieldPropsFldHelpEnter the Item Description"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameitem_num"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelCatalog Number"
"ttFieldPropsFldColLabelItem Num"
"ttFieldPropsFldHelpEnter the Catalog Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameitem_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>>,>>9.99"
"ttFieldPropsFldSideLabelQuantity"
"ttFieldPropsFldColLabelQuantity"
"ttFieldPropsFldHelpEnter the Quantity"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameitem_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitS"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelItem Type"
"ttFieldPropsFldColLabelItem Type"
"ttFieldPropsFldHelpEnter the Item Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameline_sequence"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelline_sequence"
"ttFieldPropsFldColLabelLine Seq."
"ttFieldPropsFldHelpEnter the line_sequence"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamelot"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelLot"
"ttFieldPropsFldColLabelLot"
"ttFieldPropsFldHelpEnter the Lot"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamemach_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelMachine Type"
"ttFieldPropsFldColLabelMachine Type"
"ttFieldPropsFldHelpEnter the Machine Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamemsg_status"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx"
"ttFieldPropsFldSideLabelmsg_status"
"ttFieldPropsFldColLabelMsg Status"
"ttFieldPropsFldHelpEnter the msg_status"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameNet"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>>,>>9.99"
"ttFieldPropsFldSideLabelCF-Net"
"ttFieldPropsFldColLabelCF-Net"
"ttFieldPropsFldHelpEnter the CF-Net"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamens_comment"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelNon-Stock Comment"
"ttFieldPropsFldColLabelNon-Stock Comment"
"ttFieldPropsFldHelpEnter the Non-Stock Comment"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameoldextdCost"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelOld Ex Cost"
"ttFieldPropsFldColLabelOld Ex Cost"
"ttFieldPropsFldHelpEnter the Old Ex Cost"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameoldUnitCost"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelPrevious Unit Cost"
"ttFieldPropsFldColLabelPrev Unit Cost"
"ttFieldPropsFldHelpEnter the Previous Unit Cost"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameold_stock_stat"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelOld Stock Status"
"ttFieldPropsFldColLabelOld Stock Status"
"ttFieldPropsFldHelpEnter the Old Stock Status"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepacker"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(14)"
"ttFieldPropsFldSideLabelpacker"
"ttFieldPropsFldColLabelPacker"
"ttFieldPropsFldHelpEnter the packer"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepallet_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(17)"
"ttFieldPropsFldSideLabelPallet Id"
"ttFieldPropsFldColLabelPallet Id"
"ttFieldPropsFldHelpEnter the Pallet Id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepallet_id_from"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(17)"
"ttFieldPropsFldSideLabelFrom Pallet"
"ttFieldPropsFldColLabelFrom Pallet"
"ttFieldPropsFldHelpEnter the From Pallet"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepool"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelPool"
"ttFieldPropsFldColLabelPool"
"ttFieldPropsFldHelpEnter the Pool"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepo_line"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>9"
"ttFieldPropsFldSideLabelpo_line"
"ttFieldPropsFldColLabelPO Line"
"ttFieldPropsFldHelpEnter the po_line"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepo_number"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelPO Number"
"ttFieldPropsFldColLabelPO Number"
"ttFieldPropsFldHelpEnter the PO Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepo_suffix"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelPO Suffix"
"ttFieldPropsFldColLabelPO Suffix"
"ttFieldPropsFldHelpEnter the PO Suffix"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamepp"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(1)"
"ttFieldPropsFldSideLabelCF-P"
"ttFieldPropsFldColLabelCF-P"
"ttFieldPropsFldHelpEnter the CF-P"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameproc_created"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelCreated by"
"ttFieldPropsFldColLabelCreated by"
"ttFieldPropsFldHelpEnter the Created by"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameqa_release_id"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>,>>>,>>9"
"ttFieldPropsFldSideLabelQA Release ID"
"ttFieldPropsFldColLabelQA Release ID"
"ttFieldPropsFldHelpEnter the QA Release ID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamerecord_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(6)"
"ttFieldPropsFldSideLabelRecord Type"
"ttFieldPropsFldColLabelRecord Type"
"ttFieldPropsFldHelpEnter the Record Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamerelease_id"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelrelease_id"
"ttFieldPropsFldColLabelRelease_id"
"ttFieldPropsFldHelpEnter the release_id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameresult_code"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelResult Code"
"ttFieldPropsFldColLabelResult Code"
"ttFieldPropsFldHelpEnter the Result Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameresult_msg"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(70)"
"ttFieldPropsFldSideLabelResult Message"
"ttFieldPropsFldColLabelResult Message"
"ttFieldPropsFldHelpEnter the Result Message"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamerow_status"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitO"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelStatus"
"ttFieldPropsFldColLabelStatus"
"ttFieldPropsFldHelpEnter the Status"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamert_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelRT Number"
"ttFieldPropsFldColLabelRT Number"
"ttFieldPropsFldHelpEnter the RT Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameserial_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelSerial Number"
"ttFieldPropsFldColLabelSerial Number"
"ttFieldPropsFldHelpEnter the Serial Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameshf_num"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>>>9"
"ttFieldPropsFldSideLabelShift Number"
"ttFieldPropsFldColLabelShf #"
"ttFieldPropsFldHelpEnter the Shift Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamestock_stat"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelStock Status"
"ttFieldPropsFldColLabelStock Status"
"ttFieldPropsFldHelpEnter the Stock Status"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamesubstat_code"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelSubstatus Code"
"ttFieldPropsFldColLabelSubstatus Code"
"ttFieldPropsFldHelpEnter the Substatus Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamesugg_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>>,>>9.99"
"ttFieldPropsFldSideLabelAdjusted Quantity"
"ttFieldPropsFldColLabelAdjusted Quantity"
"ttFieldPropsFldHelpEnter the Adjusted Quantity"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametask_id"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>,>>>,>>9"
"ttFieldPropsFldSideLabelTask Id"
"ttFieldPropsFldColLabelTask Id"
"ttFieldPropsFldHelpEnter the Task Id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametransmission"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelTransmission Number"
"ttFieldPropsFldColLabelTransmission Number"
"ttFieldPropsFldHelpEnter the Transmission Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameTransTypDesc"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelTransTypDesc"
"ttFieldPropsFldColLabelTransTypDesc"
"ttFieldPropsFldHelpEnter the TransTypDesc"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametrans_link"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>>>>>>9"
"ttFieldPropsFldSideLabelTransaction Link"
"ttFieldPropsFldColLabelTransaction Link"
"ttFieldPropsFldHelpEnter the Transaction Link"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametrans_num"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>>>>>>9"
"ttFieldPropsFldSideLabelTransaction Number"
"ttFieldPropsFldColLabelTrans #"
"ttFieldPropsFldHelpEnter the Transaction Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametrans_sec_time"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelSeconds Since Midnight"
"ttFieldPropsFldColLabelSeconds Since Midnight"
"ttFieldPropsFldHelpEnter the Seconds Since Midnight"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametrans_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(2)"
"ttFieldPropsFldSideLabelTransaction Type"
"ttFieldPropsFldColLabelTransaction Type"
"ttFieldPropsFldHelpEnter the Transaction Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNametruck_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelTruck Id"
"ttFieldPropsFldColLabelTruck Id"
"ttFieldPropsFldHelpEnter the Truck Id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameUnitCost"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$>>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelUnit Cost"
"ttFieldPropsFldColLabelUnit Cost"
"ttFieldPropsFldHelpEnter the Unit Cost"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNameuom"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelUnit of Measure"
"ttFieldPropsFldColLabelUnit of Measure"
"ttFieldPropsFldHelpEnter the Unit of Measure"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamevoid"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelVoid"
"ttFieldPropsFldColLabelVoid"
"ttFieldPropsFldHelpEnter the Void"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAuditLog"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNameAuditLog"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTableAuditLog_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNameAuditLog"
"ttDataSourceJoinscDSTableAuditLog"
"ttDataSourceJoinscDBTableirms.AuditLog"
"ttDataSourceJoinscBufNameAuditLog_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldstrans_num"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameblp/SearchAuditlog_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextyes"
"ttOptionsmakePrevyes"
"ttOptionsmakeLastyes"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionyes"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTableAuditLog"
"ttAttachSourcecSrcNameAuditLog"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsabs_num,AuditLog_1.abs_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsaction_code,AuditLog_1.action_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsadj_code,AuditLog_1.adj_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsbatch,AuditLog_1.batch"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsbin_from,AuditLog_1.bin_from"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsbin_num,AuditLog_1.bin_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsbin_to,AuditLog_1.bin_to"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscancelled,AuditLog_1.cancelled"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscancelled_at,AuditLog_1.cancelled_at"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscancelled_by,AuditLog_1.cancelled_by"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscargo_control,AuditLog_1.cargo_control"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscarton_id,AuditLog_1.carton_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscase_qty,AuditLog_1.case_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscc_string,AuditLog_1.cc_string"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscc_type,AuditLog_1.cc_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldscomments,AuditLog_1.comments"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsco_num,AuditLog_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsdate_time,AuditLog_1.date_time"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsdept_num,AuditLog_1.dept_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsdoc_id,AuditLog_1.doc_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsemp_num,AuditLog_1.emp_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsexp_abs,AuditLog_1.exp_abs"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsextdCost,AuditLog_1.extdCost"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsGUID,AuditLog_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsifaces_file,AuditLog_1.ifaces_file"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsifaces_time,AuditLog_1.ifaces_time"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsitem_num,AuditLog_1.item_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsitem_qty,AuditLog_1.item_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsitem_type,AuditLog_1.item_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsline_sequence,AuditLog_1.line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldslot,AuditLog_1.lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsmach_type,AuditLog_1.mach_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsmsg_status,AuditLog_1.msg_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsns_comment,AuditLog_1.ns_comment"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsoldextdCost,AuditLog_1.oldextdCost"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsoldUnitCost,AuditLog_1.oldUnitCost"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsold_stock_stat,AuditLog_1.old_stock_stat"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspacker,AuditLog_1.packer"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspallet_id,AuditLog_1.pallet_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspallet_id_from,AuditLog_1.pallet_id_from"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspool,AuditLog_1.pool"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspo_line,AuditLog_1.po_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspo_number,AuditLog_1.po_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldspo_suffix,AuditLog_1.po_suffix"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsproc_created,AuditLog_1.proc_created"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsqa_release_id,AuditLog_1.qa_release_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsrecord_type,AuditLog_1.record_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsrelease_id,AuditLog_1.release_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsresult_code,AuditLog_1.result_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsresult_msg,AuditLog_1.result_msg"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsrow_status,AuditLog_1.row_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsrt_num,AuditLog_1.rt_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsserial_num,AuditLog_1.serial_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsshf_num,AuditLog_1.shf_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsstock_stat,AuditLog_1.stock_stat"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldssubstat_code,AuditLog_1.substat_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldssugg_qty,AuditLog_1.sugg_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstask_id,AuditLog_1.task_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstransmission,AuditLog_1.transmission"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstrans_link,AuditLog_1.trans_link"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstrans_num,AuditLog_1.trans_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstrans_sec_time,AuditLog_1.trans_sec_time"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstrans_type,AuditLog_1.trans_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldstruck_id,AuditLog_1.truck_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsUnitCost,AuditLog_1.UnitCost"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsuom,AuditLog_1.uom"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldsvoid,AuditLog_1.void"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAuditLog"
"ttAttachDtlcSrcNameAuditLog"
"ttAttachDtlMappedFieldswh_num,AuditLog_1.wh_num"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"