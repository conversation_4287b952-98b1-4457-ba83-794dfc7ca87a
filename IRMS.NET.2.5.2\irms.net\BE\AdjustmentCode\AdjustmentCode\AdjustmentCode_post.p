/**=================================================================**
* Y:\BE_Area\src\be\AdjustmentCode\AdjustmentCode\AdjustmentCode_post.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:36 PM
**=================================================================**/


/* Business Entity Definintions */
{AdjustmentCode/AdjustmentCode/AdjustmentCode_ds.i}
{AdjustmentCode/AdjustmentCode/AdjustmentCode_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsAdjustmentCode .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN DO:
        CREATE ds_Control.
        ASSIGN ds_Control.PropName = 'COMMAND'
               ds_Control.PropValue = 'POST'.
    END.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


