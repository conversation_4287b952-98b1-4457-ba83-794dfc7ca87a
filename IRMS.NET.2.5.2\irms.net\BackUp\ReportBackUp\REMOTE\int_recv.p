/*** 
** Prints Item Labels on Intermec  printer
** File name is hard coded in lbl_prnt.w .
***/

DEF VAR SCCS_ID  AS CHAR NO-UNDO INIT "@(#) $Header: /pdb/9.01/remote/RCS/int_recv.p,v 1.3 2000-02-24 12:07:08-06 tanya Exp $~n" .
                     

&SCOP DEBUG FALSE 

DEF INPUT PARAMETER ch_abs_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_descr         AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_po_num        AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_rec_date      AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_emp_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_lot           AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_loc           AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_ser_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_lbl_size      AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_printer       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER i_qty            AS  INT   NO-UNDO .

DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
DEF VAR ch_print  AS CHAR NO-UNDO        .
DEF VAR ch_file   AS CHAR NO-UNDO        .

MESSAGE "Starting Printing..." .

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .


/***
**   Build print File 
*/

DO WHILE i_qty >= i_entry :

   ASSIGN
      i_entry = i_entry + 1 .                          

   MESSAGE 'Generating ' ch_abs_num  'Label...'.
      
   RUN print_label.
END.


/***
**  Print the File
***/
message "About to print item labels:" ch_file .

if (opsys eq "UNIX")  
then do:
    ASSIGN
        ch_print = "lp -c -d " + ch_printer + " " + ch_file .

    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .
    
    pause 5 no-message.
    
    OS-DELETE  VALUE ( ch_file    ) .
end .

else do: /*NT*/

 /********************* old code using spooler ***********************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        message "Copying" ch_file "to" ch_spool_dir .
    
        os-copy value(ch_file) value(ch_spool_dir) .
        
        os-delete value(ch_file) .
            
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .
   *******************************************************************/
   

       message "copying file  " ch_file "to printer " ch_printer .

       OS-COPY VALUE( ch_file ) VALUE( ch_printer) .

       OS-DELETE  VALUE ( ch_file  ) .   
   
end .
     
 /* the end */
  return .    

PROCEDURE print_label:                         
   DEF VAR  ch_value AS CHAR NO-UNDO .


    OUTPUT TO VALUE ( ch_file )  APPEND. 
    CASE ch_lbl_size :

       WHEN "2x4" THEN   
       DO:

          PUT UNFORMATTED  
             "<STX><ESC>C<ETX>~n"
             "<STX><ESC>P;E3;F3;<ETX>~n"
  

             "<STX>L50;o003,000;l796;f3;w4;<ETX>~n"
             "<STX>L51;o003,000;l350;f0;w4;<ETX>~n"
             "<STX>L52;o353,000;l796;f3;w4;<ETX>~n"
             "<STX>L53;o003,792;l350;f0;w4;<ETX>~n"
             "<STX>L54;o170,000;l796;f3;w4;<ETX>~n"
             "<STX>L55;o230,000;l796;f3;w4;<ETX>~n"
             "<STX>L56;o290,000;l796;f3;w4;<ETX>~n"
             "<STX>L57;o230,490;l060;f0;w4;<ETX>~n"
             "<STX>L58;o230,160;l060;f0;w4;<ETX>~n"
             "<STX>L59;o290,395;l060;f0;w4;<ETX>~n"


             "<STX>H00;o015,785;f1;h1;w1;c2;d3,Item Number:;<ETX>~n"
             "<STX>H01;o175,785;f1;h1;w1;c2;d3,Description:;<ETX>~n"
             "<STX>H02;o235,785;f1;h1;w1;c2;d3,Received #:;<ETX>~n"
             "<STX>H03;o235,485;f1;h1;w1;c2;d3,Received Date:;<ETX>~n"
             "<STX>H04;o235,155;f1;h1;w1;c2;d3,Received By:;<ETX>~n"
             "<STX>H05;o295,785;f1;h1;w1;c2;d3,Lot #:;<ETX>~n"
             "<STX>H06;o295,390;f1;h1;w1;c2;d3,Serial #:;<ETX>~n"

             "<STX>H07;o000,630;f1;h1;w1;c21;d0,24;<ETX>~n"
             "<STX>H08;o190,785;f1;h1;w1;c21;d0,30;<ETX>~n"
             "<STX>H09;o250,785;f1;h1;w1;c21;d0,14;<ETX>~n"
             "<STX>H10;o250,485;f1;h1;w1;c21;d0,16;<ETX>~n"
             "<STX>H11;o250,155;f1;h1;w1;c21;d0,5;<ETX>~n"
             "<STX>H12;o315,740;f1;h2;w1;c2;d0,24;<ETX>~n"
             "<STX>H13;o315,350;f1;h2;w1;c2;d0,24;<ETX>~n"
                  
             "<STX>B90;o045,670;c6,0;f1;h110;w2;i0;d0,24;<ETX>~n"      
             "<STX>R<ETX>~n"
  
        
             "<STX><ESC>E3<ETX>~n"
             "<STX><CAN><ETX>~n"
             "<STX>" + ch_abs_num + "<CR><ETX>~n"
             "<STX>" + ch_descr + "<CR><ETX>~n"
             "<STX>" + ch_po_num + "<CR><ETX>~n"
             "<STX>" + ch_rec_date + "<CR><ETX>~n"
             "<STX>" + ch_emp_num + "<CR><ETX>~n"
             "<STX>" + ch_lot + "<CR><ETX>~n"
             "<STX>" + ch_ser_num + "<CR><ETX>~n"
             "<STX>" + ch_abs_num + "<CR><ETX>~n"
             "<STX><ETB><ETX>~n"
             .
             
       END .
       
       WHEN "2x3" THEN
       DO:
       
          PUT UNFORMATTED

             "<STX><ESC>C<ETX>~n"
             "<STX><ESC>P;E3;F3;<ETX>~n"
  
             "<STX>L50;o003,000;l598;f3;w4;<ETX>~n"
             "<STX>L51;o003,000;l350;f0;w4;<ETX>~n"
             "<STX>L52;o353,000;l598;f3;w4;<ETX>~n"
             "<STX>L53;o003,596;l350;f0;w4;<ETX>~n"
             "<STX>L54;o170,000;l596;f3;w4;<ETX>~n"
             "<STX>L55;o230,000;l596;f3;w4;<ETX>~n"
             "<STX>L56;o290,000;l596;f3;w4;<ETX>~n"
             "<STX>L57;o230,390;l060;f0;w4;<ETX>~n"
             "<STX>L58;o230,170;l060;f0;w4;<ETX>~n"
             "<STX>L59;o290,298;l060;f0;w4;<ETX>~n"

             "<STX>H00;o015,591;f1;h1;w1;c2;d3,Item Number:;<ETX>~n"
             "<STX>H01;o175,591;f1;h1;w1;c2;d3,Description:;<ETX>~n"
             "<STX>H02;o235,591;f1;h1;w1;c2;d3,Received #:;<ETX>~n"
             "<STX>H03;o235,385;f1;h1;w1;c2;d3,Received Date:;<ETX>~n"
             "<STX>H04;o235,165;f1;h1;w1;c2;d3,Received By:;<ETX>~n"
             "<STX>H05;o295,591;f1;h1;w1;c2;d3,Lot #:;<ETX>~n"
             "<STX>H06;o295,293;f1;h1;w1;c2;d3,Serial #:;<ETX>~n"

             "<STX>H07;o005,436;f1;h2;w1;c2;d0,24;<ETX>~n"
             "<STX>H08;o190,586;f1;h1;w1;c21;d0,25;<ETX>~n"
             "<STX>H09;o255,586;f1;h1;w1;c20;d0,14;<ETX>~n"
             "<STX>H10;o255,380;f1;h1;w1;c20;d0,16;<ETX>~n"
             "<STX>H11;o255,160;f1;h1;w1;c20;d0,5;<ETX>~n"
             "<STX>H12;o330,590;f1;h1;w1;c2;d0,24;<ETX>~n"
             "<STX>H13;o330,292;f1;h1;w1;c2;d0,24;<ETX>~n"
          
             "<STX>B90;o045,500;c6,0;f1;h110;w1.5;i0;d0,24;<ETX>~n"      
             "<STX>R<ETX>~n"

       
             "<STX><ESC>E3<ETX>~n"
             "<STX><CAN><ETX>~n"
             "<STX>" + ch_abs_num + "<CR><ETX>~n"
             "<STX>" + ch_descr + "<CR><ETX>~n"
             "<STX>" + ch_po_num + "<CR><ETX>~n"
             "<STX>" + ch_rec_date + "<CR><ETX>~n"
             "<STX>" + ch_emp_num + "<CR><ETX>~n"
             "<STX>" + ch_lot + "<CR><ETX>~n"
             "<STX>" + ch_ser_num + "<CR><ETX>~n"
             "<STX>" + ch_abs_num + "<CR><ETX>~n"
             "<STX><ETB><ETX>~n"
             .
             
       END .
       
       WHEN "1x4" THEN
       DO:
       
          PUT UNFORMATTED
       
             "<STX><ESC>C<ETX>~n"
             "<STX><ESC>P;E3;F3;~<ETX>~n"

             "<STX>L83;o175,000;l796;f3;w4;~<ETX>~n"
              
             "<STX>H00;o000,776;f1;h1;w1;c20;d0,24;~<ETX>~n"
             "<STX>H01;o000,380;f1;h1;w1;c20;d0,10;~<ETX>~n"
             "<STX>H02;o000,200;f1;h1;w1;c20;d0,10;~<ETX>~n"
             "<STX>H03;o130,776;f1;h1;w1;c20;d0,30;~<ETX>~n"
             "<STX>H04;o130,150;f1;h1;w1;c20;d0,06;~<ETX>~n"    
             "<STX>B100;o035,770;c6,0;f1;h90;w2;d0,24;p@;~<ETX>~n"
             "<STX>R<ETX>~n"

   
             "<STX><ESC>E3<ETX>~n"
             "<STX><CAN><ETX>~n"
             "<STX>" + ch_abs_num + "<CR><ETX>~n"
             "<STX>" + ch_rec_date + "<CR><ETX>~n"
             "<STX>" + ch_po_num + "<CR><ETX>~n"
             "<STX>" + ch_descr + "<CR><ETX>~n"
             "<STX>" + ch_emp_num + "<CR><ETX>~n"
             "<STX>" + ch_abs_num + "<CR><ETX>~n"
             "<STX><ETB><ETX>~n"
             .
                 
       END .
    END CASE .
END PROCEDURE.

