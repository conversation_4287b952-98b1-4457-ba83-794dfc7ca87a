/**=================================================================**
* Y:\BE_Area\src\be\Abcclassification\Abcclassification\Abcclassification_post.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/10/06, 16:46 PM
**=================================================================**/


/* Business Entity Definintions */
{Abcclassification/Abcclassification/Abcclassification_ds.i}
{Abcclassification/Abcclassification/Abcclassification_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsAbcclassification .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN DO:
        CREATE ds_Control.
        ASSIGN ds_Control.PropName = 'COMMAND'
               ds_Control.PropValue = 'POST'.
    END.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


