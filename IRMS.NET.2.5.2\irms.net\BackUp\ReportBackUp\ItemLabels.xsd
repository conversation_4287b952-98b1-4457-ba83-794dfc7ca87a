<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="http://tempuri.org/XMLSchema.xsd" elementFormDefault="qualified" xmlns="http://tempuri.org/XMLSchema.xsd" xmlns:mstns="http://tempuri.org/XMLSchema.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Inventory">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="abs_num" type="xs:string" />
        <xs:element name="item_desc" type="xs:string" />
        <xs:element name="po_number" type="xs:string" />
        <xs:element name="date_time" type="xs:dateTime" />
        <xs:element name="emp_num" type="xs:string" />
        <xs:element name="lot" type="xs:string" />
        <xs:element name="serial_num" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>