<?xml version="1.0" encoding="utf-8"?>
<XMLScript Version="2.0">
   <Command>
       <Print JobName="Job1">
           <PrintSetup>
               <Printer>\\bartender\zebra105</Printer>
               <IdenticalCopiesOfLabel>3</IdenticalCopiesOfLabel>
           </PrintSetup>
           <Format CloseAtEndOfJob="true">CADlabel.btw</Format>
           <RecordSet Name="Text File 1" Type="btTextFile">
               <Delimitation>btDelimCustom</Delimitation>
               <FieldDelimiter>|</FieldDelimiter>
               <UseFieldNamesFromFirstRecord>true</UseFieldNamesFromFirstRecord>
               <TextData>
               <![CDATA[Ship From Name|Ship From Addr1|Ship From Addr2|Ship From Addr3|Ship From City|Ship From State|Ship From Zip Code|Ship From Country|Ship To name|Ship To Addr1|Ship To Addr2|Ship To Addr3|Ship To City|Ship To State|Ship To Zip Code|Ship To Country|Customer PO|Order|Order Suffix
               Rubies Costume Company|540 Broadhallow road  (RT 110)|| |Melville|NY|11747|USA|||| ||||USA||W000001309|00
               ]]>
               </TextData>
           </RecordSet>
       </Print>
   </Command>
</XMLScript>
