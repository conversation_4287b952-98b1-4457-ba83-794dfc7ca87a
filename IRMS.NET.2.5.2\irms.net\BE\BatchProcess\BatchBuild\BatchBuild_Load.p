/**=================================================================**
* S:\IRMS.NET.2.5.2\irms.net\BE\BatchProcess\BatchBuild\BatchBuild_Load.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 08/19/09, 16:30 PM
**=================================================================**/


/* Business Entity Definintions */
{BatchProcess/BatchBuild/BatchBuild_ds.i}
{BatchProcess/BatchBuild/BatchBuild_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT        PARAM ipcGUID AS  CHAR .
    DEF       OUTPUT PARAM DATASET FOR dsBatchBuild .


    DEF VAR hDataSet   AS HANDLE   NO-UNDO. 
    DEF VAR cTable     AS CHAR     NO-UNDO. 


    hDataSet = DYNAMIC-FUNCTION('getProperty' IN THIS-PROCEDURE,'DataSetHandle').
    IF hDataSet:NUM-RELATIONS > 0 THEN
       cTable = hDataSet:GET-RELATION(1):PARENT-BUFFER:NAME.
    ELSE
       cTable = hDataSet:GET-BUFFER-HANDLE(1):NAME.


    CREATE ds_Filter.
    ASSIGN
       ds_Filter.TableName  = cTable
       ds_Filter.FieldName  = 'GUID'
       ds_Filter.Operand    = '='
       ds_Filter.FieldValue = ipcGUID
       .


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


