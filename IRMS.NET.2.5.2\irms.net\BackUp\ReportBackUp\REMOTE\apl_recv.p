/*** 
** Prints Apollo Labels on Intermec  printer
** File name is hard coded in lbl_prnt.w .
***/

DEF VAR SCCS_ID  AS CHAR NO-UNDO INIT "@(#) $Header: /pdb/9.01/remote/RCS/apl_recv.p,v 1.2 2000/02/24 20:11:52 tanya Exp $~n" .
                     

&SCOP DEBUG FALSE 

DEF INPUT PARAMETER ch_abs_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_descr         AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_po_num        AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_rec_date      AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_emp_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_lot           AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_loc           AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_ser_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_lbl_size      AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_printer AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER i_qty            AS  INT   NO-UNDO .


DEF VAR ch_type   AS CHAR NO-UNDO INIT ? .
DEF VAR ch_list   AS CHAR NO-UNDO INIT ? .
DEF VAR ch_que    AS CHAR NO-UNDO INIT ? .
DEF VAR ch_arrow  AS CHAR NO-UNDO INIT ? .
DEF VAR ch_format AS CHAR NO-UNDO INIT ? . 
DEF VAR i_type    AS INT  NO-UNDO INIT ? .
DEF VAR i_arrow   AS INT  NO-UNDO INIT ? .
DEF VAR i_num_ent AS INT  NO-UNDO        .
DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
DEF VAR ch_print  AS CHAR NO-UNDO        .
DEF VAR ch_file   AS CHAR NO-UNDO        .

MESSAGE "Starting Printing..." .

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .


/***
**   Build print File 
*/

DO WHILE i_qty >= i_entry :

   ASSIGN
      i_entry = i_entry + 1 .                          

   MESSAGE 'Generating ' ch_abs_num  'Label...'.
      
   RUN print_label.
END.


/***
**  Print the File
***/
message "About to print item labels:" ch_file .

if (opsys eq "UNIX")  
then do:
    ASSIGN
        ch_print = "lp -c -d " + ch_printer + " " + ch_file .

    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .
    
    pause 5 no-message.
    
    OS-DELETE  VALUE ( ch_file    ) .
end .

else do: /*NT*/

    /******************** old code using spooler ***********************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_que
        .

        message "Copying" ch_file "to" ch_spool_dir .
    
        os-copy value(ch_file) value(ch_spool_dir) .
        
        os-delete value(ch_file) .
            
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .
    *******************************************************************/
       
    message "copying file  " ch_file "to printer " ch_printer .

    OS-COPY VALUE( ch_file) VALUE( ch_printer) .

    OS-DELETE  VALUE ( ch_file ) .   
    
end .
     
 /* the end */
  return .    

PROCEDURE print_label:                         
   DEF VAR  ch_value AS CHAR NO-UNDO .


    OUTPUT TO VALUE ( ch_file )  APPEND. 

    CASE ch_lbl_size :

       WHEN "2x4" THEN   
       DO:

          PUT UNFORMATTED                          
            "m i~n"
            "J~n"

            "Sl1;0,0.05,2.00,2.10,4.0~n"

            "G 0.0,0.80,0;   L:3.80,0.02~n"
            "G 0.0,1.15,0;   L:3.80,0.02~n"
            "G 0.0,1.50,0;   L:3.80,0.02~n"
            "G 1.4,1.15,270; L:0.35,0.02~n"
            "G 2.8,1.15,270; L:0.35,0.02~n"
            "G 1.9,1.50,270; L:0.35,0.02~n"
   
            "G 0,0,0;    R:3.8,1.85,0.02,0.02~n"
 

            "B 0.35,0.25,0,code128,0.5,0.012;" + ch_abs_num + "~n"

            "T 0.10,0.10,0,3,pt7;Item Number:~n"
            "T 0.10,0.90,0,3,pt7;Description:~n"
            "T 0.10,1.25,0,3,pt7;Received #:~n"
            "T 1.50,1.25,0,3,pt7;Received Date:~n"
            "T 2.90,1.25,0,3,pt7;Received By:~n"
            "T 0.10,1.60,0,3,pt7;Lot #::~n"
            "T 2.00,1.60,0,3,pt7;Serial #:~n"   
    
            "T 0.80,0.15,0,5,0.16,h.11;" + ch_abs_num + "~n"
            "T 0.15,1.10,0,5,0.16,h.11;" + ch_descr + "~n"
            "T 0.15,1.45,0,5,0.16,h.11;" + ch_po_num + "~n"
            "T 1.55,1.45,0,5,0.16,h.11;" + ch_rec_date + "~n"
            "T 2.95,1.45,0,5,0.16,h.11;" + ch_emp_num + "~n"
            "T 0.15,1.80,0,5,0.14,h.08;" + ch_lot + "~n"
            "T 2.05,1.80,0,5,0.14,h.08;" + ch_ser_num + "~n"
       
            "A 1~n"
            .
             
       END .
       
       WHEN "2x3" THEN
       DO:
          PUT UNFORMATTED   
             "m i~n"
             "J~n"

             "Sl1;0,0.05,2.00,2.10,3.0~n"

             "G 0.0,1.15,0;   L:2.80,0.02~n"
             "G 0.0,1.50,0;   L:2.80,0.02~n"
  
             "G 1.40,1.50,270; L:0.35,0.02~n"
             "G 1.10,1.15,270; L:0.35,0.02~n"
             "G 2.10,1.15,270; L:0.35,0.02~n"
 
   
             "G 0,0.8,0;    R:2.8,1.05,0.02,0.02~n"
 
             "B 0.02,0.20,0,code128,0.5,0.008;" + ch_abs_num + "~n"   
 
             "T 0.05,0.10,0,3,pt7;Item Number:~n"
             "T 0.05,0.90,0,3,pt7;Description:~n"
             "T 0.05,1.25,0,3,pt7;Received #:~n"
             "T 1.15,1.25,0,3,pt7;Received Date:~n"
             "T 2.15,1.25,0,3,pt7;Received By:~n"
             "T 0.05,1.60,0,3,pt7;Lot #:~n"
             "T 1.45,1.60,0,3,pt7;Serial #:~n"
          
             "T 0.70,0.15,0,5,0.15,h.08;" + ch_abs_num + "~n"
             "T 0.10,1.10,0,5,0.16,h.11;" + ch_descr + "~n"
             "T 0.10,1.45,0,3,pt8;" + ch_po_num + "~n"
             "T 1.20,1.45,0,3,pt8;" + ch_rec_date + "~n"
             "T 2.20,1.45,0,3,pt8;" + ch_emp_num + "~n"
             "T 0.05,1.80,0,3,0.1,h.07;" + ch_lot + "~n"
             "T 1.45,1.80,0,3,0.1,h.07;" + ch_ser_num + "~n"     
   
             "A 1~n" .

       END .
       
       WHEN "1x4" THEN
       DO:

       END .
       WHEN "4x6" THEN
       DO:

       END .
       
    END CASE .



END PROCEDURE.

