/**=================================================================**
* Y:\irms.net.1.4.0\irms.net\BE\AltLocation\AltLocation\Altlocation_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 17/07/07, 10:39 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qAltLocation FOR AltLocation SCROLLING . 
QUERY qAltLocation:QUERY-PREPARE("FOR EACH AltLocation").
QUERY qAltLocation:QUERY-OPEN.


DEF  QUERY qAltLocation_BEFORE FOR AltLocation_BEFORE SCROLLING . 
QUERY qAltLocation_BEFORE:QUERY-PREPARE("FOR EACH AltLocation_BEFORE").
QUERY qAltLocation_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "AltLocation" */
DEFINE BUFFER binmst_1 FOR irms.binmst.
DEFINE QUERY qSrcAltLocation
    FOR binmst_1
        SCROLLING.
DEFINE DATA-SOURCE AltLocation
    FOR QUERY qSrcAltLocation
        binmst_1 KEYS (co_num,wh_num,bin_num)        .
DATA-SOURCE AltLocation:PREFER-DATASET = no.
DATA-SOURCE AltLocation:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.07.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_AltLocation                  AS  HANDLE              
    FIELD   hQry_AltLocation                 AS  HANDLE              
    FIELD   hQry_AltLocation_BEFORE          AS  HANDLE              
    FIELD   AltLocation_DataSourceHdl        AS  HANDLE              
    FIELD   AltLocation_BatchSize            AS  INTEGER             
                                                 INIT 50
    FIELD   AltLocation_Fill                 AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocation_CanRead              AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocation_CanCreate            AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocation_CanUpdate            AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocation_CanDelete            AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocation_Src_Names            AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocation_Src_Hdls             AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocation_CurrentSource        AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   AltLocation_UniqueKey            AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   AltLocation_AltLocation_Map      AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocation_AltLocation_CF       AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   AltLocation_AltLocation_NoP      AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocation_hdl                  AS  HANDLE              
    FIELD   AltLocation_UseQuery             AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocation_PostTable            AS  CHARACTER           
                                                 INIT "binmst_1"
    FIELD   AltLocation_qhdl                 AS  HANDLE              
    FIELD   AltLocation_binmst_1_W           AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocation_binmst_1_S           AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocation_Buffs                AS  CHARACTER           
                                                 INIT "binmst_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "binmst,AltLocation"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "AltLocation,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsAltlocation:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_AltLocation                  = QUERY qAltLocation:HANDLE
       htt_AltLocation                   = TEMP-TABLE AltLocation:HANDLE
       hQry_AltLocation_BEFORE           = QUERY qAltLocation_BEFORE:HANDLE
       AltLocation_src_Names             = 'AltLocation,Default'
       AltLocation_src_Hdls              =         STRING(DATA-SOURCE AltLocation:HANDLE)
                                           + ',' + STRING(DATA-SOURCE AltLocation:HANDLE)
       AltLocation_AltLocation_Map       =         'abc,binmst_1.abc'
                                           + ',' + 'abs_num,binmst_1.abs_num'
                                           + ',' + 'aisle,binmst_1.aisle'
                                           + ',' + 'bin_full,binmst_1.bin_full'
                                           + ',' + 'bin_hits,binmst_1.bin_hits'
                                           + ',' + 'bin_num,binmst_1.bin_num'
                                           + ',' + 'check_qty,binmst_1.check_qty'
                                           + ',' + 'co_num,binmst_1.co_num'
                                           + ',' + 'cube,binmst_1.cube'
                                           + ',' + 'depth,binmst_1.depth'
                                           + ',' + 'GUID,binmst_1.GUID'
                                           + ',' + 'height,binmst_1.height'
                                           + ',' + 'host_origin,binmst_1.host_origin'
                                           + ',' + 'loc_type,binmst_1.loc_type'
                                           + ',' + 'max_lvl,binmst_1.max_lvl'
                                           + ',' + 'max_pal,binmst_1.max_pal'
                                           + ',' + 'max_weight,binmst_1.max_weight'
                                           + ',' + 'min_lvl,binmst_1.min_lvl'
                                           + ',' + 'pallet_footprint,binmst_1.pallet_footprint'
                                           + ',' + 'physical,binmst_1.physical'
                                           + ',' + 'prim_pick,binmst_1.prim_pick'
                                           + ',' + 'prim_pick_type,binmst_1.prim_pick_type'
                                           + ',' + 'rep_qty,binmst_1.rep_qty'
                                           + ',' + 'rep_unit,binmst_1.rep_unit'
                                           + ',' + 'row_status,binmst_1.row_status'
                                           + ',' + 'stack_height,binmst_1.stack_height'
                                           + ',' + 'wh_num,binmst_1.wh_num'
                                           + ',' + 'wh_zone,binmst_1.wh_zone'
                                           + ',' + 'width,binmst_1.width'
                                           + ',' + 'wood_flag,binmst_1.wood_flag'
       AltLocation_hdl                   = DATA-SOURCE AltLocation:HANDLE
       AltLocation_qhdl                  = QUERY qSrcAltLocation:HANDLE
       TopLevelTables                    = 'AltLocation'
       DataSetOneTransaction             = no
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/AltLocation_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAltlocation .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAltlocation BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAltlocation .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAltlocation BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AltLocation_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAltlocation .
     RUN AltLocation_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAltlocation BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AltLocation_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAltlocation .
     RUN AltLocation_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAltlocation BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_AltLocation_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAltlocation .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('AltLocation') NO-ERROR .
     RUN AltLocation_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAltlocation BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AltLocation_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAltlocation .
     RUN AfterRowFill  IN THIS-PROCEDURE ('AltLocation') NO-ERROR .
     RUN AltLocation_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAltlocation BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


