/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\SearchItem\SearchItem_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:39 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qttorddtlSrch FOR ttorddtlSrch SCROLLING . 
QUERY qttorddtlSrch:QUERY-PREPARE("FOR EACH ttorddtlSrch").
QUERY qttorddtlSrch:QUERY-OPEN.


DEF  QUERY qttorddtlSrch_BEFORE FOR ttorddtlSrch_BEFORE SCROLLING . 
QUERY qttorddtlSrch_BEFORE:QUERY-PREPARE("FOR EACH ttorddtlSrch_BEFORE").
QUERY qttorddtlSrch_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "srcttorddtlsh" */
DEFINE BUFFER orddtl_1 FOR irms.orddtl.
DEFINE BUFFER item_1 FOR irms.item.
DEFINE QUERY qSrcsrcttorddtlsh
    FOR orddtl_1,
        item_1
        SCROLLING.
DEFINE DATA-SOURCE srcttorddtlsh
    FOR QUERY qSrcsrcttorddtlsh
        orddtl_1 KEYS (id,line,line_sequence),
        item_1 KEYS (co_num,wh_num,abs_num)        .
DATA-SOURCE srcttorddtlsh:PREFER-DATASET = no.
DATA-SOURCE srcttorddtlsh:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttorddtlSrch                 AS  HANDLE              
    FIELD   hQry_ttorddtlSrch                AS  HANDLE              
    FIELD   hQry_ttorddtlSrch_BEFORE         AS  HANDLE              
    FIELD   ttorddtlSrch_DataSourceHdl       AS  HANDLE              
    FIELD   ttorddtlSrch_BatchSize           AS  INTEGER             
                                                 INIT 50
    FIELD   ttorddtlSrch_Fill                AS  LOGICAL             
                                                 INIT yes
    FIELD   ttorddtlSrch_CanRead             AS  LOGICAL             
                                                 INIT yes
    FIELD   ttorddtlSrch_CanCreate           AS  LOGICAL             
                                                 INIT no
    FIELD   ttorddtlSrch_CanUpdate           AS  LOGICAL             
                                                 INIT no
    FIELD   ttorddtlSrch_CanDelete           AS  LOGICAL             
                                                 INIT no
    FIELD   ttorddtlSrch_Src_Names           AS  CHARACTER           
                                                 INIT ""
    FIELD   ttorddtlSrch_Src_Hdls            AS  CHARACTER           
                                                 INIT ""
    FIELD   ttorddtlSrch_CurrentSource       AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   ttorddtlSrch_UniqueKey           AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ttorddtlSrch_srcttorddtlsh_Map   AS  CHARACTER           
                                                 INIT ""
    FIELD   ttorddtlSrch_srcttorddtlsh_CF    AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ttorddtlSrch_srcttorddtlsh_NoP   AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_hdl                AS  HANDLE              
    FIELD   srcttorddtlsh_UseQuery           AS  LOGICAL             
                                                 INIT yes
    FIELD   srcttorddtlsh_PostTable          AS  CHARACTER           
                                                 INIT "orddtl_1"
    FIELD   srcttorddtlsh_qhdl               AS  HANDLE              
    FIELD   srcttorddtlsh_orddtl_1_W         AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_orddtl_1_S         AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_item_1_W           AS  CHARACTER           
                                                 INIT "item_1.co_num = orddtl_1.co_num and item_1.wh_num = orddtl_1.wh_num and  item_1.abs_num = orddtl_1.abs_num"
    FIELD   srcttorddtlsh_item_1_S           AS  CHARACTER           
                                                 INIT ""
    FIELD   srcttorddtlsh_Buffs              AS  CHARACTER           
                                                 INIT "orddtl_1,item_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "orddtl,ttorddtlSrch,item,ttorddtlSrch"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "ttorddtlSrch,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsSearchItem:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_ttorddtlSrch                 = QUERY qttorddtlSrch:HANDLE
       htt_ttorddtlSrch                  = TEMP-TABLE ttorddtlSrch:HANDLE
       hQry_ttorddtlSrch_BEFORE          = QUERY qttorddtlSrch_BEFORE:HANDLE
       ttorddtlSrch_src_Names            = 'srcttorddtlsh,Default'
       ttorddtlSrch_src_Hdls             =         STRING(DATA-SOURCE srcttorddtlsh:HANDLE)
                                           + ',' + STRING(DATA-SOURCE srcttorddtlsh:HANDLE)
       ttorddtlSrch_srcttorddtlsh_Map    =         'abs_num,orddtl_1.abs_num'
                                           + ',' + 'act_qty,orddtl_1.act_qty'
                                           + ',' + 'assigned,orddtl_1.assigned'
                                           + ',' + 'bin_num,orddtl_1.bin_num'
                                           + ',' + 'charges,orddtl_1.charges'
                                           + ',' + 'comment,orddtl_1.comment'
                                           + ',' + 'co_num,orddtl_1.co_num'
                                           + ',' + 'discount,orddtl_1.discount'
                                           + ',' + 'DiscountAmt,orddtl_1.DiscountAmt'
                                           + ',' + 'drop_cube,orddtl_1.drop_cube'
                                           + ',' + 'drop_weight,orddtl_1.drop_weight'
                                           + ',' + 'ExtdPrice,orddtl_1.ExtdPrice'
                                           + ',' + 'filler_flag,orddtl_1.filler_flag'
                                           + ',' + 'fl_zone,orddtl_1.fl_zone'
                                           + ',' + 'gift_wrap,orddtl_1.gift_wrap'
                                           + ',' + 'GUID,orddtl_1.GUID'
                                           + ',' + 'host_origin,orddtl_1.host_origin'
                                           + ',' + 'id,orddtl_1.id'
                                           + ',' + 'item_desc,item_1.item_desc'
                                           + ',' + 'line,orddtl_1.line'
                                           + ',' + 'line_alt_number,orddtl_1.line_alt_number'
                                           + ',' + 'line_sequence,orddtl_1.line_sequence'
                                           + ',' + 'line_status,orddtl_1.line_status'
                                           + ',' + 'lot,orddtl_1.lot'
                                           + ',' + 'msds_employee,orddtl_1.msds_employee'
                                           + ',' + 'msds_packed,orddtl_1.msds_packed'
                                           + ',' + 'msds_required,orddtl_1.msds_required'
                                           + ',' + 'msds_sheet,orddtl_1.msds_sheet'
                                           + ',' + 'ordered_qty,orddtl_1.ordered_qty'
                                           + ',' + 'order_alt_num,orddtl_1.order_alt_num'
                                           + ',' + 'order_alt_suf,orddtl_1.order_alt_suf'
                                           + ',' + 'orig_cube,orddtl_1.orig_cube'
                                           + ',' + 'orig_req_qty,orddtl_1.orig_req_qty'
                                           + ',' + 'orig_weight,orddtl_1.orig_weight'
                                           + ',' + 'package_code,orddtl_1.package_code'
                                           + ',' + 'pick_line,orddtl_1.pick_line'
                                           + ',' + 'pool,orddtl_1.pool'
                                           + ',' + 'po_line,orddtl_1.po_line'
                                           + ',' + 'po_line_sequence,orddtl_1.po_line_sequence'
                                           + ',' + 'po_number,orddtl_1.po_number'
                                           + ',' + 'po_suffix,orddtl_1.po_suffix'
                                           + ',' + 'req_emp,orddtl_1.req_emp'
                                           + ',' + 'req_qty,orddtl_1.req_qty'
                                           + ',' + 'ret_qty,orddtl_1.ret_qty'
                                           + ',' + 'rt_num,orddtl_1.rt_num'
                                           + ',' + 'same_lot,orddtl_1.same_lot'
                                           + ',' + 'serial_num,orddtl_1.serial_num'
                                           + ',' + 'ship_cube,orddtl_1.ship_cube'
                                           + ',' + 'ship_weight,orddtl_1.ship_weight'
                                           + ',' + 'stock_stat,orddtl_1.stock_stat'
                                           + ',' + 'tax,orddtl_1.tax'
                                           + ',' + 'vendor_id,orddtl_1.vendor_id'
                                           + ',' + 'wh_num,orddtl_1.wh_num'
                                           + ',' + 'work_center,orddtl_1.work_center'
       srcttorddtlsh_hdl                 = DATA-SOURCE srcttorddtlsh:HANDLE
       srcttorddtlsh_qhdl                = QUERY qSrcsrcttorddtlsh:HANDLE
       TopLevelTables                    = 'ttorddtlSrch'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/SearchARItem_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchItem .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchItem BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchItem .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchItem BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttorddtlSrch_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchItem .
     RUN ttorddtlSrch_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchItem BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttorddtlSrch_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchItem .
     RUN ttorddtlSrch_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchItem BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_ttorddtlSrch_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchItem .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('ttorddtlSrch') NO-ERROR .
     RUN ttorddtlSrch_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchItem BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttorddtlSrch_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearchItem .
     RUN AfterRowFill  IN THIS-PROCEDURE ('ttorddtlSrch') NO-ERROR .
     RUN ttorddtlSrch_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearchItem BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


