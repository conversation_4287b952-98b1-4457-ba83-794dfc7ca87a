<?xml version="1.0" encoding="utf-8"?>
<XMLScript Version="2.0">
   <Command>
       <Print JobName="Job1">
           <PrintSetup>
               <Printer>\\panther\zebra220</Printer>
           </PrintSetup>
           <Format CloseAtEndOfJob="true">itemlabel.btw</Format>
           <RecordSet Name="Text File 1" Type="btTextFile">
               <Delimitation>btDelimCustom</Delimitation>
               <FieldDelimiter>|</FieldDelimiter>
               <UseFieldNamesFromFirstRecord>true</UseFieldNamesFromFirstRecord>
               <TextData>
               <![CDATA[ItemNumber|ItemDescription|RcdDate|ReceivedBy|PONumber|POSuffix|Lot|Expiry|Serial|Quantity|Status|ItemType|MSRP|UOM|Company|Warehouse|Zone|AltNum|UPC|Group|Line|SubLine|Class|MSDS|Country|SecDesc|LongDesc|NumofLabels
               1001|PHARMACEUTICAL SAMPLES XXXXXXX|03/16/09|IRMS|100|||| |48|C|S|123|EA|MDC|CRP|01|444|LIMU|LIMU|D|ttt|AI|iii|ABAAAAAAAABBBBBBBBBBCCCCCCCCCCDDDDDDDDDDEEEEEEEEEEFFFFFFFFFFGGGGGGGGGGHHHHHHHHHHIIIIIIIIII|1
               ]]>
               </TextData>
           </RecordSet>
       </Print>
   </Command>
</XMLScript>
