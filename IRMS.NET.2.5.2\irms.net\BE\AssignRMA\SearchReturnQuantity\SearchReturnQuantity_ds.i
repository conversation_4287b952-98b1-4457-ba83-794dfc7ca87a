/**=================================================================**
* S:\IRMS.NET.2.6.0\irms.net\BE\AssignRMA\SearchReturnQuantity\SearchReturnQuantity_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 07/11/08, 11:03 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE ttrtdetSrch NO-UNDO
    BEFORE-TABLE ttrtdetSrch_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   abs_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Item Number"
                                                 LABEL "Absolute Item Number"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the company-wide unique code for this item.|no|yes"
    FIELD   act_quantity                     AS  DECIMAL             
                                                 COLUMN-LABEL "Actual Quantity"
                                                 LABEL "Actual Quantity"
                                                 FORMAT ">>>,>>9"
                                                 INIT 0
                                                 HELP "Quantity received|no|yes"
    FIELD   asn_flag                         AS  LOGICAL             
                                                 COLUMN-LABEL "ASN FLag?"
                                                 LABEL "asn_flag"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Is this an EDI transaction?|no|yes"
    FIELD   bo_count                         AS  INTEGER             
                                                 COLUMN-LABEL "B/O Count"
                                                 LABEL "B/O Count"
                                                 FORMAT ">>9"
                                                 INIT 0
                                                 HELP "The number of back orders for this item.|no|yes"
    FIELD   bo_quantity                      AS  DECIMAL             
                                                 COLUMN-LABEL "B/O Quantity"
                                                 LABEL "B/O Quantity"
                                                 FORMAT ">>>,>>9"
                                                 INIT 0
                                                 HELP "Quantity.|no|yes"
    FIELD   case_qty                         AS  INTEGER             
                                                 COLUMN-LABEL "Outer Pack"
                                                 LABEL "Outer Pack"
                                                 FORMAT ">>,>>9"
                                                 INIT 0
                                                 HELP "Outer pack quantity for this RT line.|no|yes"
    FIELD   comments                         AS  CHARACTER           
                                                 COLUMN-LABEL "Comments"
                                                 LABEL "Comments"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Comments for this RT line.|no|yes"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the company number.|no|yes"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "Custom Fields"
                                                 LABEL "Custom Fields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Custom Fields|no|no"
    FIELD   delivery                         AS  DATE                
                                                 COLUMN-LABEL "Delivery"
                                                 LABEL "Delivery"
                                                 FORMAT "99/99/9999"
                                                 HELP "Estimated delivery date.|no|yes"
    FIELD   exp_quantity                     AS  DECIMAL             
                                                 COLUMN-LABEL "Expected Quantity"
                                                 LABEL "Expected Quantity"
                                                 FORMAT ">>>,>>9"
                                                 INIT 0
                                                 HELP "Quantity expected|no|yes"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID|no|no"
    FIELD   GUID1                            AS  DECIMAL             
                                                 COLUMN-LABEL "GUID1"
                                                 LABEL "GUID1"
                                                 FORMAT "99999999.999999999"
                                                 HELP "Enter the GUID1|no|no"
    FIELD   GUID2                            AS  DECIMAL             
                                                 COLUMN-LABEL "GUID2"
                                                 LABEL "GUID2"
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID2|no|no"
    FIELD   host_origin                      AS  CHARACTER           
                                                 COLUMN-LABEL "Host Origin"
                                                 LABEL "Host Origin"
                                                 FORMAT "X(25)"
                                                 INIT ""
                                                 HELP "Enter the Host Origin|no|yes"
    FIELD   item_cost                        AS  DECIMAL             
                                                 COLUMN-LABEL "Cost"
                                                 LABEL "Cost"
                                                 FORMAT "$>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Cost|no|yes"
    FIELD   item_desc                        AS  CHARACTER           
                                                 COLUMN-LABEL "Item Description"
                                                 LABEL "Item Description"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Description for this item.|no|yes"
    FIELD   item_num                         AS  CHARACTER           
                                                 COLUMN-LABEL "Alternate Item Number"
                                                 LABEL "Alternate Item Number"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter this item's catalog number.|no|yes"
    FIELD   line_indicator                   AS  CHARACTER           
                                                 COLUMN-LABEL "line_indicator"
                                                 LABEL "line_indicator"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   line_num                         AS  INTEGER             
                                                 COLUMN-LABEL "Line Number"
                                                 LABEL "Line Number"
                                                 FORMAT ">>>>>>9"
                                                 INIT 0
                                                 HELP "Enter the Line Number|no|yes"
    FIELD   line_sequence                    AS  INTEGER             
                                                 COLUMN-LABEL "Line Sequence"
                                                 LABEL "line_sequence"
                                                 FORMAT ">9"
                                                 INIT 0
                                                 HELP "Enter the line_sequence|no|yes"
    FIELD   lot                              AS  CHARACTER           
                                                 COLUMN-LABEL "Lot"
                                                 LABEL "Lot"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Lot Number.|no|yes"
    FIELD   ordered_qty                      AS  DECIMAL             
                                                 COLUMN-LABEL "Quantity Ordered"
                                                 LABEL "Quantity Ordered"
                                                 FORMAT "->>,>>9.99"
                                                 HELP "Quantity ordered.|no|yes"
    FIELD   packer                           AS  CHARACTER           
                                                 COLUMN-LABEL "Packer"
                                                 LABEL "packer"
                                                 FORMAT "X(14)"
                                                 INIT ""
                                                 HELP "Enter the packer|no|yes"
    FIELD   packing_list                     AS  LOGICAL             
                                                 COLUMN-LABEL "Packing List RT?"
                                                 LABEL "packing_list"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the packing_list|no|yes"
    FIELD   percent_fill                     AS  DECIMAL             
                                                 COLUMN-LABEL "Percent Fill"
                                                 LABEL "Percent Fill"
                                                 FORMAT "->>,>>9.99"
                                                 HELP "Enter the Percent Fill|no|yes"
    FIELD   pool                             AS  CHARACTER           
                                                 COLUMN-LABEL "Pool"
                                                 LABEL "Pool"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Pool|no|yes"
    FIELD   po_line                          AS  INTEGER             
                                                 COLUMN-LABEL "PO Line"
                                                 LABEL "po_line"
                                                 FORMAT ">>>9"
                                                 INIT 0
                                                 HELP "Enter the po_line|no|yes"
    FIELD   po_number                        AS  CHARACTER           
                                                 COLUMN-LABEL "PO Number"
                                                 LABEL "PO Number"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the PO Number|no|yes"
    FIELD   po_suffix                        AS  CHARACTER           
                                                 COLUMN-LABEL "PO Suffix"
                                                 LABEL "PO Suffix"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Enter the PO Suffix|no|yes"
    FIELD   po_type                          AS  CHARACTER           
                                                 COLUMN-LABEL "PO Type"
                                                 LABEL "PO Type"
                                                 FORMAT "XX"
                                                 INIT ""
                                                 HELP "Purchase Order Type.|no|yes"
    FIELD   prod_desc                        AS  CHARACTER           
                                                 COLUMN-LABEL "Product Description"
                                                 LABEL "prod_desc"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   qty_unavail                      AS  DECIMAL             
                                                 COLUMN-LABEL "Unavailable Qty"
                                                 LABEL "qty_unavail"
                                                 FORMAT ">,>>>,>>>.99"
                                                 INIT 0
                                                 HELP "R&D Field|no|no"
    FIELD   quantity                         AS  DECIMAL             
                                                 COLUMN-LABEL "Quantity Left"
                                                 LABEL "Quantity Left"
                                                 FORMAT ">>>,>>9"
                                                 INIT 0
                                                 HELP "Quantity.|no|yes"
    FIELD   rd_po_type                       AS  CHARACTER           
                                                 COLUMN-LABEL "rd_po_type"
                                                 LABEL "rd_po_type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   receiver_num                     AS  CHARACTER           
                                                 COLUMN-LABEL "receiver_num"
                                                 LABEL "receiver_num"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   return_fl                        AS  CHARACTER           
                                                 COLUMN-LABEL "return_fl"
                                                 LABEL "return_fl"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   ret_line                         AS  INTEGER             
                                                 COLUMN-LABEL "Line"
                                                 LABEL "Line"
                                                 FORMAT ">>>9"
                                                 INIT 1
                                                 HELP "R&D Field|no|no"
    FIELD   ret_line_sequence                AS  INTEGER             
                                                 COLUMN-LABEL "ret_line_sequence"
                                                 LABEL "ret_line_sequence"
                                                 FORMAT ">9"
                                                 INIT 0
                                                 HELP "R&D Field|no|no"
    FIELD   row_status                       AS  CHARACTER           
                                                 COLUMN-LABEL "Status"
                                                 LABEL "Status"
                                                 FORMAT "x"
                                                 INIT "O"
                                                 HELP "The status for this R/T line.|no|yes"
    FIELD   rtn_order                        AS  CHARACTER           
                                                 COLUMN-LABEL "Order"
                                                 LABEL "Order"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   rtn_order_suffix                 AS  CHARACTER           
                                                 COLUMN-LABEL "Order Suffix"
                                                 LABEL "Order Suffix"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   rt_id                            AS  INTEGER             
                                                 COLUMN-LABEL "RT ID"
                                                 LABEL "RT Id"
                                                 FORMAT ">>>>>>9"
                                                 INIT 0
                                                 HELP "DO NOT EDIT THIS FIELD -- RT id assigned by IRMS|no|yes"
    FIELD   special_handling                 AS  CHARACTER           
                                                 COLUMN-LABEL "Special Handling"
                                                 LABEL "Special Handling"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "<B>ack-order, <C>ross-docking, <T>ie.|no|yes"
    FIELD   uom                              AS  CHARACTER           
                                                 COLUMN-LABEL "UOM"
                                                 LABEL "Unit of Measure"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Unit of Measure for this RT line.|no|yes"
    FIELD   vendor_id                        AS  CHARACTER           
                                                 COLUMN-LABEL "Vendor ID"
                                                 LABEL "Vendor ID"
                                                 FORMAT "X(9)"
                                                 INIT ""
                                                 HELP "Vendor ID|no|yes"
    FIELD   vend_item                        AS  CHARACTER           
                                                 COLUMN-LABEL "Vendor Item"
                                                 LABEL "Vendor Item"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Vendor-specific item number.|no|yes"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse Number.|no|yes"

                    .
TEMP-TABLE ttrtdetSrch:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsSearchReturnQuantity
    FOR ttrtdetSrch,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


