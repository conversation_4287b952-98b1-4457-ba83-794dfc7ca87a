/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\AssignRMA\AssignRMA_load.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:38 PM
**=================================================================**/


/* Business Entity Definintions */
{AssignRMA/AssignRMA/AssignRMA_ds.i}
{AssignRMA/AssignRMA/AssignRMA_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT        PARAM ipcGUID AS  CHAR .
    DEF       OUTPUT PARAM DATASET FOR dsAssignRMA .


    DEF VAR hDataSet   AS HANDLE   NO-UNDO. 
    DEF VAR cTable     AS CHAR     NO-UNDO. 


    hDataSet = DYNAMIC-FUNCTION('getProperty' IN THIS-PROCEDURE,'DataSetHandle').
    IF hDataSet:NUM-RELATIONS > 0 THEN
       cTable = hDataSet:GET-RELATION(1):PARENT-BUFFER:NAME.
    ELSE
       cTable = hDataSet:GET-BUFFER-HANDLE(1):NAME.


    CREATE ds_Filter.
    ASSIGN
       ds_Filter.TableName  = cTable
       ds_Filter.FieldName  = 'GUID'
       ds_Filter.Operand    = '='
       ds_Filter.FieldValue = ipcGUID
       .


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


