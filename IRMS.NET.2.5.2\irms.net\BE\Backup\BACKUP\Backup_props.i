/**=================================================================**
* Y:\BE_Area\src\be\Backup\Backup\Backup_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:42 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qbkmst FOR bkmst SCROLLING . 
QUERY qbkmst:QUERY-PREPARE("FOR EACH bkmst").
QUERY qbkmst:QUERY-OPEN.


DEF  QUERY qbkmst_BEFORE FOR bkmst_BEFORE SCROLLING . 
QUERY qbkmst_BEFORE:QUERY-PREPARE("FOR EACH bkmst_BEFORE").
QUERY qbkmst_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "bkmst" */
DEFINE BUFFER bkmst_1 FOR irms.bkmst.
DEFINE QUERY qSrcbkmst
    FOR bkmst_1
        SCROLLING.
DEFINE DATA-SOURCE bkmst
    FOR QUERY qSrcbkmst
        bkmst_1 KEYS (GUID)        .
DATA-SOURCE bkmst:PREFER-DATASET = no.
DATA-SOURCE bkmst:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_bkmst                        AS  HANDLE              
    FIELD   hQry_bkmst                       AS  HANDLE              
    FIELD   hQry_bkmst_BEFORE                AS  HANDLE              
    FIELD   bkmst_DataSourceHdl              AS  HANDLE              
    FIELD   bkmst_BatchSize                  AS  INTEGER             
                                                 INIT 50
    FIELD   bkmst_Fill                       AS  LOGICAL             
                                                 INIT yes
    FIELD   bkmst_CanRead                    AS  LOGICAL             
                                                 INIT yes
    FIELD   bkmst_CanCreate                  AS  LOGICAL             
                                                 INIT yes
    FIELD   bkmst_CanUpdate                  AS  LOGICAL             
                                                 INIT yes
    FIELD   bkmst_CanDelete                  AS  LOGICAL             
                                                 INIT yes
    FIELD   bkmst_Src_Names                  AS  CHARACTER           
                                                 INIT ""
    FIELD   bkmst_Src_Hdls                   AS  CHARACTER           
                                                 INIT ""
    FIELD   bkmst_CurrentSource              AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   bkmst_UniqueKey                  AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   bkmst_bkmst_Map                  AS  CHARACTER           
                                                 INIT ""
    FIELD   bkmst_bkmst_CF                   AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   bkmst_bkmst_NoP                  AS  CHARACTER           
                                                 INIT ""
    FIELD   bkmst_hdl                        AS  HANDLE              
    FIELD   bkmst_UseQuery                   AS  LOGICAL             
                                                 INIT yes
    FIELD   bkmst_PostTable                  AS  CHARACTER           
                                                 INIT "bkmst_1"
    FIELD   bkmst_qhdl                       AS  HANDLE              
    FIELD   bkmst_bkmst_1_W                  AS  CHARACTER           
                                                 INIT ""
    FIELD   bkmst_bkmst_1_S                  AS  CHARACTER           
                                                 INIT ""
    FIELD   bkmst_Buffs                      AS  CHARACTER           
                                                 INIT "bkmst_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "bkmst,bkmst"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "bkmst,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsBackup:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_bkmst                        = QUERY qbkmst:HANDLE
       htt_bkmst                         = TEMP-TABLE bkmst:HANDLE
       hQry_bkmst_BEFORE                 = QUERY qbkmst_BEFORE:HANDLE
       bkmst_src_Names                   = 'bkmst,Default'
       bkmst_src_Hdls                    =         STRING(DATA-SOURCE bkmst:HANDLE)
                                           + ',' + STRING(DATA-SOURCE bkmst:HANDLE)
       bkmst_bkmst_Map                   =         'fri_ai,bkmst_1.fri_ai'
                                           + ',' + 'fri_db,bkmst_1.fri_db'
                                           + ',' + 'fri_in,bkmst_1.fri_in'
                                           + ',' + 'GUID,bkmst_1.GUID'
                                           + ',' + 'mon_ai,bkmst_1.mon_ai'
                                           + ',' + 'mon_db,bkmst_1.mon_db'
                                           + ',' + 'mon_in,bkmst_1.mon_in'
                                           + ',' + 'sat_ai,bkmst_1.sat_ai'
                                           + ',' + 'sat_db,bkmst_1.sat_db'
                                           + ',' + 'sat_in,bkmst_1.sat_in'
                                           + ',' + 'sun_ai,bkmst_1.sun_ai'
                                           + ',' + 'sun_db,bkmst_1.sun_db'
                                           + ',' + 'sun_in,bkmst_1.sun_in'
                                           + ',' + 'thu_ai,bkmst_1.thu_ai'
                                           + ',' + 'thu_db,bkmst_1.thu_db'
                                           + ',' + 'thu_in,bkmst_1.thu_in'
                                           + ',' + 'tue_ai,bkmst_1.tue_ai'
                                           + ',' + 'tue_db,bkmst_1.tue_db'
                                           + ',' + 'tue_in,bkmst_1.tue_in'
                                           + ',' + 'wed_ai,bkmst_1.wed_ai'
                                           + ',' + 'wed_db,bkmst_1.wed_db'
                                           + ',' + 'wed_in,bkmst_1.wed_in'
       bkmst_hdl                         = DATA-SOURCE bkmst:HANDLE
       bkmst_qhdl                        = QUERY qSrcbkmst:HANDLE
       TopLevelTables                    = 'bkmst'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/Backup_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackup .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackup BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackup .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackup BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_bkmst_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackup .
     RUN bkmst_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackup BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_bkmst_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackup .
     RUN bkmst_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackup BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_bkmst_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackup .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('bkmst') NO-ERROR .
     RUN bkmst_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackup BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_bkmst_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackup .
     RUN AfterRowFill  IN THIS-PROCEDURE ('bkmst') NO-ERROR .
     RUN bkmst_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackup BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


