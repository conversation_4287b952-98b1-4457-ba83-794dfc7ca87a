/**=================================================================**
* Y:\irms.net.2.5.1\irms.net\BE\backdoorlogin\BackdoorLogin_getschema.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 03/25/08, 08:19 PM
**=================================================================**/


/* Business Entity Definintions */
{backdoorlogin/BackdoorLogin_ds.i}
{backdoorlogin/BackdoorLogin_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Schema.


    RUN Schema_Fill .


/**************************** END OF FILE ****************************/


