/**=================================================================**
* S:\IRMS.NET.2.5.2\irms.net\BE\BatchProcess\BatchBuild\BatchBuild_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 08/19/09, 16:30 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qttBatchBuild FOR ttBatchBuild SCROLLING . 
QUERY qttBatchBuild:QUERY-PREPARE("FOR EACH ttBatchBuild").
QUERY qttBatchBuild:QUERY-OPEN.


DEF  QUERY qttBatchBuild_BEFORE FOR ttBatchBuild_BEFORE SCROLLING . 
QUERY qttBatchBuild_BEFORE:QUERY-PREPARE("FOR EACH ttBatchBuild_BEFORE").
QUERY qttBatchBuild_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.07.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttBatchBuild                 AS  HANDLE              
    FIELD   hQry_ttBatchBuild                AS  HANDLE              
    FIELD   hQry_ttBatchBuild_BEFORE         AS  HANDLE              
    FIELD   ttBatchBuild_DataSourceHdl       AS  HANDLE              
    FIELD   ttBatchBuild_BatchSize           AS  INTEGER             
                                                 INIT 0
    FIELD   ttBatchBuild_Fill                AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBatchBuild_CanRead             AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBatchBuild_CanCreate           AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBatchBuild_CanUpdate           AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBatchBuild_CanDelete           AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBatchBuild_Src_Names           AS  CHARACTER           
                                                 INIT ""
    FIELD   ttBatchBuild_Src_Hdls            AS  CHARACTER           
                                                 INIT ""
    FIELD   ttBatchBuild_CurrentSource       AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   ttBatchBuild_UniqueKey           AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT ""
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "ttBatchBuild,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsBatchBuild:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_ttBatchBuild                 = QUERY qttBatchBuild:HANDLE
       htt_ttBatchBuild                  = TEMP-TABLE ttBatchBuild:HANDLE
       hQry_ttBatchBuild_BEFORE          = QUERY qttBatchBuild_BEFORE:HANDLE
       TopLevelTables                    = 'ttBatchBuild'
       DataSetOneTransaction             = yes
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/BatchBuild_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsBatchBuild .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBatchBuild BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsBatchBuild .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBatchBuild BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttBatchBuild_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsBatchBuild .
     RUN ttBatchBuild_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBatchBuild BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttBatchBuild_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsBatchBuild .
     RUN ttBatchBuild_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBatchBuild BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_ttBatchBuild_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsBatchBuild .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('ttBatchBuild') NO-ERROR .
     RUN ttBatchBuild_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBatchBuild BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttBatchBuild_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsBatchBuild .
     RUN AfterRowFill  IN THIS-PROCEDURE ('ttBatchBuild') NO-ERROR .
     RUN ttBatchBuild_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBatchBuild BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


