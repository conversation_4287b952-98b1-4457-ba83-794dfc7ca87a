/*** 
** Location labels for Intermec printer .
** File name is hard coded in lbl_prnt.w .
***/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/int_locs.p,v 1.5 2000-02-24 11:42:33-06 tanya Exp $"
.

DEF INPUT PARAMETER i_arrow       AS INT   NO-UNDO .
DEF INPUT PARAMETER ch_format     AS CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_value_locs AS CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_printer      AS CHAR  NO-UNDO .
DEF INPUT PARAMETER i_type        AS INT   NO-UNDO .


DEF VAR i_num_ent AS INT  NO-UNDO        .
DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
DEF VAR ch_loc    AS CHAR NO-UNDO        .
DEF VAR ch_print  AS CHAR NO-UNDO        .
DEF VAR ch_file   AS CHAR NO-UNDO        .


MESSAGE "Starting Printing..." .


ASSIGN
   i_num_ent = NUM-ENTRIES ( ch_value_locs )
   .

IF i_num_ent = 0 THEN
DO:
   MESSAGE 'No location label list passed for printing...' .
   
   RETURN .
END.
      
IF ( ch_format = ? ) OR ERROR-STATUS:ERROR THEN
   ASSIGN
      ch_format = ''  . 


IF ch_printer = ? THEN
DO:
   MESSAGE 'No UNIX printer queue specified...' .
   RETURN .
END.

/***
**   Build print File 
***/

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LOC", ".txt", output ch_file ) .

DO WHILE i_num_ent >= i_entry :
 
   ASSIGN
      ch_loc  = ENTRY ( i_entry , ch_value_locs ) .
      i_entry = i_entry + 1 .                          
   MESSAGE 'Generating ' ch_loc  'Label...'.   
      
   RUN print_label ( ch_loc , ch_file ) .
END.

/***
**  Print the File
***/
message "About to print location labels:" ch_file .

if (opsys eq "UNIX")
then do:
    ASSIGN
        ch_print = "lp -d" + ch_printer + " " + ch_file .
    
    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .

    pause 5 no-message .

    OS-DELETE         VALUE ( ch_file    ) .
end .
else do: /*NT*/

     /***************** old code using spooler ***************************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        message "Copying" ch_file "to" ch_spool_dir .
        
        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .

    ****************************************************************/
    
    
       message "copying file  " ch_file "to printer " ch_printer .

       OS-COPY VALUE( ch_file ) VALUE( ch_printer) .

       OS-DELETE  VALUE ( ch_file  ) .   


end .

/* the end */
return .



/* <<<<<<<<<<<<<<<<<<<<<< PROCEDURES >>>>>>>>>>>>>>>>>>>>> */

PROCEDURE print_label:                         
    DEF INPUT PARAMETER ch_loc   AS CHAR NO-UNDO .
    DEF INPUT PARAMETER ch_file  AS CHAR NO-UNDO .

    DEF VAR             ch_value AS CHAR NO-UNDO .

    ASSIGN
       ch_loc = CAPS ( ch_loc ) 
       .
       
    RUN format_string ( ch_loc, OUTPUT ch_value ) .

    OUTPUT TO VALUE ( ch_file )  APPEND. 
    
    CASE i_type :
       WHEN 1 THEN   /* 1x4 Standard Label */
       DO:


          PUT UNFORMATTED
       
              "<STX><ESC>C<ETX>~n"
              "<STX><ESC>P;E3;F3;~<ETX>~n"

              "<STX>L83;o175,000;l796;f3;w4;~<ETX>~n"
              
              "<STX>H00;o000,786;f1;h1;w1;c20;d3,LOCATION:;~<ETX>~n"
              "<STX>H01;o030,766;f1;h3;w1;c21;d0,15;~<ETX>~n"

              "<STX>B100;o05,400;c6,0;f1;h150;w4;d0,10;p@;~<ETX>~n"

              "<STX>R<ETX>~n"
              "<STX><ESC>E3<ETX>~n"
              "<STX><CAN><ETX>~n"
              "<STX>" + ch_value + "<CR><ETX>~n"
              "<STX>" + ch_loc + "<CR><ETX>~n"
       
              "<STX><ETB><ETX>~n"  .
       END .
              
       WHEN 2 THEN       
       DO:   /* 2x4 Narrow Label (1x4 printed on 2x4 size) */

          PUT UNFORMATTED
              
              "<STX><ESC>C<ETX>~n"
              "<STX><ESC>P;E3;F3;~<ETX>~n"  
                           
              "<STX>L83;o175,000;l796;f3;w4;~<ETX>~n"
              "<STX>L84;o000,450;l175;f0;w4;~<ETX>~n"

              "<STX>H00;o000,786;f1;h1;w1;c20;d3,LOCATION:;~<ETX>~n"
              "<STX>H01;o030,766;f1;h3;w1;c21;d0,12;~<ETX>~n"

              "<STX>B100;o05,380;c6,0;f1;h150;w4;d0,8;p@;~<ETX>~n"

              "<STX>R<ETX>~n"
              "<STX><ESC>E3<ETX>~n"
              "<STX><CAN><ETX>~n"
              "<STX>" + ch_value + "<CR><ETX>~n"
              "<STX>" + ch_loc + "<CR><ETX>~n"

              "<STX><ETB><ETX>~n" .
 
       END.       
       
       WHEN 3 THEN
       DO:   /* 2X4 Standard Label*/

          PUT UNFORMATTED 
             "<STX><ESC>C<ETX>~n"
             "<STX><ESC>P;E3;F3;~<ETX>~n" .
           
          CASE i_arrow:   /* ARROW UP*/
              WHEN 1 THEN
              DO:
              
                 PUT UNFORMATTED  
                            
                    "<STX>W50;o65,70;w50;l80;h60;~<ETX>~n" 
       
                    "<STX>L51;o05,101;l002;f1;w2;~<ETX>~n"
                    "<STX>L52;o07,103;l006;f1;w2;~<ETX>~n"
                    "<STX>L53;o09,105;l010;f1;w2;~<ETX>~n"
                    "<STX>L54;o11,107;l014;f1;w2;~<ETX>~n"
                    "<STX>L55;o13,109;l018;f1;w2;~<ETX>~n"
                    "<STX>L56;o15,111;l022;f1;w2;~<ETX>~n"
                    "<STX>L57;o17,113;l026;f1;w2;~<ETX>~n"
                    "<STX>L58;o19,115;l030;f1;w2;~<ETX>~n"
                    "<STX>L59;o21,117;l034;f1;w2;~<ETX>~n"
                    "<STX>L60;o23,119;l038;f1;w2;~<ETX>~n"
                    "<STX>L61;o25,121;l042;f1;w2;~<ETX>~n"
                    "<STX>L62;o27,123;l046;f1;w2;~<ETX>~n"
                    "<STX>L63;o29,125;l050;f1;w2;~<ETX>~n"
                    "<STX>L64;o31,127;l054;f1;w2;~<ETX>~n"
                    "<STX>L65;o33,129;l058;f1;w2;~<ETX>~n"
                    "<STX>L66;o35,131;l062;f1;w2;~<ETX>~n"
                    "<STX>L67;o37,133;l066;f1;w2;~<ETX>~n"
                    "<STX>L69;o39,135;l070;f1;w2;~<ETX>~n"
                    "<STX>L70;o41,137;l074;f1;w2;~<ETX>~n"
                    "<STX>L71;o43,139;l078;f1;w2;~<ETX>~n"
                    "<STX>L72;o45,141;l082;f1;w2;~<ETX>~n"
                    "<STX>L73;o47,143;l086;f1;w2;~<ETX>~n"
                    "<STX>L74;o49,145;l090;f1;w2;~<ETX>~n"
                    "<STX>L75;o51,147;l094;f1;w2;~<ETX>~n"
                    "<STX>L76;o53,149;l098;f1;w2;~<ETX>~n"
                    "<STX>L77;o55,151;l102;f1;w2;~<ETX>~n"
                    "<STX>L78;o57,153;l106;f1;w2;~<ETX>~n"
                    "<STX>L79;o59,155;l110;f1;w2;~<ETX>~n"
                    "<STX>L80;o61,157;l114;f1;w2;~<ETX>~n"
                    "<STX>L81;o63,159;l118;f1;w2;~<ETX>~n"
                    "<STX>L82;o65,161;l122;f1;w2;~<ETX>~n" 

                    "<STX>L83;o000,200;l175;f0;w4;~<ETX>~n" .

              END.
               
               
              WHEN 2 THEN   /* ARROW DOWN */
              DO:

                 PUT UNFORMATTED
    
                    "<STX>W50;o05,70;w50;l80;h60;~<ETX>~n" 

                    "<STX>L51;o145,101;l002;f1;w2;~<ETX>~n"
                    "<STX>L52;o143,103;l006;f1;w2;~<ETX>~n"
                    "<STX>L53;o141,105;l010;f1;w2;~<ETX>~n"
                    "<STX>L54;o139,107;l014;f1;w2;~<ETX>~n"
                    "<STX>L55;o137,109;l018;f1;w2;~<ETX>~n"
                    "<STX>L56;o135,111;l022;f1;w2;~<ETX>~n"
                    "<STX>L57;o133,113;l026;f1;w2;~<ETX>~n"
                    "<STX>L58;o131,115;l030;f1;w2;~<ETX>~n"
                    "<STX>L59;o129,117;l034;f1;w2;~<ETX>~n"
                    "<STX>L60;o127,119;l038;f1;w2;~<ETX>~n"
                    "<STX>L61;o125,121;l042;f1;w2;~<ETX>~n"
                    "<STX>L62;o123,123;l046;f1;w2;~<ETX>~n"
                    "<STX>L63;o121,125;l050;f1;w2;~<ETX>~n"
                    "<STX>L64;o119,127;l054;f1;w2;~<ETX>~n"
                    "<STX>L65;o117,129;l058;f1;w2;~<ETX>~n"
                    "<STX>L66;o115,131;l062;f1;w2;~<ETX>~n"
                    "<STX>L67;o113,133;l066;f1;w2;~<ETX>~n"
                    "<STX>L68;o111,135;l070;f1;w2;~<ETX>~n"
                    "<STX>L69;o109,137;l074;f1;w2;~<ETX>~n"
                    "<STX>L70;o107,139;l078;f1;w2;~<ETX>~n"
                    "<STX>L71;o105,141;l082;f1;w2;~<ETX>~n"
                    "<STX>L72;o103,143;l086;f1;w2;~<ETX>~n"
                    "<STX>L73;o101,145;l090;f1;w2;~<ETX>~n"
                    "<STX>L74;o099,147;l094;f1;w2;~<ETX>~n"
                    "<STX>L75;o097,149;l098;f1;w2;~<ETX>~n"
                    "<STX>L76;o095,151;l102;f1;w2;~<ETX>~n"
                    "<STX>L78;o093,153;l106;f1;w2;~<ETX>~n"
                    "<STX>L79;o091,155;l110;f1;w2;~<ETX>~n"
                    "<STX>L80;o089,157;l114;f1;w2;~<ETX>~n"
                    "<STX>L81;o087,159;l118;f1;w2;~<ETX>~n"
                    "<STX>L82;o085,161;l122;f1;w2;~<ETX>~n" 

                    "<STX>L83;o000,200;l175;f0;w4;~<ETX>~n" .

              END.
          END CASE .

          PUT UNFORMATTED      /*PRINT LOCATION AND BAR CODE */


              "<STX>L84;o175,000;l796;f3;w4;~<ETX>~n"
            
              "<STX>H00;o000,786;f1;h1;w1;c21;d3,LOCATION:;~<ETX>~n"
              "<STX>H01;o030,726;f1;h2;w1;c22;d0,14;~<ETX>~n"

              "<STX>B100;o200,710;c6,0;f1;h150;w5;d0,10;p@;~<ETX>~n"


              "<STX>R<ETX>~n"
              "<STX><ESC>E3<ETX>~n"

              "<STX><CAN><ETX>~n"
              "<STX>" + ch_value + "<CR><ETX>~n"
              "<STX>" + ch_loc + "<CR><ETX>~n"
              "<STX><ETB><ETX>" .

          END.
      WHEN 4 THEN     /* 4X6 Standard Label*/
      DO:
          PUT UNFORMATTED  
             "<STX><ESC>C<ETX>~n"
             "<STX><ESC>P;E3;F3;~<ETX>~n" .
 
          CASE i_arrow :
             WHEN 1 THEN     /* ARROW UP */

                PUT UNFORMATTED

                   "<STX>W50;o950,164;w100;l110;h165;~<ETX>~n"
              
                   "<STX>L051;o1004,060;l002;f0;w2;~<ETX>~n"
                   "<STX>L052;o1002,062;l006;f0;w2;~<ETX>~n"
                   "<STX>L053;o1000,064;l010;f0;w2;~<ETX>~n"
                   "<STX>L054;o0998,066;l014;f0;w2;~<ETX>~n"
                   "<STX>L055;o0996,068;l018;f0;w2;~<ETX>~n"
                   "<STX>L056;o0994,070;l022;f0;w2;~<ETX>~n"
                   "<STX>L057;o0992,072;l026;f0;w2;~<ETX>~n"
                   "<STX>L058;o0990,074;l030;f0;w2;~<ETX>~n"
                   "<STX>L059;o0988,076;l034;f0;w2;~<ETX>~n"
                   "<STX>L060;o0986,078;l038;f0;w2;~<ETX>~n"
                   "<STX>L061;o0984,080;l042;f0;w2;~<ETX>~n"
                   "<STX>L062;o0982,082;l046;f0;w2;~<ETX>~n"
                   "<STX>L063;o0980,084;l050;f0;w2;~<ETX>~n"
                   "<STX>L064;o0978,086;l054;f0;w2;~<ETX>~n"
                   "<STX>L065;o0976,088;l058;f0;w2;~<ETX>~n"
                   "<STX>L066;o0974,090;l062;f0;w2;~<ETX>~n"
                   "<STX>L067;o0972,092;l066;f0;w2;~<ETX>~n"
                   "<STX>L068;o0970,094;l070;f0;w2;~<ETX>~n"
                   "<STX>L069;o0968,096;l074;f0;w2;~<ETX>~n"
                   "<STX>L070;o0966,098;l078;f0;w2;~<ETX>~n"
                   "<STX>L071;o0964,100;l082;f0;w2;~<ETX>~n"
                   "<STX>L072;o0962,102;l086;f0;w2;~<ETX>~n"
                   "<STX>L073;o0960,104;l090;f0;w2;~<ETX>~n"
                   "<STX>L074;o0958,106;l094;f0;w2;~<ETX>~n"
                   "<STX>L075;o0956,108;l098;f0;w2;~<ETX>~n"
                   "<STX>L076;o0954,110;l102;f0;w2;~<ETX>~n"
                   "<STX>L077;o0952,112;l106;f0;w2;~<ETX>~n"
                   "<STX>L078;o0950,114;l110;f0;w2;~<ETX>~n"
                   "<STX>L079;o0948,116;l114;f0;w2;~<ETX>~n"
                   "<STX>L080;o0946,118;l118;f0;w2;~<ETX>~n"
                   "<STX>L081;o0944,120;l122;f0;w2;~<ETX>~n"
                   "<STX>L082;o0942,122;l126;f0;w2;~<ETX>~n"
                   "<STX>L083;o0940,124;l130;f0;w2;~<ETX>~n"
                   "<STX>L084;o0938,126;l134;f0;w2;~<ETX>~n"
                   "<STX>L085;o0936,128;l138;f0;w2;~<ETX>~n"
                   "<STX>L086;o0934,130;l142;f0;w2;~<ETX>~n"
                   "<STX>L087;o0932,132;l146;f0;w2;~<ETX>~n"
                   "<STX>L088;o0930,134;l150;f0;w2;~<ETX>~n"
                   "<STX>L089;o0928,136;l154;f0;w2;~<ETX>~n"
                   "<STX>L090;o0926,138;l158;f0;w2;~<ETX>~n"
                   "<STX>L091;o0924,140;l162;f0;w2;~<ETX>~n"
                   "<STX>L092;o0922,142;l166;f0;w2;~<ETX>~n"
                   "<STX>L093;o0920,144;l170;f0;w2;~<ETX>~n"
                   "<STX>L094;o0918,146;l174;f0;w2;~<ETX>~n"
                   "<STX>L095;o0916,148;l178;f0;w2;~<ETX>~n"
                   "<STX>L096;o0914,150;l182;f0;w2;~<ETX>~n"
                   "<STX>L097;o0912,152;l186;f0;w2;~<ETX>~n"
                   "<STX>L098;o0910,154;l190;f0;w2;~<ETX>~n"
                   "<STX>L099;o0908,156;l194;f0;w2;~<ETX>~n"
                   "<STX>L100;o0906,158;l198;f0;w2;~<ETX>~n"
                   "<STX>L101;o0904,160;l202;f0;w2;~<ETX>~n"
                   "<STX>L102;o0902,162;l206;f0;w2;~<ETX>~n"
                   "<STX>L103;o0900,164;l210;f0;w2;~<ETX>~n" 

                   "<STX>L104;o800,000;l0398;f3;w2;~<ETX>~n" .

               WHEN 2 THEN      /*ARROW DOWN*/

                  PUT UNFORMATTED

                     "<STX>W50;o950,59;w100;l110;h165;~<ETX>~n"

                     "<STX>L051;o1004,334;l002;f0;w2;~<ETX>~n"
                     "<STX>L052;o1002,332;l006;f0;w2;~<ETX>~n"
                     "<STX>L053;o1000,330;l010;f0;w2;~<ETX>~n"
                     "<STX>L054;o0998,328;l014;f0;w2;~<ETX>~n"
                     "<STX>L055;o0996,326;l018;f0;w2;~<ETX>~n"
                     "<STX>L056;o0994,324;l022;f0;w2;~<ETX>~n"
                     "<STX>L057;o0992,322;l026;f0;w2;~<ETX>~n"
                     "<STX>L058;o0990,320;l030;f0;w2;~<ETX>~n"
                     "<STX>L059;o0988,318;l034;f0;w2;~<ETX>~n"
                     "<STX>L060;o0986,316;l038;f0;w2;~<ETX>~n"
                     "<STX>L061;o0984,314;l042;f0;w2;~<ETX>~n"
                     "<STX>L062;o0982,312;l046;f0;w2;~<ETX>~n"
                     "<STX>L063;o0980,310;l050;f0;w2;~<ETX>~n"
                     "<STX>L064;o0978,308;l054;f0;w2;~<ETX>~n"
                     "<STX>L065;o0976,306;l058;f0;w2;~<ETX>~n"
                     "<STX>L066;o0974,304;l062;f0;w2;~<ETX>~n"
                     "<STX>L067;o0972,302;l066;f0;w2;~<ETX>~n"
                     "<STX>L068;o0970,300;l070;f0;w2;~<ETX>~n"
                     "<STX>L069;o0968,298;l074;f0;w2;~<ETX>~n"
                     "<STX>L070;o0966,296;l078;f0;w2;~<ETX>~n"
                     "<STX>L071;o0964,294;l082;f0;w2;~<ETX>~n"
                     "<STX>L072;o0962,292;l086;f0;w2;~<ETX>~n"
                     "<STX>L073;o0960,290;l090;f0;w2;~<ETX>~n"
                     "<STX>L074;o0958,288;l094;f0;w2;~<ETX>~n"
                     "<STX>L075;o0956,286;l098;f0;w2;~<ETX>~n"
                     "<STX>L076;o0954,284;l102;f0;w2;~<ETX>~n"
                     "<STX>L077;o0952,282;l106;f0;w2;~<ETX>~n"
                     "<STX>L078;o0950,280;l110;f0;w2;~<ETX>~n"
                     "<STX>L079;o0948,278;l114;f0;w2;~<ETX>~n"
                     "<STX>L080;o0946,276;l118;f0;w2;~<ETX>~n"
                     "<STX>L081;o0944,274;l122;f0;w2;~<ETX>~n"
                     "<STX>L082;o0942,272;l126;f0;w2;~<ETX>~n"
                     "<STX>L083;o0940,270;l130;f0;w2;~<ETX>~n"
                     "<STX>L084;o0938,268;l134;f0;w2;~<ETX>~n"
                     "<STX>L085;o0936,266;l138;f0;w2;~<ETX>~n"
                     "<STX>L086;o0934,264;l142;f0;w2;~<ETX>~n"
                     "<STX>L087;o0932,262;l146;f0;w2;~<ETX>~n"
                     "<STX>L088;o0930,260;l150;f0;w2;~<ETX>~n"
                     "<STX>L089;o0928,258;l154;f0;w2;~<ETX>~n"
                     "<STX>L090;o0926,256;l158;f0;w2;~<ETX>~n"
                     "<STX>L091;o0924,254;l162;f0;w2;~<ETX>~n"
                     "<STX>L092;o0922,252;l166;f0;w2;~<ETX>~n"
                     "<STX>L093;o0920,250;l170;f0;w2;~<ETX>~n"
                     "<STX>L094;o0918,248;l174;f0;w2;~<ETX>~n"
                     "<STX>L095;o0916,246;l178;f0;w2;~<ETX>~n"
                     "<STX>L096;o0914,244;l182;f0;w2;~<ETX>~n"
                     "<STX>L097;o0912,242;l186;f0;w2;~<ETX>~n"
                     "<STX>L098;o0910,240;l190;f0;w2;~<ETX>~n"
                     "<STX>L099;o0908,238;l194;f0;w2;~<ETX>~n"
                     "<STX>L100;o0906,236;l198;f0;w2;~<ETX>~n"
                     "<STX>L101;o0904,234;l202;f0;w2;~<ETX>~n"
                     "<STX>L102;o0902,232;l206;f0;w2;~<ETX>~n"
                     "<STX>L103;o0900,230;l210;f0;w2;~<ETX>~n"
                     "<STX>L104;o0898,228;l214;f0;w2;~<ETX>~n" 
                     "<STX>L105;o0896,226;l218;f0;w2;~<ETX>~n" 
                     "<STX>L106;o0894,224;l222;f0;w2;~<ETX>~n"
             
                     "<STX>L107;o800,000;l0398;f3;w2;~<ETX>~n" .

          END CASE .
           
          /* PRINT LOCATION AND BAR CODE */
      
          PUT UNFORMATTED

             "<STX>L108;o000,398;l1200;f0;w2;~<ETX>~n"
                            
             "<STX>H000;o010,010;f0;h1;w1;c22;d3,LOCATION:;~<ETX>~n"
             "<STX>H001;o010,090;f0;h3;w1;c22;d0,14;~<ETX>~n"

             "<STX>B109;o070,450;f0;h300;w8;c6,0;d0,10;~<ETX>~n"

             "<STX>R<ETX>~n"
             "<STX><ESC>E3<ETX>~n"
             "<STX><CAN><ETX>~n"
             "<STX>" + ch_value + "<CR><ETX>~n"
             "<STX>" + ch_loc + "<CR><ETX>~n"
             "<STX><ETB><ETX>" .

       END.   
   END CASE .
END PROCEDURE.


PROCEDURE format_string :
/* -----------------------------------------------------------
  Purpose:    Format a location string ...
  Parameters: Old Location (in) New formated Location (out)
  Notes:
-------------------------------------------------------------*/
 DEF INPUT  PARAMETER ch_loc    AS CHAR NO-UNDO.
 DEF OUTPUT PARAMETER ch_newloc AS CHAR NO-UNDO .
     
 DEF VAR ch_build    AS CHAR NO-UNDO .
 DEF VAR i_charpos   AS INT  NO-UNDO .
 DEF VAR i_numdashes AS INT  NO-UNDO .
 DEF VAR i_length    AS INT  NO-UNDO .
 DEF VAR i_count     AS INT  NO-UNDO .
                         
   ASSIGN
      i_numdashes = NUM-ENTRIES ( ch_format )
      ch_newloc   = ch_loc          
      .
      
   IF i_numdashes < 1 THEN
      RETURN .
               
   buildloop:
   REPEAT WHILE i_count < i_numdashes :
                     
      ASSIGN
         i_count   = i_count + 1
         i_length  = LENGTH ( ch_newloc )
         i_charpos = INT ( ENTRY (  i_count , ch_format ) )
         NO-ERROR .
    
         IF ERROR-STATUS:ERROR THEN
         DO:
            MESSAGE "Problem with format string.  Please validate the format" +              "string in system parameters."
            .
            RETURN .
         END .
                                         
         IF i_charpos > i_length THEN
            NEXT buildloop .
                                                        
         ASSIGN
            ch_newloc = SUBSTRING ( ch_newloc, 1, i_charpos - 1 ) + '-' +
            SUBSTRING ( ch_newloc, i_charpos )
            .
      END.
                                             
END PROCEDURE.
                                                                  
