    DEFINE INPUT PARAMETER ipordguid AS DECIMAL NO-UNDO.
    DEFINE INPUT PARAMETER ipconum AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipwhnum AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER iprmanum AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipretitem AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipretitemdesc AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipnodetail AS LOGICAL NO-UNDO.
    DEFINE INPUT PARAMETER ipthert AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipretqty AS DECIMAL NO-UNDO.
    DEFINE INPUT PARAMETER iprtnreason AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipguids AS CHARACTER NO-UNDO.
    DEFINE OUTPUT PARAMETER operrmessage AS CHARACTER NO-UNDO.

    DEFINE VAR dtl_line AS INTEGER NO-UNDO.
    DEFINE VAR ictr AS INTEGER NO-UNDO.
    DEFINE VAR rtn_reason AS CHARACTER NO-UNDO.
    DEFINE VAR vid AS INTEGER NO-UNDO.
    
    /* find orddtl */
    FIND FIRST IRMS.ordhdr WHERE IRMS.ordhdr.guid = ipordguid NO-LOCK NO-ERROR.
    IF AVAILABLE IRMS.ordhdr THEN DO:
        FIND IRMS.orddtl WHERE IRMS.orddtl.id = IRMS.ordhdr.id AND IRMS.orddtl.abs_num = ipretitem NO-LOCK NO-ERROR.
        IF NOT AVAILABLE IRMS.orddtl THEN DO:
            operrmessage = "Order detail record is not available.".
            RETURN.    
        END.
        vid = IRMS.ordhdr.id.
    END.
    ELSE DO:
        operrmessage = "Order detail record is not available.".
        RETURN.    
    END.

    /* now we have a header to work with */
    
    FIND IRMS.rtmst WHERE IRMS.rtmst.co_num = ipconum AND IRMS.rtmst.wh_num = ipwhnum AND IRMS.rtmst.rt_num = iprmanum NO-LOCK NO-ERROR.
    IF NOT AVAILABLE IRMS.rtmst THEN DO:
        operrmessage = "No R/T record available.".
        RETURN.
    END.

    /* find the last line number */
    /***
    IF NOT ipnodetail THEN DO:
    ***/
        FOR EACH IRMS.rtdet WHERE IRMS.rtdet.rt_id = IRMS.rtmst.rt_id NO-LOCK: 
            ASSIGN dtl_line = IRMS.rtdet.line_num.
        END.
    /***
    END.
    ****/

    DO TRANSACTION:
        IF NUM-ENTRIES(ipguids) <> 0 THEN DO:
            DO ictr = 1 TO NUM-ENTRIES(ipguids):
                FIND FIRST IRMS.serial_history WHERE IRMS.serial_history.GUID = DECIMAL(ENTRY(ictr,ipguids)) EXCLUSIVE-LOCK NO-ERROR.
                IF AVAILABLE IRMS.serial_history THEN DO:
                    ASSIGN IRMS.serial_history.rma = ipthert.
                END.
            END.
        END.

        FIND IRMS.rtdet WHERE IRMS.rtdet.rt_id = IRMS.rtmst.rt_id AND IRMS.rtdet.abs_num = ipretitem EXCLUSIVE-LOCK NO-ERROR.
        IF LOCKED IRMS.rtdet THEN DO:
            operrmessage = "Receipts record is in use. Please try later.".
            RETURN.
        END.
        IF NOT AVAILABLE IRMS.rtdet THEN DO:
            IF dtl_line = 0 THEN DO:
                CREATE IRMS.rtdet.
                ASSIGN IRMS.rtdet.rt_id     = IRMS.rtmst.rt_id
                   IRMS.rtdet.co_num = ipconum 
                   IRMS.rtdet.wh_num = ipwhnum /*SRI20060526*/
                   IRMS.rtdet.line_num  = 1
                   IRMS.rtdet.abs_num   = ipretitem
                   IRMS.rtdet.item_desc = ipretitemdesc.
            END. 
            ELSE DO:
                CREATE IRMS.rtdet.
                ASSIGN IRMS.rtdet.rt_id     = IRMS.rtmst.rt_id
                   IRMS.rtdet.co_num = ipconum 
                   IRMS.rtdet.wh_num = ipwhnum /*SRI20060526*/
                   IRMS.rtdet.line_num  = dtl_line + 1
                   IRMS.rtdet.abs_num   = ipretitem
                   IRMS.rtdet.item_desc = ipretitemdesc.
            END.
        END.

        /* update orddtl line */

        FIND IRMS.orddtl WHERE IRMS.orddtl.id = vid AND IRMS.orddtl.abs_num = ipretitem EXCLUSIVE-LOCK NO-ERROR.
        IF AVAILABLE IRMS.orddtl THEN DO:
            IF LOCKED IRMS.orddtl THEN DO:
                operrmessage = "Order Detail record is in use. Please try later.".
                RETURN.
            END.
            IRMS.orddtl.ret_qty = IRMS.orddtl.ret_qty + ipretqty.
            RELEASE IRMS.orddtl. 
        END.
        ELSE DO: 
            operrmessage = "Order detail record is not available.".
            RETURN.    
        END.

        ASSIGN 
            IRMS.rtdet.exp_quantity = IRMS.rtdet.exp_quantity + ipretqty
            IRMS.rtdet.quantity     = IRMS.rtdet.quantity + ipretqty 
            IRMS.rtdet.po_number    = IRMS.rtmst.order 
            IRMS.rtdet.comments     = iprtnreason.
        
        RELEASE IRMS.rtdet.
    END.

