"ttTablePropsCREATE"
"ttTablePropsTblTableNameinv_adj"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKeyGUID"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNameadj_code"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(6)"
"ttFieldPropsFldSideLabelAdjustment Code"
"ttFieldPropsFldColLabeladj code"
"ttFieldPropsFldHelpEnter the Adjustment Code"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNameadj_desc"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelDescription"
"ttFieldPropsFldColLabeldesc"
"ttFieldPropsFldHelpEnter the Description"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the Company"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustom Fields"
"ttFieldPropsFldColLabelCustom Fields"
"ttFieldPropsFldHelpEnter the Custom Fields"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNameenter_loc"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelLocation Required"
"ttFieldPropsFldColLabelLocation Required"
"ttFieldPropsFldHelpEnter the Location Required"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat*********.*********"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNamehost_origin"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(25)"
"ttFieldPropsFldSideLabelHost Origin"
"ttFieldPropsFldColLabelHost Origin"
"ttFieldPropsFldHelpEnter the Host Origin"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNamestatus_1"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelstatus_1"
"ttFieldPropsFldColLabelstatus_1"
"ttFieldPropsFldHelpEnter the status_1"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNamestatus_2"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelstatus_2"
"ttFieldPropsFldColLabelstatus_2"
"ttFieldPropsFldHelpEnter the status_2"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNametest"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(1)"
"ttFieldPropsFldSideLabeltest"
"ttFieldPropsFldColLabeltest"
"ttFieldPropsFldHelpEnter the test"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNametran_types"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(50)"
"ttFieldPropsFldSideLabelTransaction Types"
"ttFieldPropsFldColLabelTransaction Types"
"ttFieldPropsFldHelpEnter the Transaction Types"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNamevalid_status_1"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelvalid_status_1"
"ttFieldPropsFldColLabelvalid_status_1"
"ttFieldPropsFldHelpEnter the valid_status_1"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNamevalid_status_2"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelvalid_status_2"
"ttFieldPropsFldColLabelvalid_status_2"
"ttFieldPropsFldHelpEnter the valid_status_2"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameinv_adj"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse"
"ttDataSourceCREATE"
"ttDataSourceDSrcNameinv_adj"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTableinv_adj_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNameinv_adj"
"ttDataSourceJoinscDSTableinv_adj"
"ttDataSourceJoinscDBTableirms.inv_adj"
"ttDataSourceJoinscBufNameinv_adj_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num,adj_code"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextyes"
"ttOptionsmakePrevyes"
"ttOptionsmakeLastyes"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionno"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTableinv_adj"
"ttAttachSourcecSrcNameinv_adj"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsadj_code,inv_adj_1.adj_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsadj_desc,inv_adj_1.adj_desc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsco_num,inv_adj_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsenter_loc,inv_adj_1.enter_loc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsGUID,inv_adj_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldshost_origin,inv_adj_1.host_origin"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsstatus_1,inv_adj_1.status_1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsstatus_2,inv_adj_1.status_2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldstran_types,inv_adj_1.tran_types"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsvalid_status_1,inv_adj_1.valid_status_1"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldsvalid_status_2,inv_adj_1.valid_status_2"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableinv_adj"
"ttAttachDtlcSrcNameinv_adj"
"ttAttachDtlMappedFieldswh_num,inv_adj_1.wh_num"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"