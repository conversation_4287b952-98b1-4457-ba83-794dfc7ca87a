/**=================================================================**
* Y:\irms.net.1.3.1\irms.net\BE\Audit\Search\Search_First.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 02/26/14, 17:20 PM
**=================================================================**/


/* Business Entity Definintions */
{Audit/Search/Search_ds.i}
{Audit/Search/Search_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF       OUTPUT PARAM DATASET FOR dsSearch .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN
        CREATE ds_Control.
    ASSIGN ds_Control.PropName = 'COMMAND'
           ds_Control.PropValue = 'NEXT'.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


