/****  create carton master and detail for pick ***/
/**
***  FILE : ctn_rjr.p 
***  For RJR cartonization 
***  INPUT 
***             co_wh_wave_order_reprint
*/
    define input parameter my_co    as character no-undo .
    define input parameter my_wh    as character no-undo .
    define input parameter wave     as integer   no-undo .
    define input parameter ord_id   as integer   no-undo .
    define input parameter re_print as character no-undo .
       
/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: $~n"
.       

/* <<<<<<<<<<  BUFFER  >>>>>>>>>> */
    define buffer bf_ord  for irms.ordhdr .
    
/* <<<<<<<<<< VARIABLE >>>>>>>>>> */

/* <<<<<<<<<<BEGINNING>>>>>>>>>> */
    if (wave EQ 0 OR wave EQ ? ) AND (ord_id EQ 0 OR ord_id EQ ?)
    then do:
        message "Invalid input parameter." .
        return error .
    end .
    if wave GT 0 
    then do :
        for each bf_ord no-lock 
            where 
                bf_ord.co_num = my_co and 
                bf_ord.wh_num = my_wh and 
                bf_ord.batch  = wave
        :
            run ord_pick (input bf_ord.id) no-error .
            release bf_ord no-error .
        end .
    end .
    if ord_id GT 0 
    then do:
        run ord_pick (input ord_id) no-error .
    end .
    return .
/* <<<<<<<<<<PROCEDURE>>>>>>>>> */
procedure ord_pick :
    define input parameter my_id as integer no-undo .

    /* <<<<<<<<<<BUFFER>>>>>>>>>> */
    define buffer bf_ord  for irms.ordhdr .
    define buffer bf_pick for irms.pick   .
    define buffer bf_cmst for irms.cartonmst .
    define buffer bf_cdtl for irms.cartondtl .
    define buffer bf_size for irms.carton_size .
    define buffer bf_item for irms.item .
    
    /* <<<<<<<<<<VARIABLE>>>>>>>>>> */
    define variable count as integer initial 0 no-undo .

    find first bf_ord no-lock 
        where 
            bf_ord.id = my_id 
        no-error 
    .
    if not available (bf_ord)
    then do:
        message "Invalid Order ID." .
    end .
    for each bf_pick exclusive-lock 
        where 
            bf_pick.co_num = my_co and 
            bf_pick.wh_num = my_wh and 
            bf_pick.order  = bf_ord.order and 
            bf_pick.order_suffix = bf_ord.order_suffix
    :
        assign count = count + 1 .
        if bf_pick.qty EQ 0 
        then do:
            next .
        end .
        if trim(bf_pick.carton_id) EQ ""
        then do:
            create bf_cmst no-error .
            assign 
                bf_cmst.co_num  = my_co 
                bf_cmst.wh_num  = my_wh 
                bf_cmst.carton_id = "C" + 
                        string(next-value(cartonmst_carton_id), "999999999")
                bf_cmst.tracking_id  = "" 
                bf_cmst.order        = bf_ord.order
                bf_cmst.order_suffix = bf_ord.order_suffix
                bf_cmst.carrier_id   = TRIM( bf_ord.carrier ) + "/" 
                                        + TRIM( bf_ord.service )
                bf_cmst.full         = TRUE
                bf_cmst.batch        = bf_pick.batch 
            .
            create bf_cdtl no-error .
            assign 
                bf_cdtl.carton_num = bf_cmst.carton_num 
                bf_cdtl.abs_num    = bf_pick.abs_num 
                bf_cdtl.qty        = bf_pick.qty
                bf_cdtl.lot        = bf_pick.lot 
                bf_cdtl.pool       = bf_pick.pool
                bf_cdtl.bin_num    = bf_pick.bin_num
                bf_cdtl.pick_id    = bf_pick.id  
                bf_cdtl.row_status = "O" 

                bf_pick.carton_id = bf_cmst.carton_id 
            .
            release bf_cdtl no-error .
            release bf_cmst no-error .
        end .
        release bf_pick no-error .
    end .
    if count EQ 0 
    then do:
        message "No pick for order" string(bf_ord.order) .
    end .
    else do:
        run pic_lbl.p (input my_id) no-error .
    end .
    release bf_ord no-error .
    return .
end procedure .
/* <<<<<<<<<<  END >>>>>>>>>> */
