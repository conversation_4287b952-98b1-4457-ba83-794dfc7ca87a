/**=================================================================**
* Y:\irms.net\src\be\Audit\EmpTransSum\EmpTransSum_proxy.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 12/07/04, 15:30 PM
**=================================================================**/


/* Business Entity Definintions */
{Audit\EmpTransSum\EmpTransSum_ds.i}
{Audit\EmpTransSum\EmpTransSum_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsEmpTransSum .


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


