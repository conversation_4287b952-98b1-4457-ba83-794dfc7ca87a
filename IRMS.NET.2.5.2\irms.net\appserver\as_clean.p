/* Clean Up Memory Attempt */

DEFINE VARIABLE hTemp   AS HANDLE    NO-UNDO.
DEFINE VARIABLE hObject AS HANDLE    NO-UNDO.
DEFINE VARIABLE vTemp   AS CHARACTER NO-UNDO.

/****
ASSIGN hObject = SESSION:FIRST-DATASET.
DO WHILE hObject <> ?:
    ASSIGN hTemp   = hObject
           hObject = hObject:NEXT-SIBLING.
    
    MESSAGE 'ProDataSet, Handle=' hTemp
                        ', Name=' hTemp:NAME
                     ', Dynamic=' hTemp:DYNAMIC VIEW-AS ALERT-BOX.
END.

ASSIGN hObject = SESSION:FIRST-DATA-SOURCE.
DO WHILE hObject <> ?:
    ASSIGN hTemp   = hObject
           hObject = hObject:NEXT-SIBLING
           vTemp   = (IF hTemp:QUERY = ? THEN ? ELSE hTemp:QUERY:PREPARE-STRING).
    MESSAGE 'DataSource, Handle=' hTemp
                        ', Name=' hTemp:NAME
                       ', Query=' vTemp VIEW-AS ALERT-BOX.
END.

ASSIGN hObject = SESSION:FIRST-BUFFER.
DO WHILE hObject <> ?:
    ASSIGN hTemp   = hObject
           hObject = hObject:NEXT-SIBLING.
    MESSAGE 'Buffer, Handle=' hTemp
                    ', Name=' hTemp:NAME
                   ', Table=' hTemp:TABLE
                 ', Dynamic=' hTemp:DYNAMIC
                 ', DataSet=' hTemp:DATASET VIEW-AS ALERT-BOX.
END.
*****/

ASSIGN hObject = SESSION:FIRST-PROCEDURE.
DO WHILE hObject <> ?:
    ASSIGN hTemp   = hObject
           hObject = hObject:NEXT-SIBLING.
    
    IF htemp:NAME = "session/session_super.p" THEN NEXT.

    IF VALID-HANDLE(htemp) AND htemp:PERSISTENT THEN
        DELETE PROCEDURE htemp.
END.

/****
ASSIGN hObject = SESSION:FIRST-QUERY.
DO WHILE hObject <> ?:
    ASSIGN hTemp   = hObject
           hObject = hObject:NEXT-SIBLING.
    MESSAGE 'Query, Handle=' hTemp
                   ', Name=' hTemp:NAME
                ', Dynamic=' hTemp:DYNAMIC
                  ', Query=' hTemp:PREPARE-STRING VIEW-AS ALERT-BOX.
END.
*********/
     
