/***
** File: prntloop.p 
** 09sep97: <PERSON> <PERSON> <PERSON>
**
**  This program will do the following...
**    1) Print the file to the next open printer by printer form.
**        Uses the printmst table and the custom_data[1] for prioritization
**    2) Moves the file (presumed from /tmp) to the archive location.
**    3) Zips the file saving precious resources.
**  
**  - this file was designed to be used with cartonization.  
**  - made assumptions for directories and command lines,  
**     they may be turned into parameters at another date.
***/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID as character  no-undo
   initial "@(#) $Header: /pdb/4.0/rf/RCS/prn_mani.p,v 1.3 1997/05/13 13:11:29
fwang Exp $~n"  .

&SCOP DEBUG FALSE

DEF INPUT PARAMETER ch_co            AS CHAR NO-UNDO .
DEF INPUT PARAMETER ch_wh            AS CHAR NO-UNDO .
DEF INPUT PARAMETER ch_form          AS CHAR NO-UNDO .
DEF INPUT PARAMETER ch_file          AS CHAR NO-UNDO .

DEF VAR ch_print_command    AS CHAR             no-undo .
DEF VAR ch_archive_command  AS CHAR             no-undo .
DEF VAR ch_file_dir         AS CHAR             no-undo .
DEF VAR ch_archive_dir      AS CHAR             no-undo .
DEF VAR ch_queue            AS CHAR             no-undo .

DEF VAR ch_base_dir         AS CHAR NO-UNDO .

          
ASSIGN
    ch_archive_command = ' gzip -9 ' 
    ch_base_dir        = os-getenv("IRMS_BASEDIR")
    ch_print_command   = ch_base_dir + '/bin/maxilp -c -d'
    ch_archive_dir     = ch_base_dir + '/wave/'
    ch_file_dir        = SESSION:TEMP-DIRECTORY  
    .

MAIN-BLOCK:
DO:

    /*** 
    ** If given a form, select printer and print... 
    ***/
    IF LENGTH ( TRIM ( ch_form ) ) > 0 THEN
    DO:

        RUN selectprinter NO-ERROR.

        IF ERROR-STATUS:ERROR THEN
        RETURN ERROR .
  
        IF LENGTH ( TRIM ( ch_queue ) ) > 0 THEN
        DO:

            RUN printfile NO-ERROR.
         
            IF ERROR-STATUS:ERROR THEN
            RETURN ERROR .
        END.  
    END.
    /*
    IF LENGTH ( TRIM ( ch_archive_command ) ) > 0 and 
       LENGTH ( TRIM ( ch_archive_dir     ) ) > 0 
    THEN DO:
       
        
        
        RUN doarchiving NO-ERROR .

        IF ERROR-STATUS:ERROR THEN
        RETURN ERROR .
    END.
    */
END. 

/***
**  Archive file to specified directory, allow file to 
***/
PROCEDURE doarchiving :
    DEF VAR ch_out_command AS CHAR NO-UNDO .
    
    def var ch_file1 as char no-undo .
    
    assign ch_file1 = substring(ch_file, 1, 7)
        + substring(ch_file, 10, 4) .

    IF LENGTH ( TRIM ( ch_archive_dir ) ) > 0 THEN
    DO:
        CASE OPSYS :
            WHEN "unix"  THEN
            ASSIGN ch_out_command = 'mv ' + /*ch_file_dir +*/  ch_file 
                                    + ' ' + ch_archive_dir + ch_file1 .
         WHEN "msdos" THEN
            ASSIGN 
               ch_out_command = 'move ' 
               + /*ch_file_dir + */ ch_file + ' ' + ch_archive_dir .
         OTHERWISE 
            RETURN ERROR .
      END CASE .

      OS-COMMAND SILENT VALUE ( ch_out_command ) .   
   END.

   IF LENGTH ( TRIM ( ch_archive_command ) ) > 0 THEN
   DO:
      ASSIGN
         ch_out_command = ch_archive_command  + ' ' 
            + ch_archive_dir + ch_file1 .

       OS-COMMAND SILENT VALUE ( ch_out_command ) .   
   END.
END.

/***
**  Copy file to the printer...
***/
PROCEDURE printfile :
    
   DEF VAR ch_out_command AS CHAR NO-UNDO .
   
   CASE OPSYS :

      WHEN "unix"  THEN
         ASSIGN 
            ch_out_command = ch_print_command + ' ' 
                            + ch_queue + ' ' /*+ ch_file_dir*/ + ch_file   .
      WHEN "msdos" THEN
         ASSIGN 
            ch_out_command = ch_print_command + ' ' + 
                            /*ch_file_dir +*/  ch_file   + ' ' + ch_queue.
      OTHERWISE 
         RETURN ERROR .
   END CASE .
  
   OS-COMMAND SILENT VALUE ( ch_out_command ) . 
END.


/***
** Selects the next open printer out of the printer table 
***/
PROCEDURE selectprinter :
   DEF BUFFER b_printer FOR irms.printmst .
   DEF QUERY  q_printer FOR b_printer     .
   
   OPEN QUERY q_printer
      FOR EACH b_printer
         WHERE
            b_printer.co_num     = ch_co   AND
            b_printer.wh_num     = ch_wh   AND
            b_printer.row_status           AND
            b_printer.print_form = ch_form 
         EXCLUSIVE
         BY b_printer.last_time_used 
         .
   
   printloop:
   DO TRANSACTION ON ERROR UNDO :
   
      GET NEXT q_printer .
 
       IF NOT AVAILABLE ( b_printer ) THEN
         RETURN ERROR .

      pause 1 no-message.

      RUN return_time ( OUTPUT b_printer.last_time_used ).
 
      /*IF AVAILABLE ( b_printer ) THEN
         ASSIGN  */
         
      assign ch_queue = b_printer.printer .
     
   END.
END.

PROCEDURE return_time:
    DEF OUTPUT PARAMETER ch_time  AS CHAR NO-UNDO format "x(14)".
    DEF VAR              sdate    AS CHAR NO-UNDO .
    DEF VAR              stime    AS CHAR NO-UNDO .
               
    ASSIGN
       sdate = STRING( TODAY, "99/99/9999" )
       stime = STRING( TIME, "HH:MM:SS" ) .
                           
    ASSIGN
       ch_time = SUBSTRING( sdate, 7, 4 ) +  /* "1994" */          
                 SUBSTRING( sdate, 1, 2 ) +  /* "199412" */
                 SUBSTRING( sdate, 4, 2 ) +  /* "19941213" */
                 SUBSTRING( stime, 1, 2 ) +  /* "1994121314" */
                 SUBSTRING( stime, 4, 2 ) +  /* "199412131415" */
                 SUBSTRING( stime, 7, 2)     /* "19981213141535" */ 
                 .

END PROCEDURE.
