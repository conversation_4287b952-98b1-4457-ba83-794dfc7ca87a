/**=================================================================**
* S:\IRMS.NET.2.6.0\irms.net\BE\AssignRMA\AssignRma\AssignRMA_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 07/11/08, 10:23 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE ttordhdrr NO-UNDO
    BEFORE-TABLE ttordhdrr_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   actual_freight                   AS  DECIMAL             
                                                 COLUMN-LABEL "Actual Freight"
                                                 LABEL "Actual Freight"
                                                 FORMAT "$ >>>>9.99"
                                                 INIT 0
                                                 HELP "Actual freight charges.|no|yes"
    FIELD   assigned                         AS  LOGICAL             
                                                 COLUMN-LABEL "Assigned for Pick"
                                                 LABEL "Assigned for Pick"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "This order has been assigned for picking|no|yes"
    FIELD   batch                            AS  INTEGER             
                                                 COLUMN-LABEL "Order Batch"
                                                 LABEL "Order Batch"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Don't change this field.|no|yes"
    FIELD   bill_addr1                       AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Address1"
                                                 LABEL "Billing Address1"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Bill To|no|yes"
    FIELD   bill_addr2                       AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Address2"
                                                 LABEL "Billing Address2"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Bill To|no|yes"
    FIELD   bill_addr_ext1                   AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Add Ext1"
                                                 LABEL "Billing Add Ext1"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Address|no|yes"
    FIELD   bill_addr_ext2                   AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Add Ext2"
                                                 LABEL "Billing Add Ext2"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Address|no|yes"
    FIELD   bill_addr_ext3                   AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Add Ext3"
                                                 LABEL "Billing Add Ext3"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Address|no|yes"
    FIELD   bill_city                        AS  CHARACTER           
                                                 COLUMN-LABEL "Billing City"
                                                 LABEL "Billing City"
                                                 FORMAT "X(20)"
                                                 INIT ""
                                                 HELP "Bill-to city.|no|yes"
    FIELD   bill_country                     AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Country"
                                                 LABEL "Billing Country"
                                                 FORMAT "x(20)"
                                                 INIT "USA"
                                                 HELP "Country for the billing address.|no|yes"
    FIELD   bill_email                       AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Email"
                                                 LABEL "Billing Email"
                                                 FORMAT "x(50)"
                                                 INIT ""
                                                 HELP "Enter the Bill-To Email|no|yes"
    FIELD   bill_name                        AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Name"
                                                 LABEL "Billing Name"
                                                 FORMAT "X(30)"
                                                 INIT ""
                                                 HELP "Name of the person who gets the bad news.|no|yes"
    FIELD   bill_phone                       AS  CHARACTER           
                                                 COLUMN-LABEL "Billing Phone"
                                                 LABEL "Billing Phone Number"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter this Billing phone number.|no|yes"
    FIELD   bill_state                       AS  CHARACTER           
                                                 COLUMN-LABEL "Billing State"
                                                 LABEL "Billing State"
                                                 FORMAT "X(2)"
                                                 INIT ""
                                                 HELP "Bill-to state.|no|yes"
    FIELD   bill_zip                         AS  CHARACTER           
                                                 COLUMN-LABEL "Billing ZIP"
                                                 LABEL "Billing ZIP"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Bill-to ZIP.|no|yes"
    FIELD   box_id                           AS  CHARACTER           
                                                 COLUMN-LABEL "Carton Size"
                                                 LABEL "Carton Size"
                                                 FORMAT "X(8)"
                                                 INIT ""
                                                 HELP "Enter the Carton Size|no|yes"
    FIELD   branch_id                        AS  CHARACTER           
                                                 COLUMN-LABEL "Branch Id"
                                                 LABEL "Branch Id"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Branch Id|no|yes"
    FIELD   cancelled_by                     AS  CHARACTER           
                                                 COLUMN-LABEL "Cancelled By"
                                                 LABEL "Cancelled By"
                                                 FORMAT "x(6)"
                                                 INIT ""
                                                 HELP "Enter the Cancelled By|no|yes"
    FIELD   cancel_date_time                 AS  CHARACTER           
                                                 COLUMN-LABEL "Date and Time"
                                                 LABEL "Date and Time"
                                                 FORMAT "9999-99-99 99:99"
                                                 INIT ""
                                                 HELP "Enter the Date and Time|no|yes"
    FIELD   cancel_flag                      AS  LOGICAL             
                                                 COLUMN-LABEL "Canceled?"
                                                 LABEL "Canceled?"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the cancel_flag|no|yes"
    FIELD   card                             AS  LOGICAL             
                                                 COLUMN-LABEL "Card"
                                                 LABEL "Card"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the card|no|yes"
    FIELD   card_type                        AS  CHARACTER           
                                                 COLUMN-LABEL "Card Type"
                                                 LABEL "card_type"
                                                 FORMAT "X(6)"
                                                 INIT ""
                                                 HELP "Enter the card_type|no|yes"
    FIELD   carrier                          AS  CHARACTER           
                                                 COLUMN-LABEL "Carrier"
                                                 LABEL "Carrier"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the carrier for this order.|no|yes"
    FIELD   charges                          AS  DECIMAL             
                                                 COLUMN-LABEL "Total charges"
                                                 LABEL "Total charges"
                                                 FORMAT "$ >>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Order total.|no|yes"
    FIELD   charge_type                      AS  CHARACTER           
                                                 COLUMN-LABEL "Charge Type"
                                                 LABEL "charge type"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the charge type|no|yes"
    FIELD   class                            AS  CHARACTER           
                                                 COLUMN-LABEL "Class"
                                                 LABEL "Class"
                                                 FORMAT "XX"
                                                 INIT "SO"
                                                 HELP "Order Class|no|yes"
    FIELD   clearance_code                   AS  CHARACTER           
                                                 COLUMN-LABEL "Clearance Code"
                                                 LABEL "Clearance Code"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Clearance Code|no|yes"
    FIELD   clearance_required               AS  LOGICAL             
                                                 COLUMN-LABEL "Clearance Required?"
                                                 LABEL "clearance required"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the clearance_required|no|yes"
    FIELD   cod_addr1                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Address"
                                                 LABEL "COD Address"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Address|no|yes"
    FIELD   cod_addr2                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Address"
                                                 LABEL "COD Address"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Address|no|yes"
    FIELD   cod_addr3                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Address"
                                                 LABEL "COD Address"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Address|no|yes"
    FIELD   cod_addr4                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Address"
                                                 LABEL "COD Address"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Address|no|yes"
    FIELD   cod_addr5                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Address"
                                                 LABEL "COD Address"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Address|no|yes"
    FIELD   cod_amount                       AS  DECIMAL             
                                                 COLUMN-LABEL "COD amount"
                                                 LABEL "COD amount"
                                                 FORMAT "$ >>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Additional COD charges per carton|no|yes"
    FIELD   cod_charge                       AS  CHARACTER           
                                                 COLUMN-LABEL "Charge COD to"
                                                 LABEL "Charge COD to"
                                                 FORMAT "X"
                                                 INIT "C"
                                                 HELP "Charge COD to Consignee or Shipper?|no|yes"
    FIELD   cod_city                         AS  CHARACTER           
                                                 COLUMN-LABEL "COD City"
                                                 LABEL "COD City"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the COD City|no|yes"
    FIELD   cod_country                      AS  CHARACTER           
                                                 COLUMN-LABEL "COD Country"
                                                 LABEL "COD Country"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the COD Country|no|yes"
    FIELD   cod_email                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Email"
                                                 LABEL "COD Email"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Email|no|yes"
    FIELD   cod_flag                         AS  LOGICAL             
                                                 COLUMN-LABEL "COD"
                                                 LABEL "COD"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Is this a COD order?|no|yes"
    FIELD   cod_name                         AS  CHARACTER           
                                                 COLUMN-LABEL "COD Name"
                                                 LABEL "COD Name"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the COD Name|no|yes"
    FIELD   cod_phone                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD Phone Number"
                                                 LABEL "Cod Phone Number"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter this Cod phone number.|no|yes"
    FIELD   cod_state                        AS  CHARACTER           
                                                 COLUMN-LABEL "COD State"
                                                 LABEL "COD State"
                                                 FORMAT "x(2)"
                                                 INIT ""
                                                 HELP "Enter the COD State|no|yes"
    FIELD   cod_zip                          AS  CHARACTER           
                                                 COLUMN-LABEL "COD Postal Code"
                                                 LABEL "COD Postal Code"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the COD Postal Code|no|yes"
    FIELD   comment                          AS  CHARACTER           
                                                 COLUMN-LABEL "Comment"
                                                 LABEL "comment"
                                                 FORMAT "X(30)"
                                                 INIT ""
                                                 HELP "Enter the comment|no|yes"
    FIELD   consignee_attn                   AS  CHARACTER           
                                                 COLUMN-LABEL "Consignee Attn"
                                                 LABEL "consignee_attn"
                                                 FORMAT "X(50)"
                                                 INIT ""
                                                 HELP "Enter the consignee_attn|no|yes"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the company number.|no|yes"
    FIELD   customer_freight                 AS  DECIMAL             
                                                 COLUMN-LABEL "Customer Freight"
                                                 LABEL "Customer Freight"
                                                 FORMAT "$ >>>>9.99"
                                                 INIT 0
                                                 HELP "What the customer paid for S/H.|no|yes"
    FIELD   customer_po                      AS  CHARACTER           
                                                 COLUMN-LABEL "Customer's PO #"
                                                 LABEL "Customer's PO #"
                                                 FORMAT "X(21)"
                                                 INIT ""
                                                 HELP "Customer's Purchase Order Number|no|yes"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "CustomFields"
                                                 LABEL "CustomFields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the CustomFields|no|no"
    FIELD   custom_selector                  AS  CHARACTER           
                                                 COLUMN-LABEL "Custom Selector"
                                                 LABEL "Custom Selector"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Custom Selector|no|yes"
    FIELD   cust_code                        AS  CHARACTER           
                                                 COLUMN-LABEL "Customer"
                                                 LABEL "Customer Code"
                                                 FORMAT "x(12)"
                                                 INIT ""
                                                 HELP "Customer code (for billing).|no|yes"
    FIELD   del_route                        AS  CHARACTER           
                                                 COLUMN-LABEL "Route Id"
                                                 LABEL "Route Id"
                                                 FORMAT "x(3)"
                                                 INIT ""
                                                 HELP "The id for this delivery route in IRMS.|no|yes"
    FIELD   dept_num                         AS  INTEGER             
                                                 COLUMN-LABEL "Department"
                                                 LABEL "Department"
                                                 FORMAT ">>>>>9"
                                                 HELP "Department Number|no|yes"
    FIELD   discount                         AS  DECIMAL             
                                                 COLUMN-LABEL "Discount"
                                                 LABEL "Discount"
                                                 FORMAT ">>9.99%"
                                                 INIT 0
                                                 HELP "Discount Amount.|no|yes"
    FIELD   DiscountAmt                      AS  DECIMAL             
                                                 COLUMN-LABEL "Discount Amt"
                                                 LABEL "Discount Amt"
                                                 FORMAT "$->>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Discount Amt|no|yes"
    FIELD   doc_id                           AS  CHARACTER           
                                                 COLUMN-LABEL "Document ID"
                                                 LABEL "Document ID"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the Document ID|no|yes"
    FIELD   drop_cube                        AS  DECIMAL             
                                                 COLUMN-LABEL "Drop Cube"
                                                 LABEL "drop_cube"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the drop_cube|no|yes"
    FIELD   drop_type                        AS  CHARACTER           
                                                 COLUMN-LABEL "Drop Type"
                                                 LABEL "Drop Type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Drop Type|no|yes"
    FIELD   drop_weight                      AS  DECIMAL             
                                                 COLUMN-LABEL "Drop Weight"
                                                 LABEL "Drop Weight"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Weight calculated at order drop time.|no|yes"
    FIELD   exp_ship_date                    AS  DATE                
                                                 COLUMN-LABEL "Ship Date"
                                                 LABEL "Ship Date"
                                                 FORMAT "99/99/9999"
                                                 HELP "Expected Shipping Date, from host.|no|yes"
    FIELD   freight_terms                    AS  CHARACTER           
                                                 COLUMN-LABEL "Freight Terms"
                                                 LABEL "Freight Terms"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Freight Terms|no|yes"
    FIELD   gift                             AS  LOGICAL             
                                                 COLUMN-LABEL "Gift"
                                                 LABEL "Gift"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Is this order a gift?|no|yes"
    FIELD   gift_wrap                        AS  LOGICAL             
                                                 COLUMN-LABEL "Gift Wrap"
                                                 LABEL "Gift Wrap"
                                                 FORMAT "yes/no"
                                                 HELP "Gift wrap whole order?|no|yes"
    FIELD   gift_wrap_type                   AS  CHARACTER           
                                                 COLUMN-LABEL "Gift Wrap Type"
                                                 LABEL "gift_wrap_type"
                                                 FORMAT "X(6)"
                                                 INIT ""
                                                 HELP "Enter the gift_wrap_type|no|yes"
    FIELD   guaranteed_del_time              AS  CHARACTER           
                                                 COLUMN-LABEL "Guaranteed Delivery Time"
                                                 LABEL "Guaranteed Delivery Time"
                                                 FORMAT "99:99"
                                                 INIT "23:59"
                                                 HELP "Enter the Guaranteed Delivery Time|no|yes"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID|no|no"
    FIELD   hold_reason                      AS  CHARACTER           
                                                 COLUMN-LABEL "Hold Reason"
                                                 LABEL "Hold Reason"
                                                 FORMAT "X(20)"
                                                 INIT ""
                                                 HELP "Reason why this order is on hold|no|yes"
    FIELD   host_batch                       AS  CHARACTER           
                                                 COLUMN-LABEL "Host Wave"
                                                 LABEL "Host Wave"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Host-assigned wave id.|no|yes"
    FIELD   host_origin                      AS  CHARACTER           
                                                 COLUMN-LABEL "Host Origin"
                                                 LABEL "Host Origin"
                                                 FORMAT "X(25)"
                                                 INIT ""
                                                 HELP "Enter the Host Origin|no|yes"
    FIELD   host_selector                    AS  CHARACTER           
                                                 COLUMN-LABEL "Host selector"
                                                 LABEL "Host selector"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Host selector|no|yes"
    FIELD   host_sequence                    AS  INTEGER             
                                                 COLUMN-LABEL "Host Sequence"
                                                 LABEL "Host Sequence"
                                                 FORMAT ">,>>>,>>9"
                                                 HELP "Sequence of this order within the host-assigned wave.|no|yes"
    FIELD   id                               AS  INTEGER             
                                                 COLUMN-LABEL "Id"
                                                 LABEL "Id"
                                                 FORMAT ">>>>>>9"
                                                 INIT 0
                                                 HELP "DO NOT EDIT THIS FIELD.|no|yes"
    FIELD   image_name                       AS  CHARACTER           
                                                 COLUMN-LABEL "Image Name"
                                                 LABEL "image_name"
                                                 FORMAT "X(20)"
                                                 INIT ""
                                                 HELP "Enter the image_name|no|yes"
    FIELD   international                    AS  LOGICAL             
                                                 COLUMN-LABEL "International"
                                                 LABEL "International"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "International order.|no|yes"
    FIELD   kit_build_type                   AS  CHARACTER           
                                                 COLUMN-LABEL "Kit Build Type"
                                                 LABEL "Kit Build Type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Used for work orders.|no|yes"
    FIELD   line_count                       AS  INTEGER             
                                                 COLUMN-LABEL "Line Count"
                                                 LABEL "Line Count"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Number of lines in this order.|no|yes"
    FIELD   lot                              AS  CHARACTER           
                                                 COLUMN-LABEL "Lot"
                                                 LABEL "Lot"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Lot Number.|no|yes"
    FIELD   max_days                         AS  INTEGER             
                                                 COLUMN-LABEL "Maximum Days"
                                                 LABEL "Maximum Days"
                                                 FORMAT ">,>>>,>>9"
                                                 HELP "Enter the Maximum Days|no|yes"
    FIELD   my_desc1                         AS  CHARACTER           
                                                 COLUMN-LABEL "my_desc1"
                                                 LABEL "my_desc1"
                                                 FORMAT "x(50)"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   my_desc2                         AS  CHARACTER           
                                                 COLUMN-LABEL "my_desc2"
                                                 LABEL "my_desc2"
                                                 FORMAT "x(50)"
                                                 INIT ""
                                                 HELP "R&D Field|no|no"
    FIELD   no_detail                        AS  LOGICAL             
                                                 COLUMN-LABEL "No Details?"
                                                 LABEL "No Details?"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the CFno_detail|no|yes"
    FIELD   num_cartons                      AS  INTEGER             
                                                 COLUMN-LABEL "Number of Cartons"
                                                 LABEL "Number of Cartons"
                                                 FORMAT ">,>>>,>>9"
                                                 HELP "Enter the Number of Cartons|no|yes"
    FIELD   order                            AS  CHARACTER           
                                                 COLUMN-LABEL "Order"
                                                 LABEL "Order"
                                                 FORMAT "x(12)"
                                                 INIT ""
                                                 HELP "Please enter an order number.|no|yes"
    FIELD   order_date                       AS  DATE                
                                                 COLUMN-LABEL "Order Date"
                                                 LABEL "Order Date"
                                                 FORMAT "99/99/9999"
                                                 HELP "Date when order was received by IRMS.|no|yes"
    FIELD   order_status                     AS  CHARACTER           
                                                 COLUMN-LABEL "Status"
                                                 LABEL "Status"
                                                 FORMAT "X"
                                                 INIT "O"
                                                 HELP "Enter the status for this order.|no|yes"
    FIELD   order_suffix                     AS  CHARACTER           
                                                 COLUMN-LABEL "Order Suffix"
                                                 LABEL "Order Suffix"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Enter the Order Suffix|no|yes"
    FIELD   orig_cube                        AS  DECIMAL             
                                                 COLUMN-LABEL "Orig Cube"
                                                 LABEL "orig_cube"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the orig_cube|no|yes"
    FIELD   orig_order_date                  AS  DATE                
                                                 COLUMN-LABEL "Order Date"
                                                 LABEL "Order Date"
                                                 FORMAT "99/99/9999"
                                                 HELP "Order date as received from the host.|no|yes"
    FIELD   orig_weight                      AS  DECIMAL             
                                                 COLUMN-LABEL "Orig. Calc. Weight"
                                                 LABEL "Orig. Calc. Weight"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Original Calculated Weight|no|yes"
    FIELD   partial                          AS  LOGICAL             
                                                 COLUMN-LABEL "Partial Shipment"
                                                 LABEL "Partial Shipment"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Partial Shipment.|no|yes"
    FIELD   pay_method                       AS  CHARACTER           
                                                 COLUMN-LABEL "Pay Method"
                                                 LABEL "Pay Method"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Pay method|no|yes"
    FIELD   pool                             AS  CHARACTER           
                                                 COLUMN-LABEL "Pool"
                                                 LABEL "Pool"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Pool|no|yes"
    FIELD   printed                          AS  LOGICAL             
                                                 COLUMN-LABEL "Printed"
                                                 LABEL "Printed"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "True if this order has been printed.|no|yes"
    FIELD   priority                         AS  INTEGER             
                                                 COLUMN-LABEL "Priority"
                                                 LABEL "Priority"
                                                 FORMAT "999"
                                                 INIT 0
                                                 HELP "Order Priority.|no|yes"
    FIELD   product                          AS  CHARACTER           
                                                 COLUMN-LABEL "Finished Product"
                                                 LABEL "Finished Product"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "The finished product for this work-order|no|yes"
    FIELD   product_qty                      AS  DECIMAL             
                                                 COLUMN-LABEL "Production Quantity"
                                                 LABEL "Production Quantity"
                                                 FORMAT ">>>,>>9.99"
                                                 HELP "Quantity to be produced by this work-order|no|yes"
    FIELD   pro_number                       AS  CHARACTER           
                                                 COLUMN-LABEL "Pro Number"
                                                 LABEL "Pro Number"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Pro Number|no|yes"
    FIELD   rate_type                        AS  CHARACTER           
                                                 COLUMN-LABEL "Rate Type"
                                                 LABEL "Rate Type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Rate type.|no|yes"
    FIELD   rma_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "RMA Number"
                                                 LABEL "RMA Number"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the rma_num|no|yes"
    FIELD   row_status                       AS  LOGICAL             
                                                 COLUMN-LABEL "Active"
                                                 LABEL "Active"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Order active?|no|yes"
    FIELD   service                          AS  CHARACTER           
                                                 COLUMN-LABEL "Service"
                                                 LABEL "Service"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Service.|no|yes"
    FIELD   ship_addr1                       AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Address1"
                                                 LABEL "Shipping Address1"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Ship To|no|yes"
    FIELD   ship_addr2                       AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Address2"
                                                 LABEL "Shipping Address2"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Ship To|no|yes"
    FIELD   ship_addr_ext1                   AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Add Ext1"
                                                 LABEL "Shipping Add Ext1"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Address|no|yes"
    FIELD   ship_addr_ext2                   AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Add Ext2"
                                                 LABEL "Shipping Add Ext2"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Address|no|yes"
    FIELD   ship_addr_ext3                   AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Add Ext3"
                                                 LABEL "Shipping Add Ext3"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Address|no|yes"
    FIELD   ship_city                        AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping City"
                                                 LABEL "Shipping City"
                                                 FORMAT "X(20)"
                                                 INIT ""
                                                 HELP "Ship-To city.|no|yes"
    FIELD   ship_country                     AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Country"
                                                 LABEL "Shipping Country"
                                                 FORMAT "x(20)"
                                                 INIT "USA"
                                                 HELP "Country for the shipping address.|no|yes"
    FIELD   ship_cube                        AS  DECIMAL             
                                                 COLUMN-LABEL "Ship Cube"
                                                 LABEL "Ship Cube"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the ship_cube|no|yes"
    FIELD   ship_cust_code                   AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Customer"
                                                 LABEL "Shipping Customer"
                                                 FORMAT "x(12)"
                                                 INIT ""
                                                 HELP "Customer code (for shipping).|no|yes"
    FIELD   ship_date                        AS  DATE                
                                                 COLUMN-LABEL "Ship Date"
                                                 LABEL "Ship Date"
                                                 FORMAT "99/99/9999"
                                                 HELP "Date when order shipped.|no|yes"
    FIELD   ship_email                       AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Email"
                                                 LABEL "Shipping Email"
                                                 FORMAT "x(50)"
                                                 INIT ""
                                                 HELP "Enter the Ship-To Email|no|yes"
    FIELD   ship_msg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Messages"
                                                 LABEL "Shipping Messages"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Shipping Messages|no|yes"
    FIELD   ship_name                        AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Name"
                                                 LABEL "Shipping Name"
                                                 FORMAT "X(30)"
                                                 INIT ""
                                                 HELP "Person who gets the toy.|no|yes"
    FIELD   ship_phone                       AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping Phone"
                                                 LABEL "Shipping Phone"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter this Shipping phone number.|no|yes"
    FIELD   ship_state                       AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping State"
                                                 LABEL "Shipping State"
                                                 FORMAT "X(2)"
                                                 INIT ""
                                                 HELP "Ship-To state.|no|yes"
    FIELD   ship_weight                      AS  DECIMAL             
                                                 COLUMN-LABEL "Ship Weight"
                                                 LABEL "Ship Weight"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Weight calculated or input at ship time.|no|yes"
    FIELD   ship_zip                         AS  CHARACTER           
                                                 COLUMN-LABEL "Shipping ZIP"
                                                 LABEL "Shipping ZIP"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Ship-to ZIP.|no|yes"
    FIELD   shp_by_irms                      AS  LOGICAL             
                                                 COLUMN-LABEL "Shipped By IRMS"
                                                 LABEL "Shipped By IRMS"
                                                 FORMAT "yes/no"
                                                 INIT yes
                                                 HELP "Order was shipped by IRMS|no|yes"
    FIELD   store                            AS  CHARACTER           
                                                 COLUMN-LABEL "Store"
                                                 LABEL "Store"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the store|no|yes"
    FIELD   tax                              AS  DECIMAL             
                                                 COLUMN-LABEL "Tax"
                                                 LABEL "Tax"
                                                 FORMAT "$ >>>,>>9.99"
                                                 INIT 0
                                                 HELP "Order tax.|no|yes"
    FIELD   the_rt                           AS  CHARACTER           
                                                 COLUMN-LABEL "RT Number"
                                                 LABEL "RT Number"
                                                 INIT ""
                                                 HELP "Enter the the_rt|no|yes"
    FIELD   type                             AS  CHARACTER           
                                                 COLUMN-LABEL "Order Type"
                                                 LABEL "Order Type"
                                                 FORMAT "XX"
                                                 INIT "R"
                                                 HELP "Enter the type for this order.|no|yes"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse Number.|no|yes"

                    .
TEMP-TABLE ttordhdrr:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsAssignRMA
    FOR ttordhdrr,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


