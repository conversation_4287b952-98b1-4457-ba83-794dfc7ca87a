/**=================================================================**
* Y:\irms.net.1.4.0\irms.net\BE\AltLocation\AltLocation\Altlocation_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 17/07/07, 10:39 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE AltLocation NO-UNDO
    BEFORE-TABLE AltLocation_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   abc                              AS  CHARACTER           
                                                 COLUMN-LABEL "Location Class"
                                                 LABEL "Location Class"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Location Class|no|yes"
    FIELD   abs_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Item Number"
                                                 LABEL "Item Number"
                                                 FORMAT "x(24)"
                                                 INIT "0.0"
                                                 HELP "Enter the Item Number|no|yes"
    FIELD   aisle                            AS  INTEGER             
                                                 COLUMN-LABEL "Aisle"
                                                 LABEL "Aisle"
                                                 FORMAT ">>>9"
                                                 INIT 1
                                                 HELP "Enter the Aisle|no|yes"
    FIELD   bin_full                         AS  LOGICAL             
                                                 COLUMN-LABEL "bin_full"
                                                 LABEL "bin_full"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the bin_full|no|yes"
    FIELD   bin_hits                         AS  INTEGER             
                                                 COLUMN-LABEL "Bin Hits"
                                                 LABEL "Bin Hits"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the Bin Hits|no|yes"
    FIELD   bin_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Location Id"
                                                 LABEL "Location Id"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Location Id|no|yes"
    FIELD   check_qty                        AS  LOGICAL             
                                                 COLUMN-LABEL "Check Quantity"
                                                 LABEL "Check Quantity"
                                                 FORMAT "yes/no"
                                                 INIT yes
                                                 HELP "Enter the Check Quantity|no|yes"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Company|no|yes"
    FIELD   cube                             AS  DECIMAL             
                                                 COLUMN-LABEL "Cube"
                                                 LABEL "Cube"
                                                 FORMAT ">>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Cube|no|yes"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "Custom Fields"
                                                 LABEL "Custom Fields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Custom Fields|no|yes"
    FIELD   depth                            AS  INTEGER             
                                                 COLUMN-LABEL "Depth"
                                                 LABEL "Depth"
                                                 FORMAT ">,>>9"
                                                 INIT 0
                                                 HELP "Enter the Depth|no|yes"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID|no|yes"
    FIELD   height                           AS  INTEGER             
                                                 COLUMN-LABEL "Height"
                                                 LABEL "Height"
                                                 FORMAT ">,>>9"
                                                 INIT 0
                                                 HELP "Enter the Height|no|yes"
    FIELD   host_origin                      AS  CHARACTER           
                                                 COLUMN-LABEL "Host Origin"
                                                 LABEL "Host Origin"
                                                 FORMAT "X(25)"
                                                 INIT ""
                                                 HELP "Enter the Host Origin|no|yes"
    FIELD   loc_type                         AS  CHARACTER           
                                                 COLUMN-LABEL "Location Type"
                                                 LABEL "Location Type"
                                                 FORMAT "x"
                                                 INIT "P"
                                                 HELP "Enter the Location Type|no|yes"
    FIELD   max_lvl                          AS  DECIMAL             
                                                 COLUMN-LABEL "max lvl"
                                                 LABEL "Maximum Level"
                                                 FORMAT ">>,>>9.99"
                                                 INIT 0.0
                                                 HELP "Enter the Maximum Level|no|yes"
    FIELD   max_pal                          AS  INTEGER             
                                                 COLUMN-LABEL "max pal"
                                                 LABEL "Maximum Pallets"
                                                 FORMAT ">>9"
                                                 INIT 1
                                                 HELP "Enter the Maximum Pallets|no|yes"
    FIELD   max_weight                       AS  INTEGER             
                                                 COLUMN-LABEL "max weight"
                                                 LABEL "Maximum Weight"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the Maximum Weight|no|yes"
    FIELD   min_lvl                          AS  DECIMAL             
                                                 COLUMN-LABEL "min lvl"
                                                 LABEL "Minimum Level"
                                                 FORMAT ">>,>>9.99"
                                                 INIT 0.0
                                                 HELP "Enter the Minimum Level|no|yes"
    FIELD   pallet_footprint                 AS  INTEGER             
                                                 COLUMN-LABEL "Pallet Footprint"
                                                 LABEL "Pallet Footprint"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the Pallet Footprint|no|yes"
    FIELD   physical                         AS  LOGICAL             
                                                 COLUMN-LABEL "Physical"
                                                 LABEL "Physical"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the Physical|no|yes"
    FIELD   prim_pick                        AS  LOGICAL             
                                                 COLUMN-LABEL "Primary Pick"
                                                 LABEL "Primary Pick"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the Primary Pick|no|yes"
    FIELD   prim_pick_type                   AS  CHARACTER           
                                                 COLUMN-LABEL "P.P. Type"
                                                 LABEL "P.P. Type"
                                                 FORMAT "X"
                                                 INIT "S"
                                                 HELP "Enter the P.P. Type|no|yes"
    FIELD   rep_qty                          AS  DECIMAL             
                                                 COLUMN-LABEL "Rep Quantity"
                                                 LABEL "Rep Quantity"
                                                 FORMAT ">>,>>9.99"
                                                 INIT 0.0
                                                 HELP "Enter the Rep Quantity|no|yes"
    FIELD   rep_unit                         AS  CHARACTER           
                                                 COLUMN-LABEL "Replenish. Unit"
                                                 LABEL "Replenish. Unit"
                                                 FORMAT "X"
                                                 INIT "C"
                                                 HELP "Enter the Replenish. Unit|no|yes"
    FIELD   row_status                       AS  LOGICAL             
                                                 COLUMN-LABEL "Active"
                                                 LABEL "Active"
                                                 FORMAT "yes/no"
                                                 INIT yes
                                                 HELP "Enter the Active|no|yes"
    FIELD   stack_height                     AS  INTEGER             
                                                 COLUMN-LABEL "Stacking Height"
                                                 LABEL "Stacking Height"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the Stacking Height|no|yes"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse|no|yes"
    FIELD   wh_zone                          AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse Zone"
                                                 LABEL "Warehouse Zone"
                                                 FORMAT "XX"
                                                 INIT ""
                                                 HELP "Enter the Warehouse Zone|no|yes"
    FIELD   width                            AS  INTEGER             
                                                 COLUMN-LABEL "Width"
                                                 LABEL "Width"
                                                 FORMAT ">,>>9"
                                                 INIT 0
                                                 HELP "Enter the Width|no|yes"
    FIELD   wood_flag                        AS  LOGICAL             
                                                 COLUMN-LABEL "Wood"
                                                 LABEL "Wood"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the Wood|no|yes"

                    .
TEMP-TABLE AltLocation:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsAltlocation
    FOR AltLocation,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


