/*** 
** 
** Print Item Unload Labels on zebra  printer
***/

DEF VAR SCCS_ID  AS CHAR NO-UNDO INIT "@(#) $Header: /pdb/9.01/remote/RCS/uloadlbl.p,v 1.3 2000-03-16 10:44:03-06 dave Exp $~n" .
                     
    DEF INPUT PARAMETER ch_abs_num       AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER ch_item_desc     AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER ch_unload_date   AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER ch_emp_num       AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER ch_dock_id       AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER ch_value_printer AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER i_qty            AS  INT   NO-UNDO .
    DEF INPUT PARAMETER ch_wh_zone       AS  CHAR  NO-UNDO .
    DEF INPUT PARAMETER ch_os            AS  CHAR  NO-UNDO .
     
    DEF VAR ch_type   AS CHAR NO-UNDO INIT ? .
    DEF VAR ch_list   AS CHAR NO-UNDO INIT ? .
    DEF VAR ch_que    AS CHAR NO-UNDO INIT ? .
    DEF VAR ch_arrow  AS CHAR NO-UNDO INIT ? .
    DEF VAR ch_format AS CHAR NO-UNDO INIT ? .
    DEF VAR i_type    AS INT  NO-UNDO INIT ? .
    DEF VAR i_num_ent AS INT  NO-UNDO        .
    DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
    DEF VAR ch_print  AS CHAR NO-UNDO        .
    DEF VAR ch_file   AS CHAR NO-UNDO        .
 
    MESSAGE "Starting Printing..." .
   /* output through 'lp -d zebra > /dev/null' . */
    ASSIGN
        ch_file = SESSION:TEMP-DIRECTORY + 'uloadlbl.p'.
 
/***                   ***
 **   Build print File **/
   
    DO WHILE i_qty >= i_entry :
        ASSIGN
            i_entry = i_entry + 1 .
            MESSAGE 'Generating ' ch_abs_num  'Label...'.

        RUN print_label .
    END.
  
  
/***
 **  Print the File
 ***/
 
    MESSAGE "About to print item labels:" ch_file .
     IF (opsys eq "UNIX") THEN
       DO:
          ASSIGN ch_print = "lp -c -d " + ch_value_printer + " " + ch_file .
          MESSAGE "Running command:" ch_print .
          OS-COMMAND SILENT VALUE ( ch_print ) .
          PAUSE 5 NO-MESSAGE.
          OS-DELETE  VALUE ( ch_file    ) .
       END .
       
     ELSE DO: /*NT*/
          /*****This is the NT Print method when spooler was used*****
           
            DEFINE VARIABLE ch_spool_dir AS CHARACTER NO-UNDO .
            ASSIGN          ch_spool_dir = os-getenv("IRMS_SPOOLER") .
                                        
            IF (ch_spool_dir gt "") THEN
                DO:
                   ASSIGN ch_spool_dir = ch_spool_dir + "/" + ch_que .                              MESSAGE "Copying" ch_file "to" ch_spool_dir .  
                   OS-COPY VALUE(ch_file) value(ch_spool_dir) .
                   OS-DELETE VALUE(ch_file) .
                END .

            ELSE
                DO:
                  MESSAGE "IRMS_SPOOLER variable is not defined." .
                  RETURN ERROR .
                END .*****************************************/
           
           MESSAGE "copying file " ch_file "to printer " ch_value_printer .
            
           OS-COPY VALUE( ch_file ) VALUE( ch_value_printer ) .
           
           OS-DELETE VALUE ( ch_file ) .
                    
END.                        
RETURN .
                           
PROCEDURE print_label:
                        
   DEFINE VARIABLE ch_value AS CHARACTER NO-UNDO .
   
   OUTPUT TO VALUE ( ch_file )  APPEND.
                                              
   PUT UNFORMATTED
              "^XA^CFD^FS
               ^FO5,5
               ^GB790,1200,4^FS
               ^FO625,9
               ^GB0,1195,4^FS
               ^FO510,9
               ^GB0,703,4^FS
               ^FO514,456
               ^GB111,0,4^FS
               ^FO9,710
               ^GB617,0,4^FS
               ^FO310,714
               ^GB0,490,4^FS
               ^FO750,20
               ^A0R,30,25^FDDescription:^FS
               ^FO635,50
               ^A0R,100,76^FD" + ch_item_desc + "^FS
               ^FO585,20
               ^A0R,30,25^FDDate Received:^FS
               ^FO520,65
               ^A0R,50,50^FD" + ch_unload_date + "^FS
               ^FO585,475
               ^A0R,30,25^FDReceived By:^FS
               ^FO520,500
               ^A0R,50,50^FD" + ch_emp_num + "^FS
               ^FO585,720
               ^A0R,30,25^FDPrimary Bin:^FS
               ^FO410,750
               ^A0R,100,100^FD" + ch_dock_id + "^FS
               ^FO470,20
               ^A0R,30m,25^FDItem Number:^FS
               ^FO375,65
               ^A0R,75,75^FD" + ch_abs_num + "^FS
               ^BY3.0
               ^FO50,50
               ^BCR,300,N,N,N^FD" + ch_abs_num + "^FS
               ^FO270,720
               ^A0R,30,25^FDPutaway Zone:^FS
               ^FO75,750
               ^A0R,170,170^FD" + ch_wh_zone + "^FS
               
               ^XZ".


  

 END PROCEDURE.                       
