"ttTablePropsCREATE"
"ttTablePropsTblTableNamettorddtlSrch"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKeyGUID"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameabs_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelItem Number"
"ttFieldPropsFldColLabelItem Number"
"ttFieldPropsFldHelpEnter the company-wide unique code for this item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameact_qty"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelActual Quantity"
"ttFieldPropsFldColLabelActual Qty"
"ttFieldPropsFldHelpActual quantity picked for this order line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameassigned"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelAllocated"
"ttFieldPropsFldColLabelAllocated"
"ttFieldPropsFldHelpEnter the Allocated"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamebin_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelLocation"
"ttFieldPropsFldColLabelLocation"
"ttFieldPropsFldHelpLocation where this order line will be picked."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamecharges"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelItem Price"
"ttFieldPropsFldColLabelItem Price"
"ttFieldPropsFldHelpPrice charged for this item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamecomment"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelComment"
"ttFieldPropsFldColLabelComment"
"ttFieldPropsFldHelpEnter the comment"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the company number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustom Fields"
"ttFieldPropsFldColLabelCustom Fields"
"ttFieldPropsFldHelpEnter the Custom Fields"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamediscount"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>9.<<%"
"ttFieldPropsFldSideLabelItem Discount"
"ttFieldPropsFldColLabelItem Discount"
"ttFieldPropsFldHelpItem discount."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameDiscountAmt"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$->>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelDiscount Amt"
"ttFieldPropsFldColLabelDiscount Amt"
"ttFieldPropsFldHelpEnter the Discount Amt"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamedrop_cube"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelDrop Cube"
"ttFieldPropsFldColLabelDrop Cube"
"ttFieldPropsFldHelpEnter the drop_cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamedrop_weight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelDrop Weight"
"ttFieldPropsFldColLabelDrop Weight"
"ttFieldPropsFldHelpWeight calculated at order drop time."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameExtdPrice"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$>>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelExtd Price"
"ttFieldPropsFldColLabelExtd Price"
"ttFieldPropsFldHelpEnter the ExtdPrice"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamefiller_flag"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelUse as filler?"
"ttFieldPropsFldColLabelUse as filler?"
"ttFieldPropsFldHelpUse as filler?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamefl_zone"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelFull Case Location"
"ttFieldPropsFldColLabelFull Case Location"
"ttFieldPropsFldHelpThe Full Case Location for this order line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamegift_wrap"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelGift Wrap"
"ttFieldPropsFldColLabelGift Wrap"
"ttFieldPropsFldHelpGift wrap this line?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameGUID1"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID-ordhdr"
"ttFieldPropsFldColLabelGUID-ordhdr"
"ttFieldPropsFldHelpEnter the GUID-ordhdr"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamehost_origin"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(25)"
"ttFieldPropsFldSideLabelHost Origin"
"ttFieldPropsFldColLabelHost Origin"
"ttFieldPropsFldHelpEnter the Host Origin"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameid"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>>>>9"
"ttFieldPropsFldSideLabelId"
"ttFieldPropsFldColLabelId"
"ttFieldPropsFldHelpDO NOT EDIT THIS FIELD."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameitem_desc"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelItem Description"
"ttFieldPropsFldColLabelItem Description"
"ttFieldPropsFldHelpEnter a description for this item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameline"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit1"
"ttFieldPropsFldFormat>>>9"
"ttFieldPropsFldSideLabelLine"
"ttFieldPropsFldColLabelLine"
"ttFieldPropsFldHelpOrder Line number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameline_alt_number"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelAlt Line Num"
"ttFieldPropsFldColLabelAlt Line Num"
"ttFieldPropsFldHelpR&D field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameLine_sequence"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelLine Sequence"
"ttFieldPropsFldColLabelLine Sequence"
"ttFieldPropsFldHelpOrder Line Sequence"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameline_status"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitO"
"ttFieldPropsFldFormatx(12)"
"ttFieldPropsFldSideLabelStatus"
"ttFieldPropsFldColLabelStatus"
"ttFieldPropsFldHelpEnter the status for this line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamelot"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelLot"
"ttFieldPropsFldColLabelLot"
"ttFieldPropsFldHelpLot Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamemsds_employee"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(5)"
"ttFieldPropsFldSideLabelMSDS Employee"
"ttFieldPropsFldColLabelMSDS Employee"
"ttFieldPropsFldHelpEmployee who handled this MSDS sheet."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamemsds_packed"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelMSDS Packed"
"ttFieldPropsFldColLabelMSDS Packed"
"ttFieldPropsFldHelpMSDS sheet packed?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamemsds_required"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelMSDS Required"
"ttFieldPropsFldColLabelMSDS Required"
"ttFieldPropsFldHelpEnter the MSDS Required"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamemsds_sheet"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelMSDS Sheet"
"ttFieldPropsFldColLabelMSDS Sheet"
"ttFieldPropsFldHelpEnter the MSDS Sheet"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameordered_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelQuantity Ordered"
"ttFieldPropsFldColLabelQuantity Ordered"
"ttFieldPropsFldHelpQuantity ordered."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameorder_alt_num"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelorder_alt_num"
"ttFieldPropsFldColLabelorder_alt_num"
"ttFieldPropsFldHelpR&D field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameorder_alt_suf"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelorder_alt_suf"
"ttFieldPropsFldColLabelorder_alt_suf"
"ttFieldPropsFldHelpR&D field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameorig_cube"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelOriginal Cube"
"ttFieldPropsFldColLabelOriginal Cube"
"ttFieldPropsFldHelpEnter the orig_cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameorig_req_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelOriginal Quantity"
"ttFieldPropsFldColLabelOriginal Quantity"
"ttFieldPropsFldHelpQuantity originally requested."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameorig_weight"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelOrig. Calc. Weight"
"ttFieldPropsFldColLabelOrig. Calc. Weight"
"ttFieldPropsFldHelpOriginal Calculated Weight"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepackage_code"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitB"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelPackage Code"
"ttFieldPropsFldColLabelPackage Code"
"ttFieldPropsFldHelpEnter the Package Code"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepick_line"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInityes"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelPick Line?"
"ttFieldPropsFldColLabelPick Line?"
"ttFieldPropsFldHelpPick this order line?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepool"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelPool"
"ttFieldPropsFldColLabelPool"
"ttFieldPropsFldHelpEnter the Pool"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepo_line"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>>>9"
"ttFieldPropsFldSideLabelPO Line"
"ttFieldPropsFldColLabelPO Line"
"ttFieldPropsFldHelpEnter the po_line"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepo_line_sequence"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelPO Line Seq"
"ttFieldPropsFldColLabelPO Line Seq"
"ttFieldPropsFldHelpEnter the po_line_sequence"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepo_number"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelPO Number"
"ttFieldPropsFldColLabelPO Number"
"ttFieldPropsFldHelpEnter the PO Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamepo_suffix"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelPO Suffix"
"ttFieldPropsFldColLabelPO Suffix"
"ttFieldPropsFldHelpEnter the PO Suffix"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamereq_emp"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(6)"
"ttFieldPropsFldSideLabelRequesting Emp"
"ttFieldPropsFldColLabelRequesting Emp"
"ttFieldPropsFldHelpEmployee performing a requisition"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamereq_qty"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelRequested Quantity"
"ttFieldPropsFldColLabelRequested Qty"
"ttFieldPropsFldHelpRequested Quantity for this order line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameret_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelQuantity Returned"
"ttFieldPropsFldColLabelQuantity Returned"
"ttFieldPropsFldHelpQuantity returned for this order line"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamert_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelRT Number"
"ttFieldPropsFldColLabelRT Number"
"ttFieldPropsFldHelpRT id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamesame_lot"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelSame Lot"
"ttFieldPropsFldColLabelSame Lot"
"ttFieldPropsFldHelpForce picking all quantity from the same lot."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameserialflag"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelSerialized"
"ttFieldPropsFldColLabelSerialized"
"ttFieldPropsFldHelpEnter the Serialized"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameserial_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelSerial Number"
"ttFieldPropsFldColLabelSerial Number"
"ttFieldPropsFldHelpThe serial number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameship_cube"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelShip Cube"
"ttFieldPropsFldColLabelShip Cube"
"ttFieldPropsFldHelpEnter the ship_cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNameship_weight"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelShip Weight"
"ttFieldPropsFldColLabelShip Weight"
"ttFieldPropsFldHelpWeight calculated or input at ship time."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamestock_stat"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelStock Status"
"ttFieldPropsFldColLabelStock Status"
"ttFieldPropsFldHelpThe status of this stock item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNametax"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$ >>>,>>9.99"
"ttFieldPropsFldSideLabelTax"
"ttFieldPropsFldColLabelTax"
"ttFieldPropsFldHelpTax for this order line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamevendor_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(9)"
"ttFieldPropsFldSideLabelVendor ID"
"ttFieldPropsFldColLabelVendor ID"
"ttFieldPropsFldHelpVendor ID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettorddtlSrch"
"ttFieldPropsFldNamework_center"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(15)"
"ttFieldPropsFldSideLabelWork Center"
"ttFieldPropsFldColLabelWork Center"
"ttFieldPropsFldHelpEnter the Work Center"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNamesrcttorddtlsh"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTableorddtl_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcttorddtlsh"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.orddtl"
"ttDataSourceJoinscBufNameorddtl_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsid,line,line_sequence"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcttorddtlsh"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.item"
"ttDataSourceJoinscBufNameitem_1"
"ttDataSourceJoinscDBWhereitem_1.co_num = orddtl_1.co_num and item_1.wh_num = orddtl_1.wh_num and 
item_1.abs_num = orddtl_1.abs_num"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num,abs_num"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameY:\BE_Area\src\blp\SearchARItem_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextyes"
"ttOptionsmakePrevyes"
"ttOptionsmakeLastyes"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionyes"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTablettorddtlSrch"
"ttAttachSourcecSrcNamesrcttorddtlsh"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsabs_num,orddtl_1.abs_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsact_qty,orddtl_1.act_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsassigned,orddtl_1.assigned"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsbin_num,orddtl_1.bin_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldscharges,orddtl_1.charges"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldscomment,orddtl_1.comment"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsco_num,orddtl_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsdiscount,orddtl_1.discount"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsDiscountAmt,orddtl_1.DiscountAmt"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsdrop_cube,orddtl_1.drop_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsdrop_weight,orddtl_1.drop_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsExtdPrice,orddtl_1.ExtdPrice"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsfiller_flag,orddtl_1.filler_flag"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsfl_zone,orddtl_1.fl_zone"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsgift_wrap,orddtl_1.gift_wrap"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsGUID,orddtl_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldshost_origin,orddtl_1.host_origin"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsid,orddtl_1.id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsitem_desc,item_1.item_desc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline,orddtl_1.line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline_alt_number,orddtl_1.line_alt_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline_sequence,orddtl_1.line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline_status,orddtl_1.line_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldslot,orddtl_1.lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_employee,orddtl_1.msds_employee"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_packed,orddtl_1.msds_packed"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_required,orddtl_1.msds_required"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_sheet,orddtl_1.msds_sheet"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsordered_qty,orddtl_1.ordered_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorder_alt_num,orddtl_1.order_alt_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorder_alt_suf,orddtl_1.order_alt_suf"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorig_cube,orddtl_1.orig_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorig_req_qty,orddtl_1.orig_req_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorig_weight,orddtl_1.orig_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspackage_code,orddtl_1.package_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspick_line,orddtl_1.pick_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspool,orddtl_1.pool"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_line,orddtl_1.po_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_line_sequence,orddtl_1.po_line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_number,orddtl_1.po_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_suffix,orddtl_1.po_suffix"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsreq_emp,orddtl_1.req_emp"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsreq_qty,orddtl_1.req_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsret_qty,orddtl_1.ret_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsrt_num,orddtl_1.rt_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldssame_lot,orddtl_1.same_lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsserial_num,orddtl_1.serial_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsship_cube,orddtl_1.ship_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsship_weight,orddtl_1.ship_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsstock_stat,orddtl_1.stock_stat"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldstax,orddtl_1.tax"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsvendor_id,orddtl_1.vendor_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldswh_num,orddtl_1.wh_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldswork_center,orddtl_1.work_center"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"