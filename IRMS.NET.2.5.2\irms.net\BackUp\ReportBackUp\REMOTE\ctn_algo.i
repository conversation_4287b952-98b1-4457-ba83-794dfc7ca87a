/* Shared temp-tables used in ctn_algo */
/* @(#) $Header: /pdb/9.02/remote/RCS/ctn_algo.i,v 1.4 1999/04/28 21:24:11 jyy8301 Exp $ */


define {1} shared temp-table box_size no-undo      /* fitting table   */
    field box_id  like carton_size.box_id
    field length as decimal    
    field width  as decimal    
    field height as decimal    
    field cube   as decimal 
    field weight as decimal    
    field max_weight as decimal    
    field package_code as character 
    index i_large is primary cube   descending 
                        max_weight descending 
                            length descending
                            width  descending
                            height descending
    index i_small cube max_weight length width height 
.

define {1} shared temp-table tt_zone no-undo 
    field wh_zone as character 
    field control_pick_area as character 
    index ind_zone as unique primary wh_zone 
.

define {1} shared temp-table tt_item 
    no-undo
    field ch_item      as character /* abs_num        */
    field d_cube       as decimal   /* Cube           */
    field d_height     as decimal   /* Height         */
    field d_width      as decimal   /* Width          */
    field d_length     as decimal   /* Length         */
    field d_weight     as decimal   /* Length         */
    field ch_desc      as character /* Description    */
    field uom          as character /* uom            */
    field case_qty     as decimal   /* Case quantity  */
    field self_ship    as logical   /* self ship      */
    index ind_tt_item as primary unique ch_item 
.      
define {1} shared temp-table order_line no-undo 
    field num        as integer initial 0         
    field pickid    like irms.pick.id 
    field line       like irms.orddtl.line
    field seq        like irms.orddtl.line_sequence
    field item       like irms.orddtl.abs_num
    field item_desc  like irms.item.item_desc  
    field bin        like irms.orddtl.bin_num
    field qty        like irms.orddtl.req_qty
    field or_qty     like irms.orddtl.orig_req_qty
    field price      like irms.orddtl.charges
    field pack_req   as   logical
    field kit        as   logical
    field length     as   decimal          
    field width      as   decimal          
    field height     as   decimal          
    field cube       as   decimal 
    field s_weight     as   decimal 
    field weight     as   decimal 
    field zone       as   character
    field full_case   as   logical initial false 
    index idlineseq  is primary unique num line seq
    index index_cube bin cube descending        
.
define {1} shared temp-table order_carton no-undo 
    field pickid as integer  
    field box_num as integer  
    field bin_num as character
    field qty     as integer  
    field or_qty  as integer  
    field abs_num as character
    field item_desc as character 
    field full_case as logical 
    index index_ord_ctn is primary bin_num 
.        
define {1} shared temp-table order_carton_size no-undo 
    field box_num as integer 
    field box_size as character 
    field d_max_dim1 as decimal initial 0 
    field d_max_dim2 as decimal initial 0 
    field d_max_dim3 as decimal initial 0 
    field d_open_cube as decimal 
    field d_used_cube as decimal initial 0
    field d_open_weight as decimal 
    field d_used_weight as decimal initial 0
    field d_num_line as integer initial 0 
    field zone as character
    field ch_ctlzone as character 
    field ch_item as character 
    field l_same_item as logical 
    field l_self_ship as logical 
    index index_box_num is primary box_num 
    index index_ctl_zone ch_ctlzone zone 
.

/* end of ctn_algo.i */
