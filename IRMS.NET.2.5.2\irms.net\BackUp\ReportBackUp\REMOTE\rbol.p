/* <PERSON> 8/22/96
*** Prints Shipper List / Bill of Lading Report for ALP 
*** 
*** Modified by <PERSON> for IRMS 8/27/96
***
*/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/rf/RCS/rbol.p,v 1.7 1997/09/02 15:25:00 fwang Exp $~n"
.         

&SCOP DEBUG 0

&IF {&DEBUG} &THEN
   DEF VAR company            AS CHAR NO-UNDO INITIAL '01'  .
   DEF VAR warehouse          AS CHAR NO-UNDO INITIAL '100' .
   DEF VAR ch_order           AS CHAR NO-UNDO .
   DEF VAR ch_order_suffix    AS CHAR NO-UNDO .

   UPDATE 
      ch_order           FORMAT "x(10)" ch_order_suffix FORMAT "x(2)" 
      ch_shippers_number FORMAT "x(6)"  .
&ELSE
   DEF SHARED VAR       my_co_num          AS CHAR .
   DEF SHARED VAR       my_wh_num          AS CHAR .
   DEF INPUT  PARAMETER ch_order           AS CHAR NO-UNDO .
   DEF INPUT  PARAMETER ch_order_suffix    AS CHAR NO-UNDO .
&ENDIF

DEF        VAR    ch_printer         AS CHAR NO-UNDO .
DEF        VAR    ch_shippers_number AS CHAR NO-UNDO . 
DEF        VAR    ch_command         AS CHAR NO-UNDO .
DEF        VAR    d_tot_weight       AS DEC  NO-UNDO .
DEF        VAR    d_itm_weight       AS DEC  NO-UNDO .
DEF        VAR    d_tot_req_qty      AS DEC  NO-UNDO .
DEF        VAR    d_tot_act_qty      AS DEC  NO-UNDO .
DEF        VAR    i_page_num         AS INT  NO-UNDO .
DEF        VAR    i_page_limit       AS INT  NO-UNDO .
DEF        VAR    line_num           AS INT  NO-UNDO .
DEF        VAR    body_start         AS INT  NO-UNDO .
DEF        VAR    body_end           AS INT  NO-UNDO .
DEF        VAR    ch_phone           AS CHAR NO-UNDO .
DEF        VAR    ch_outfile         AS CHAR NO-UNDO .
DEF        BUFFER b_orddtl           FOR irms.orddtl .
DEF        QUERY  q_orddtl           FOR b_orddtl    .

ASSIGN             
   d_tot_weight  = 0
   d_itm_weight  = 0
   d_tot_req_qty = 0
   d_tot_act_qty = 0 
   i_page_num    = 0
   i_page_limit  = 86 
   line_num      = i_page_limit + 1
   body_start    = 26 
   body_end      = 63 
   ch_printer    = ( IF OPSYS = 'msdos' THEN 
&IF {&DEBUG} &THEN
                        "LPT1"
&ELSE
                        "\\mattg\genicoms" 
&ENDIF
                     ELSE
                        "F3" )
                   .
                   
RUN tmpfile.p ( OUTPUT ch_outfile ) .

&IF {&DEBUG} &THEN
   message 'Output file' ch_outfile view-as alert-box.
&ENDIF

/* ********************************************* */

/*
** MESSAGE "Bill of Lading for ALP" VIEW-AS ALERT-BOX BUTTONS OK-CANCEL
**    UPDATE choice AS LOGICAL .
**
** IF NOT choice THEN
** DO:
**     MESSAGE "Program NOT run." VIEW-AS ALERT-BOX INFO .
**     RETURN .
** END .
*/

    FIND ordhdr 
       WHERE
          ordhdr.co_num       = my_co_num       AND
          ordhdr.wh_num       = my_wh_num       AND
          ordhdr.order        = ch_order        AND
          ordhdr.order_suffix = ch_order_suffix
       NO-LOCK NO-ERROR .
       
    IF ERROR-STATUS:ERROR THEN
    DO:
        MESSAGE "Order does NOT exist" VIEW-AS ALERT-BOX INFO .
        RETURN .
    END .           
         
    FIND custmast 
       WHERE
          custmast.co_num    = my_co_num        AND
          custmast.wh_num    = my_wh_num        AND
          custmast.cust_code = ordhdr.cust_code
       NO-LOCK NO-ERROR .
   
    IF ERROR-STATUS:ERROR THEN
    DO:
/*    
**      MESSAGE "Customer not found from ordhdr" VIEW-AS ALERT-BOX INFO .
*/
        ASSIGN 
           ch_phone = "Phone not available" .
    END .                                      
    ELSE 
        ASSIGN
           ch_phone = custmast.phone .
    
    FIND carrier 
       WHERE 
          carrier.co_num     = my_co_num      AND
          carrier.wh_num     = my_wh_num      AND
          carrier.carrier_id = ordhdr.carrier
    NO-LOCK NO-ERROR .

    IF ERROR-STATUS:ERROR THEN
    DO:
        MESSAGE "Carrier NOT found in carrier table" VIEW-AS ALERT-BOX INFO .
        RETURN .
    END .

   /* Now write the report */
   OUTPUT TO VALUE ( ch_outfile ).

   RUN i_page_break     .

   OPEN QUERY q_orddtl 
      FOR EACH b_orddtl /*OF ordhdr*/ WHERE b_orddtl.id = ordhdr.id AND
                                            b_orddtl.co_num = ordhdr.co_num AND
                                            b_orddtl.wh_num = ordhdr.wh_num.

   orddtlloop:
   REPEAT:
      GET NEXT q_orddtl.
      
      IF NOT AVAILABLE ( b_orddtl ) THEN 
         LEAVE orddtlloop .
                             
      FIND item 
         WHERE 
            item.co_num  = b_orddtl.co_num AND
            item.wh_num  = b_orddtl.wh_num AND
            item.abs_num = b_orddtl.abs_num
         NO-LOCK NO-ERROR .
         
       IF ERROR-STATUS:ERROR THEN
       DO:
          MESSAGE "Item not found " b_orddtl.abs_num VIEW-AS ALERT-BOX.
          RETURN .
       END . 

       ASSIGN d_itm_weight = item.weight * b_orddtl.act_qty .
                      
       PUT UNFORMATTED
           my_wh_num                                  AT  1
           b_orddtl.abs_num                           AT  4           
           item.item_desc                             AT 30
           b_orddtl.orig_req_qty                      AT 61
           b_orddtl.act_qty                           AT 71
           (b_orddtl.orig_req_qty - b_orddtl.act_qty) AT 81
           d_itm_weight                               AT 93 .

       ASSIGN
          line_num = line_num + 1.
       
       IF line_num               > body_end THEN
          RUN i_page_break .

       IF d_itm_weight          <> ?        THEN
          ASSIGN
             d_tot_weight = d_tot_weight + d_itm_weight 
             d_itm_weight = 0 .

       IF b_orddtl.orig_req_qty <> ?        THEN
          ASSIGN
             d_tot_req_qty = d_tot_req_qty + b_orddtl.orig_req_qty .

       IF b_orddtl.act_qty      <> ?        THEN
          ASSIGN 
             d_tot_act_qty = d_tot_act_qty + b_orddtl.act_qty .
   END .               
   
   /********* Trailer ******************/
   PUT UNFORMATTED
      SKIP(2)
      "Total "                        AT 45
      d_tot_req_qty                   AT 61
      d_tot_act_qty                   AT 71
      (d_tot_req_qty - d_tot_act_qty) AT 81
      d_tot_weight                    AT 93 SKIP(1)
      "F.A.K. 60"                     AT  2         .

   OUTPUT CLOSE .

   CASE OPSYS:
      WHEN 'unix' THEN
         ASSIGN
            ch_command = "lp -d " + ch_printer + " -c " + ch_outfile .

      WHEN 'msdos' THEN
         ASSIGN
            ch_command = "copy " + ch_outfile + "   " + ch_printer + " > nul " .
      
      OTHERWISE
      DO:
         MESSAGE 'Invalid operating system.' VIEW-AS ALERT-BOX.
         RETURN .
      END.
   END CASE. /* Operating System . */   
   
   OS-COMMAND VALUE ( ch_command ) .

&IF {&DEBUG} = 0 &THEN   
   OS-DELETE  VALUE ( ch_outfile ) . 
&ENDIF

RETURN .

/*-------------------------------------------------------------*/
PROCEDURE i_page_break:
   DEF VAR temp_count AS INT NO-UNDO .
   
   ASSIGN
      temp_count = i_page_limit - line_num .
/*
**   REPEAT WHILE temp_count > 0:
**      PUT UNFORMATTED SKIP(1) .
**    
**      ASSIGN 
**         temp_count = temp_count - 1.
**   END .
*/
   PUT UNFORMATTED "~f" .   
   
   ASSIGN
      i_page_num = i_page_num + 1.

   PUT UNFORMATTED
      "~n"
      "W/O#"                             AT  85
      ordhdr.order        FORMAT "x(10)" AT  93
      ordhdr.order_suffix FORMAT "x(2)"  AT  105
      /* ch_shippers_number  FORMAT "x(6)"  AT 100 */
      today                              AT 110                    
      i_page_num          FORMAT ">>>9"  AT 120  SKIP(7)               
      ordhdr.bill_name                   AT   2
      ordhdr.ship_name                   AT  51  SKIP(1)
      ordhdr.bill_addr1                  AT   2 
      ordhdr.ship_addr1                  AT  51      
      ordhdr.bill_addr2                  AT   2 
      ordhdr.ship_addr2                  AT  51      
      ordhdr.bill_city                   AT   2 
      ordhdr.bill_state                  AT  24
      ordhdr.bill_zip                    AT  27
      ordhdr.ship_city                   AT  51
      ordhdr.ship_state                  AT  74
      ordhdr.ship_zip                    AT  77
      ch_phone                           AT   2  "~n"  /* SKIP (2) */
      ordhdr.ship_date                   AT  62  skip(1)
      ordhdr.cust_code                   AT   2
      ordhdr.customer_po                 AT  17
      carrier.name                       AT  48
      ordhdr.exp_ship_date               AT  70  
      ordhdr.freight_terms               AT  90  SKIP(2)  .     

   ASSIGN 
      line_num = 17 
      body_end = line_num + 25 .
END .    
