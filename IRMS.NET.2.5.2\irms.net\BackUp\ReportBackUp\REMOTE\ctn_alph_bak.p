/*
** glauber Tue 22aug95 07:37
** printord.p -- print customer orders
*/
    define input parameter ch_co as character no-undo .
    define input parameter ch_wh as character no-undo .
    define input parameter i_wave as integer no-undo .
    
/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/8.02/rf/RCS/ctn_prnn.p,v 1.9 1998/09/04 15:31:33 tanya Exp $~n"
.       
/***
** Mod History:
** 08Sep95 glauber: added print only mode, activated by
**              running this program interactively.
**              The full functionality is available
**              by running in batch mode.
**
** 09Sep95 glauber: printing multiple lines by location sequence.
**
** 09Sep95 glauber: printing orders by lowest location sequence
**                  (stored in the bin_from field by the print
**                  request online screen).
**
** 09Sep95 glauber: print locations in fixed-width font.
**
** 18Sep95 glauber: store print images to temp table
**                  so that they can be sorted by location
**                  (splitting orders when necessary).
**
** 26Sep95 glauber: printing of exploded kits.
**
** 27Sep95 glauber: printing of "zero-ships" (aka back-ordered items).
**
** 29Sep95 glauber: print "*" FOR item quantity in the description field.
**
** 04Oct95 glauber: another try at improving the carton sizing algorithm
** (considering all 3 dimensions instead of just the largest one)
**
** 18Oct95 glauber: a step towards the final version.
** Orders can be printed or reprinted:
**     * by batch (the default way)
**     * by order (one order at a time)
** The output can be
**     * to the queues (the default way)
**     * to files
**    
** This is handled by -param.
** 
**     -param is a comma-delimited list of parameters
** 
**     * first parameter: "Q" or "F" (queues or files)
**     * second parameter: "B" or "O" (batch or order)
**     * third parameter: the batch number or order/suffix separated by " "
**     * fourth parameter (optional): prefix for the file names ("F" only)
**              or a queue name ("Q" only)
**
** IF a queue name is used, all batches, all colors, 
** will be sent to that queue.
**
** Reprints happen automatically when the printed flag is set.
**
** 17Nov95 glauber: use the delivery route instead of door number.
**
** 01Dec95 glauber: B/O items won't influence the carton size
** anymore.
**
** 05Dec95 glauber: different sequences for different kinds of
** UPS label.
**
** 06Dec95 glauber: open output streams only when necessary.
**
** Notes about kits:
** -- Kits are already exploded when we see them here (they are exploded when 
**     the order is loaded).
** -- Kit components don't go in the cartons. Only the kit itself goes there.
**     This is important when reprinting.
** -- When printing, we print the kit name, but instead of a quantity we print
**     the word "KIT". THEN we print the components
**     in ascending location order
**     with their quantities.
** -- The price goes with the kit name. No price with the components.
** -- The word "(KIT)" added to the beginning of the kit's description.
** -- The description for the components is indented a few spaces.
**
** 07aug97 glauber:
** I wouldn't trust too much the comments above...
**
** Parameters passed through environment variables:
**   PRINT_WAVE: batch     (integer)
**   PRINT_CO:   company   (character)
**   PRINT_WH:   warehouse (character)
**
** 27aug97 Peter:
** Added the automatic upgrade of UPS ground orders to 
** Alaska and Hawaii to 2nd day.  This is to help an ASD
** bug...
**
** 04sep97 Peter:
**   Added call for maxi-code label.  (Maxi-code label format 
**   prepared by G.)
**
** 10sep97 Peter:
**   Changing file name from hardcoding to wave + service + suffix file and
**   adding print rotation call instead of wavedump.p .
**
** 18sep97 Peter:
**    Adding the following information to be stored in the cartonmst:
**       sequence, weight, height, length, x_of_y, and print_form.
**
**  TO DO:
**  pjk --> DONE 9-18-97    1) Print Rotation.
**  pjk --> DONE 9-18-97    2) Rework loading of finished cartons...
**  pjk --> DONE 9-18-97    3) Num-cartons to an input function. 
**                                This eliminate size changing.
**  pjk --> DONE 9-18-97    4) Reprints by carton id                
**
**
**  04sep98 Tanya:
**  changed all printed field position to make them fit on new form
**  added warehouse info, hardcoded status code info
***/


/* <<<<<<<<<< CONSTANTS >>>>>>>>>> */
&SCOP PS_FILE              ps
&SCOP BLACK_FILE           gd
&SCOP RED_FILE             nd
&SCOP BLUE_FILE            2d
&SCOP SUF_FILE             .out
&SCOP CARRIER_UPS         "UPS"
&SCOP CARRIER_USPS        "USPS"
&SCOP SERVICE_NEXTDAY     "ND"
&SCOP SERVICE_SECONDDAY   "2D"
&SCOP SERVICE_GROUND      "GD"
&SCOP UPS_DATA_IDENTIFIER "1Z"
&SCOP UPS_SHIPPER_NUMBER  "613933"
&SCOP USPS_BULK_PERMIT    "2639"
&SCOP MAX_LINES_IN_FORM    8    

&Scop DEBUGGING            0

/* <<<<<<<<<< globals >>>>>>>>>> */
DEF NEW GLOBAL SHARED VAR    apage    AS CHAR NO-UNDO .
DEF NEW GLOBAL SHARED STREAM idstream .
DEF NEW GLOBAL SHARED STREAM 2dstream .
DEF NEW GLOBAL SHARED STREAM gdstream .
DEF NEW GLOBAL SHARED STREAM psstream .
    
/* <<<<<<<<<< file-wide variables >>>>>>>>>> */
/*
** The ups check-digit table:
** To find code subtract 47 from the ASCII value of
** character. Only numbers AND upperCASE alphabeticals
** are supported.              
** Note compiler bug IN Progress 7.3A: i need an extra space separating
** the ";" from the comment start AND finish. ELSE, i get a compile error.
*/
DEF VAR ups_checktable AS INT NO-UNDO extent 43 INITIAL
    [
        0, /*0*/   1, /*1*/   2, /*2*/   3, /*3*/   4, /*4*/
        5, /*5*/   6, /*6*/   7, /*7*/   8, /*8*/   9, /*9*/
        ?, /*:*/   ?, /* ; */ ?, /*<*/   ?, /*=*/   ?, /*>*/
        ?, /*?*/   ?, /*@*/   2, /*A*/   3, /*B*/   4, /*C*/
        5, /*D*/   6, /*E*/   7, /*F*/   8, /*G*/   9, /*H*/
        0, /*I*/   1, /*J*/   2, /*K*/   3, /*L*/   4, /*M*/
        5, /*N*/   6, /*O*/   7, /*P*/   8, /*Q*/   9, /*R*/
        0, /*S*/   1, /*T*/   2, /*U*/   3, /*V*/   4, /*W*/
        5, /*X*/   6, /*Y*/   7  /*Z*/
    ] .

DEF VAR timestamp             AS CHAR NO-UNDO INIT "" . /* time stamp FOR the start of this batch generation */
DEF VAR the_batch             AS INT  NO-UNDO .
DEF VAR i_wh_zip              AS INT  NO-UNDO .
DEF VAR ch_parcel_carrier     AS CHAR NO-UNDO INIT 'UP'  .
DEF VAR i_num_cartons_in_file AS INT  NO-UNDO INIT ?     . /* Num. cartons in printer file */
DEF VAR file_prefix           AS CHAR NO-UNDO INIT "/tmp/" .
DEF VAR paramlist             AS CHAR NO-UNDO .
DEF VAR selector              AS CHAR NO-UNDO .
DEF VAR the_order             AS CHAR NO-UNDO .
DEF VAR the_suffix            AS CHAR NO-UNDO .
DEF VAR queue                 AS CHAR NO-UNDO INIT "" .

DEF TEMP-TABLE tt_item 
   FIELD ch_abs       AS CHAR /* Item Information... */
   FIELD d_height     AS DEC  /* Item Height         */
   FIELD d_width      AS DEC  /* Item Width          */
   FIELD d_length     AS DEC  /* Item Length         */
   FIELD l_self_ship  AS LOG  /* Self Ship Flag      */
   FIELD ch_item_desc AS CHAR /* Item Description    */
   INDEX ind_tt_item AS PRIMARY UNIQUE ch_abs.
      
DEF TEMP-TABLE order_line NO-UNDO
   FIELD pickid     AS   INT  
   FIELD id         AS   INT 
   FIELD line       like irms.orddtl.line
   FIELD seq        like irms.orddtl.line_sequence
   FIELD item       like irms.orddtl.abs_num
   FIELD item_desc  like irms.item.item_desc  
   FIELD bin        like irms.orddtl.bin_num
   FIELD qty        like irms.orddtl.req_qty
   FIELD or_qty     like irms.orddtl.orig_req_qty
   FIELD price      like irms.orddtl.charges
   FIELD pack_req   AS   LOG
   FIELD kit        AS   LOG
   FIELD dim        AS   DEC     extent 3
   FIELD cube       AS   DEC
   FIELD zone       AS   CHAR  
   INDEX idlineseq  IS PRIMARY UNIQUE id line seq
   INDEX index_cube bin cube DESCENDING        .

DEF WORK-TABLE order_kit NO-UNDO
    FIELD kit_num like irms.kitmst.kit_num
    FIELD abs_num like irms.kitdtl.abs_num
    FIELD bin_num like irms.orddtl.bin_num
    FIELD qty     like irms.kitdtl.qty        .

/*
** Hold the print commands to print each page,
** so that we can print everything in location sequence
** at the end .
*/
DEF TEMP-TABLE order_form NO-UNDO
    FIELD queue      AS CHAR
    FIELD sortkey    AS CHAR
    FIELD box_size   AS CHAR 
    FIELD zone       AS CHAR 
    FIELD section    AS CHAR 
    FIELD printimage AS CHAR
    FIELD carton_id  AS CHAR 
    FIELD height     AS DEC
    FIELD width      AS DEC
    FIELD length     AS DEC
    INDEX i_loc      IS PRIMARY queue zone section box_size sortkey 
    INDEX ind_zone   zone queue .

DEF TEMP-TABLE tt_carrier
   FIELD carrier_id LIKE carrier.carrier_id
   FIELD city       LIKE carrier.city
   FIELD state      LIKE carrier.state
   FIELD shipper_id LIKE carrier.shipper_id 
   INDEX ind_carr   AS PRIMARY carrier_id .

DEF TEMP-TABLE tt_zone NO-UNDO
   FIELD wh_zone            AS CHAR 
   FIELD control_pick_area  AS CHAR 
   INDEX ind_zone AS UNIQUE PRIMARY wh_zone
   .

/*
** 05dec95 14:03 glauber
** This WORK-TABLE used FOR cartoning an order.
** Cartoning now is done before the order is printed...
*/
DEF TEMP-TABLE order_carton NO-UNDO   
    FIELD pickid    AS INT 
    FIELD box_num   AS INT
    FIELD bin_num   AS CHAR
    FIELD qty       AS INT
    FIELD or_qty    AS INT
    FIELD abs_num   AS CHAR
    FIELD item_desc AS CHAR
    FIELD price     as DEC DECIMALS 2
    FIELD kit       AS LOG        
    INDEX index_ord_ctn IS PRIMARY bin_num .

DEF TEMP-TABLE order_carton_size NO-UNDO
    FIELD box_num     AS INT                /* Id of box used w/ order_carton */
    FIELD box_size    AS CHAR               /* Box id from box_size           */
    FIELD d_max_dim1  AS DEC     INIT 0     /* Largest dim1 in box            */
    FIELD d_max_dim2  AS DEC     INIT 0     /* Largest dim2 in box            */
    FIELD d_max_dim3  AS DEC     INIT 0     /* Largest dim3 in box            */
    FIELD d_open_cube AS DEC                /* Open cube in box               */
    FIELD d_used_cube AS DEC     INIT 0     /* Cube used in box               */
    FIELD d_num_line  AS INT     INIT 0     /* Number of lines on carton      */
    FIELD zone        AS CHAR               /* Zone                           */
    FIELD ch_ctlzone  AS CHAR               /* Control Pick area              */    
    FIELD ch_item     AS CHAR               /* Item in box                    */
    FIELD l_same_item AS LOG INIT TRUE  /* Is same item in box?           */
    FIELD l_self_ship AS LOG INIT FALSE /* Is this a self ship item       */ 
    INDEX index_box_num IS PRIMARY  box_num 
    INDEX index_ctl_zone ch_ctlzone zone     
    .

DEF TEMP-TABLE box_size NO-UNDO               /* Cartib Fitting table  */
   FIELD box_id  like carton_size.box_id
   FIELD dim1    AS   DEC                     /* dimensions are sorted */
   FIELD dim2    AS   DEC
   FIELD dim3    AS   DEC
   FIELD cube    AS   DEC
   INDEX i_large IS   PRIMARY cube DESCENDING 
                              dim1 DESCENDING 
                              dim2 DESCENDING 
                              dim3 DESCENDING  
   INDEX i_small cube dim1 dim2 dim3 .


/* If true, print only, don't do anything - for specific order/carton ... */
DEF VAR print_only AS LOG NO-UNDO INITIAL FALSE .

/* flags used at output time */
DEF VAR any_gd         AS LOG NO-UNDO INIT FALSE .
DEF VAR any_1d         AS LOG NO-UNDO INIT FALSE .
DEF VAR any_2d         AS LOG NO-UNDO INIT FALSE .
DEF VAR any_ps         AS LOG NO-UNDO INIT FALSE .
DEF VAR init_cmd       AS CHAR    NO-UNDO INIT "" .
DEF VAR eject_cmd      AS CHAR    NO-UNDO INIT "" .
DEF VAR workstring     AS CHAR    NO-UNDO .
DEF VAR workstring2    AS CHAR    NO-UNDO .
DEF VAR ch_box_size    AS CHAR    NO-UNDO .            /* Box size for sort      */
DEF VAR ch_zone        AS CHAR    NO-UNDO .            /* Starting Zone for Box */
DEF VAR workint        AS INT     NO-UNDO .
DEF VAR workdec        AS DEC     NO-UNDO .
DEF VAR totalpages     AS INT     NO-UNDO .
DEF VAR thispage       AS INT     NO-UNDO .
DEF VAR the_sortkey    AS CHAR    NO-UNDO .
DEF VAR printer_name   AS CHAR    NO-UNDO INIT "LJ3" .
DEF VAR printer_driver AS HANDLE .
DEF VAR door_number    AS CHAR    NO-UNDO .
DEF VAR ups_barcode    AS CHAR    NO-UNDO .
DEF VAR orders_printed AS INT     NO-UNDO INIT 0 .
DEF VAR labels_printed AS INT     NO-UNDO .
DEF VAR i_count        AS INT     NO-UNDO INIT 0  .
DEF VAR subtotal       AS DEC     NO-UNDO INIT 0  .   /* order totals */
DEF VAR grandtotal     AS DEC     NO-UNDO INIT 0  .
DEF VAR ch_work        AS CHAR    NO-UNDO .
DEF VAR ch_printer     AS CHAR    NO-UNDO INIT ?  .
DEF VAR i              AS INT     NO-UNDO INIT ?  .
DEF VAR ch_section     AS CHAR    NO-UNDO INIT "" . /*Section within carton*/

/* tanya: warehouse info  was hard coded on the old form */
DEF VAR ch_wh1         AS CHAR format "x(25)" NO-UNDO . /* wh description */
DEF VAR ch_wh2         AS CHAR                NO-UNDO . /* wh address 1   */
DEF VAR ch_wh3         AS CHAR                NO-UNDO . /* wh address 2   */
DEF VAR ch_wh4         AS CHAR                NO-UNDO . /* city/state/zip */
DEF VAR ch_wh5         AS CHAR    NO-UNDO . /* wh country */

DEF VAR i_sequence     AS INT NO-UNDO INIT ?. /*Carton sequence to reprint*/
                                                                                    
DEF VAR i_oneday_ord   AS INT NO-UNDO INIT 0  . /* Number of 1st day Orders*/
DEF VAR i_twoday_ord   AS INT NO-UNDO INIT 0  . /* Number of 2nd day Orders*/
DEF VAR i_ground_ord   AS INT NO-UNDO INIT 0  . /* Number of ground orders */
DEF VAR i_postal_ord   AS INT NO-UNDO INIT 0  . /* Number of postal orders */
DEF VAR i_gold_ord     AS INT NO-UNDO INIT 0  . /* Number of gold room orders*/
DEF VAR ch_ord_comment AS CHAR NO-UNDO INIT '' . /* Order Comment    */
DEF VAR ch_1d_file     AS CHAR NO-UNDO FORMAT 'X(14)'. /* 1 day file */
DEF VAR ch_2d_file     AS CHAR NO-UNDO FORMAT 'X(14)'. /* 2 day file */
DEF VAR ch_gd_file     AS CHAR NO-UNDO FORMAT 'X(14)'. /* GD file  */
DEF VAR ch_ps_file     AS CHAR NO-UNDO FORMAT 'X(14)'. /* PS file  */
DEF VAR ch_x_of_y      AS CHAR NO-UNDO . /* Stores reprints X of Y */

/* <<<<<<<<<< buffers >>>>>>>>>> */
DEF BUFFER b_header      FOR irms.ordhdr       .
DEF BUFFER b_detail      FOR irms.orddtl       .
DEF BUFFER b_pick        FOR irms.pick         .
DEF BUFFER b_transa      FOR irms.AuditLog .
DEF BUFFER b_cms         FOR irms.cartonmst    .
DEF BUFFER b_cdt         FOR irms.cartondtl    .
DEF BUFFER b_parcel_zone FOR irms.parcel_zone  .
DEF BUFFER b_wh          FOR irms.whmst        .
DEF BUFFER b_item        FOR tt_item           .

DEF QUERY  q_header FOR b_header   .
DEF QUERY  q_of     FOR order_form .

/* <<<<<<<<<< ROCK'N'ROLL >>>>>>>>>> */
ASSIGN
    timestamp = STRING( today, "99/99/9999" ) + " " + STRING( time, "HH:MM AM" ) .

/* PUT UNFORMATTED "Printord: Start RUN: " timestamp ".~n" . */

&IF {&DEBUGGING} &THEN
   
    ASSIGN
       queue      = 'LJ3'
       the_batch  = 4316     
       ch_co      = '01'
       ch_wh      = 'a5'
       i_sequence = 1
       .
&ELSE
   /*
   ** Read parameters from the environment
   */
   /********************
   ASSIGN
      ch_work = OS-GETENV( "PRINT_WAVE" )
      i_wave  = INTEGER  ( ch_work      )
      ch_co   = OS-GETENV( "PRINT_CO"   )
      ch_wh   = OS-GETENV( "PRINT_WH"   )
      NO-ERROR .
    ********************/

   IF (i_wave = ?) THEN
   DO:
       MESSAGE IF ch_work = ? THEN 
                  "Undefined WAVE."
               ELSE 
                 "Invalid WAVE: " + ch_work .
       QUIT .
   END .

   IF (ch_co eq ?)THEN
   DO:
      Message "Undefined company." .
      QUIT .
   END .

   IF (ch_wh eq ?) THEN
   DO:
      MESSAGE "Undefined warehouse." .
      QUIT .
   END .
   
   ASSIGN
      ch_work               = OS-GETENV( "PRINT_CARTON_IN_FILE" )
      i_num_cartons_in_file = INTEGER  ( ch_work      )
      NO-ERROR .
   
   IF i_num_cartons_in_file = ? THEN
      ASSIGN
         i_num_cartons_in_file = 1000 .


   ASSIGN
      ch_work    = OS-GETENV( "PRINT_LABEL_SEQ" )
      i_sequence = INTEGER  ( ch_work      )
      NO-ERROR .   

   ASSIGN 
      the_batch = i_wave 
      queue     = ch_printer .

   IF i_sequence > 0 THEN
       MESSAGE 'Reprinting sequence ' i_sequence '~n'.
   ELSE 
       i_sequence = ? .

&ENDIF

/* MESSAGE "Printing batch " the_batch ".~n" . */

FIND FIRST b_wh
   WHERE
      b_wh.co_num = ch_co AND
      b_wh.wh_num = ch_wh 
   NO-LOCK NO-ERROR .

IF NOT AVAILABLE ( b_wh ) THEN
DO:
   message 'Warehouse is not available...' .
   RETURN .
END.

ASSIGN   
   ch_wh1 = b_wh.wh_desc 
   ch_wh2 = b_wh.addr1 
   ch_wh3 = b_wh.addr2   
   ch_wh4 = b_wh.city + ', ' + b_wh.state + '   ' + b_wh.zip 
   ch_wh5 = b_wh.country 
   .
      


/***
** Get warehouse shipping information... 
***/
ASSIGN
   i_wh_zip = INT ( SUBSTRING ( b_wh.zip , 1, 3 ) ) 
   .
/************ @mike
FIND FIRST b_parcel_zone
   WHERE
      b_parcel_zone.carrier_zone = ch_parcel_carrier  AND
      b_parcel_zone.from_zip     = i_wh_zip 
   NO-LOCK NO-ERROR .
   
IF NOT AVAILABLE ( b_parcel_zone ) THEN
DO:
   message 'Carrier parcel zone not properly set up...' .
   RETURN .
END.
**************/      
/***
** Load all the carrier information...
***/
FOR EACH carrier 
   WHERE
      carrier.co_num = ch_co AND
      carrier.wh_num = ch_wh 
   NO-LOCK :
                           
   CREATE tt_carrier .
                                 
   ASSIGN
      tt_carrier.carrier_id = carrier.carrier_id 
      tt_carrier.city       = carrier.city
      tt_carrier.state      = carrier.state
      tt_carrier.shipper_id = carrier.shipper_id 
      .
   
   &IF {&DEBUGGING} &THEN
      message tt_carrier .
   &ENDIF
END.

/***
** Load all the cartons...
***/
FOR EACH carton_size 
   WHERE
      carton_size.row_status = YES    AND
      carton_size.co_num     = ch_co  AND
      carton_size.wh_num     = ch_wh
   NO-LOCK:  /* set up the carton sizes */
   
   CREATE box_size .
    
   ASSIGN
      box_size.box_id = carton_size.box_id
      box_size.cube   = carton_size.cube
      box_size.dim1   = carton_size.length
      box_size.dim2   = carton_size.width
      box_size.dim3   = carton_size.height
      .  
    
   RUN sort3.p ( input-output box_size.dim1,
                 input-output box_size.dim2,
                 input-output box_size.dim3 ) .
END .

/***
**   Load all warehouse zones...
**     - Control Pick Area is the key to the future of cartonizationing...
***/
FOR EACH irms.wh_zone
   WHERE
      co_num = ch_co  AND
      wh_num = ch_wh 
   NO-LOCK :
   
   CREATE tt_zone .
   
   ASSIGN
      tt_zone.wh_zone           = wh_zone.wh_zone 
      tt_zone.control_pick_area = irms.wh_zone.control_pick_area 
      .
END.

CASE printer_name:
    WHEN "LJ3" THEN  /* Laser Jet III */
        RUN lprintor.p persistent set printer_driver .

    OTHERWISE
    DO:
        MESSAGE "Unsupported printer: " + printer_name .
        RETURN error .
    END .
END CASE .

RUN do_it .

/* MESSAGE orders_printed " orders generated.~nGeneration finished.~n" . */

/*** Debug sums...      
&IF {&DEBUGGING} &THEN

   OPEN QUERY q_of
      FOR EACH order_form 
         USE-INDEX i_loc .
   
   MESSAGE  'Output locations to temp file( /tmp/loc2.txt).' view-as alert-box.
   OUTPUT TO /tmp/loc2.txt .
 

   printloop:
   REPEAT:
      GET NEXT q_of .
   
      IF NOT AVAILABLE ( order_form ) THEN 
         LEAVE printloop.
   
      message order_form.zone ',' order_form.section ',' order_form.box_size ',' order_form.sortkey.
   END.

   MESSAGE 'Done' View-as alert-box.   
   
&ENDIF
***/

RUN printbuffer .

/***
**   Clean up time...
***/
RUN destroy IN printer_driver .

/* PUT UNFORMATTED "END RUN.~n" . */

OUTPUT CLOSE .

QUIT .

/* <<<<<<<<<< THE END >>>>>>>>>> */

/* <<<<<<<<<< INTERNAL PROCEDURES >>>>>>>>>> */
PROCEDURE do_it:
   DEF BUFFER b_cartonmst FOR irms.cartonmst .
  
   IF i_sequence NE ? THEN
   DO:
      FIND FIRST b_cartonmst
         WHERE
            b_cartonmst.co_num   = ch_co      AND
            b_cartonmst.wh_num   = ch_wh      AND
            b_cartonmst.batch    = the_batch  AND
            b_cartonmst.sequence = i_sequence    
         NO-LOCK NO-ERROR .
      
      IF NOT AVAILABLE ( b_cartonmst ) THEN
         OPEN QUERY q_header
            FOR EACH b_header 
               WHERE
                  b_header.co_num = ch_co     AND
                  b_header.wh_num = ch_wh     AND
                  b_header.batch  = the_batch
               EXCLUSIVE .
      ELSE
         OPEN QUERY q_header
            FOR EACH b_header 
               WHERE
                  b_header.co_num       = ch_co                    AND
                  b_header.wh_num       = ch_wh                    AND
                  b_header.batch        = the_batch                AND
                  b_header.order        = b_cartonmst.order        AND
                  b_header.order_suffix = b_cartonmst.order_suffix 
               EXCLUSIVE .
 
   END .
   ELSE
      OPEN QUERY q_header
         FOR EACH b_header 
            WHERE
               b_header.co_num eq ch_co     and
               b_header.wh_num eq ch_wh     and
               b_header.batch  eq the_batch
            EXCLUSIVE .
    
   ASSIGN
      orders_printed = 0
      .

   printloop:
   REPEAT TRANSACTION ON ERROR UNDO printloop, NEXT printloop:

      GET NEXT q_header .
        
      IF NOT AVAILABLE( b_header ) THEN
      DO:
         CLOSE QUERY q_header .
         LEAVE printloop .
      END .

      IF (b_header.order_status = "S") THEN
         NEXT printloop . 

      ASSIGN 
         print_only = CAN-FIND( FIRST b_cms WHERE
                        b_cms.co_num       = b_header.co_num       AND
                        b_cms.wh_num       = b_header.wh_num       AND
                        b_cms.order        = b_header.order        AND
                        b_cms.order_suffix = b_header.order_suffix ) .

      IF NOT print_only THEN
         RUN close_transaction .
      /***********@mike
      IF ( b_header.carrier NE {&CARRIER_UPS}    AND
           b_header.carrier NE {&CARRIER_USPS} ) THEN 
      DO:
         PUT UNFORMATTED
            "Order " b_header.order " " b_header.order_suffix
            " skipped because carrier " b_header.carrier " is unknown.~n" .
              
         UNDO printloop, NEXT printloop .
      END .
      **************/

/**********@mike
      IF b_header.carrier     = {&CARRIER_UPS}    AND
         b_header.service     = {&SERVICE_GROUND} AND
         b_parcel_zone.zone[ INT ( SUBSTRING ( b_header.ship_zip, 1 , 3 ) ) ] = ?
         THEN
      DO:
         MESSAGE b_header.order b_header.service 
                 b_header.service ' is being upgraded in service' .
/*
         ASSIGN
            b_header.service = {&SERVICE_SECONDDAY} .
*/   
      END.
************/
      
      RUN load_comments ( OUTPUT ch_ord_comment ) .
        
      RUN print_order NO-ERROR .

      IF ERROR-STATUS:ERROR THEN
         undo printloop, next printloop .

      IF b_header.printed = FALSE THEN  
         RUN mark_order_printed NO-ERROR.
           
      ASSIGN
         orders_printed = orders_printed + 1 .
        
      IF (orders_printed modulo 100 eq 0) THEN
         /* PUT UNFORMATTED orders_printed " orders generated...~n" . */
   END . /*printloop*/           

   IF (orders_printed modulo 100 ne 0) THEN
      /* PUT UNFORMATTED orders_printed " orders generated...~n" . */

END PROCEDURE . /*do_it*/


/*************************************************************/
PROCEDURE print_order:
    DEF QUERY q_cms FOR b_cms .
    DEF VAR   ch_zip     AS CHAR NO-UNDO FORMAT 'X(10)'.
  

    ASSIGN
        thispage    = 1
        grandtotal  = 0
        subtotal    = 0
        door_number = ''
        .

    IF NOT print_only THEN
       RUN set_totalpages ( INPUT  b_header.id, OUTPUT totalpages ) .
    ELSE
       ASSIGN
          totalpages = 1 .

    IF print_only THEN
       OPEN QUERY q_cms
          FOR EACH b_cms NO-LOCK
             WHERE
                b_cms.co_num       = b_header.co_num       AND
                b_cms.wh_num       = b_header.wh_num       AND
                b_cms.order        = b_header.order        AND
                b_cms.order_suffix = b_header.order_suffix AND
                b_cms.batch        = b_header.batch        AND
                b_cms.sequence     = i_sequence
             .

    outloop:
    DO WHILE thispage LE totalpages:

       IF NOT print_only THEN
       DO:
          RUN make_ups_barcode( OUTPUT ups_barcode ) .

          RUN create_carton NO-ERROR .

          IF ERROR-STATUS:ERROR THEN
              RETURN error .
       END .
       ELSE 
       DO:
          GET NEXT q_cms .
          
          IF NOT AVAILABLE(b_cms) THEN /* should never happen */
             LEAVE outloop .

          IF NOT CAN-FIND( FIRST b_cdt /*OF b_cms */
                              WHERE b_cdt.carton_num = b_cms.carton_num AND
                                 b_cdt.qty gt 0 ) THEN
             NEXT outloop .

          ASSIGN 
             ups_barcode = b_cms.tracking_id 
             ch_x_of_y   = b_cms.x_of_y
             .
       END .
        
       IF b_header.carrier = 'UPS' THEN
       DO:
          ASSIGN 
             ch_zip = 
                 STRING ( REPLACE ( b_header.ship_zip , '-', '' ) , 'X(10)' )
             .           
              
          IF LENGTH ( ch_zip ) < 9 THEN
          DO:
             ASSIGN
                ch_zip = ch_zip + FILL ( '0', 9 - LENGTH ( ch_zip ) ) 
                .
          END.
           
          RUN upsmaxicodeat IN printer_driver(3.0, 1.9, ch_zip,ups_barcode).
       END.
        
       RUN outdoornumber . /* the big door number    */
       RUN outupsbar     . /* ups bar code           */ 
       RUN outaddr( 1 )  . /* first copy of address  */
      
       RUN outxofy       . /* x of y                 */  
      
       RUN outaddr( 2 )  . /* second copy of address */
            
       RUN outordbar     . /* barcoded order         */
       
       RUN outcomments   . /* Output order comments  */ 
      
       RUN outordinfo    . /* human-readable order   */
      
       /* from company */
       RUN outaddr( 3 )  . /* third copy of address  */   
       
       RUN order_info   .
 /*  message "print only = " print_only view-as alert-box . */   
       IF print_only THEN  /* line stuff */
          RUN outlines_reprint .
       ELSE 
          RUN outlines         .
        
       RUN set_output_form .
            
       ASSIGN
          apage          = ""
          thispage       = thispage + 1
          labels_printed = labels_printed + 1 .
          
    END . /*outloop*/          

END PROCEDURE . /*print_order*/

/***************************************************/
PROCEDURE order_info:
   
   ASSIGN
        workstring = TRIM(b_header.order) + " " + TRIM(b_header.order_suffix) .

   RUN addressfont IN printer_driver .
          
   RUN printat IN printer_driver ( 0.5, 4.2, "Carrier: ") .               
   RUN printat IN printer_driver ( 2.0, 4.2, CAPS(b_header.carrier) ) .
   RUN printat IN printer_driver ( 0.5, 4.4, "Pro Number: ") .
   RUN printat IN printer_driver ( 2.0, 4.4, CAPS(b_header.pro_number)) .
   RUN printat IN printer_driver ( 0.5, 4.6, "Order: ") .
   RUN printat IN printer_driver ( 2.0, 4.6, workstring) .
   RUN printat IN printer_driver ( 0.5, 4.8, "Class: ") .
   RUN printat IN printer_driver ( 2.0, 4.8, CAPS(b_header.class)) .
   RUN printat IN printer_driver ( 0.5, 5.0, "Carton ID: ") .
   RUN printat IN printer_driver ( 2.0, 5.0, b_cms.carton_id) .
 
END PROCEDURE . /* order_info */
/***************************************************/
PROCEDURE set_output_form:
   DEF VAR thestream AS CHAR NO-UNDO .

   CASE b_header.carrier:
      WHEN {&CARRIER_UPS} THEN
      DO:
         CASE b_header.service:
            WHEN {&SERVICE_NEXTDAY} THEN
               ASSIGN thestream = "idstream" .

            WHEN {&SERVICE_SECONDDAY} THEN
               ASSIGN thestream = "2dstream" .

            WHEN {&SERVICE_GROUND} THEN
               ASSIGN thestream = "gdstream" .

            OTHERWISE /* BAD! @@@ insert logging @@@*/
               PUT UNFORMATTED "Invalid service" " " b_header.service "!~n" .
         END CASE . /*UPS SERVICE*/
     END . /*UPS*/

     WHEN {&CARRIER_USPS} THEN
        ASSIGN thestream = "psstream" .

     OTHERWISE
        ASSIGN thestream = "gdstream" .
   END CASE .

   CREATE order_form .

   ASSIGN
      order_form.queue      = thestream
      order_form.sortkey    = the_sortkey
      order_form.box_size   = ch_box_size
      order_form.printimage = apage 
      order_form.section    = ch_section
      order_form.zone       = ch_zone    
      order_form.carton_id  = ups_barcode
      .
END PROCEDURE . /* output_form */


/*****************************************************/
PROCEDURE set_totalpages:
   DEF INPUT  PARAMETER the_id     AS INT NO-UNDO.
   DEF OUTPUT PARAMETER totalpages AS INT NO-UNDO.

   DEF VAR    linecount    AS   INT                 NO-UNDO .
   DEF VAR    theqty       LIKE irms.orddtl.req_qty NO-UNDO .
   DEF VAR    sumcubes     AS   DEC                 NO-UNDO .
   DEF VAR    maxdim       AS   DEC                 NO-UNDO EXTENT 3 .
   DEF VAR    FIRST_in_box AS   LOG             NO-UNDO .

   DEF BUFFER b_order      FOR  irms.ordhdr .
   DEF BUFFER b_kitline    FOR  irms.orddtl .   
   DEF BUFFER b_carton     FOR  irms.cartonmst .
   DEF QUERY  q_size       FOR  box_size .

   /* 
   ** initialize PROCEDURE 
   */
   FIND FIRST b_order
      WHERE 
         b_order.id = the_id
      NO-LOCK .

   ASSIGN 
       totalpages = 0
       linecount  = 0 
       .

   RUN clean_up_buffers  . /* Clean up temp buffers */

   RUN get_pick_info  ( b_order.order, b_order.order_suffix, b_order.id    ) .
   RUN get_order_info ( b_order.id   ) .

/***
** Now we have all the pertinent order line information in
** one convenient place. It's time to translate it to
** carton information.
***/
   ASSIGN 
      totalpages = 0 .

   RUN carton_rest ( INPUT-OUTPUT totalpages ) .

END PROCEDURE . /*set_totalpages*/


/************************************************************/
PROCEDURE get_pick_info:
/* 
** for each pick of the order line, add the line item to the 
** order_line information table. 
*/
   DEF INPUT PARAMETER ch_order  AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_suffix AS CHAR NO-UNDO .
   DEF INPUT PARAMETER i_id      AS INT  NO-UNDO .
   
   DEF BUFFER b_pick    FOR irms.pick   .
   DEF BUFFER b_kitline FOR irms.orddtl .
   DEF QUERY  q_pick    FOR b_pick      .
   DEF VAR    i_new_id  AS  INT          NO-UNDO INITIAL 0.
   
   OPEN QUERY q_pick
      FOR EACH b_pick NO-LOCK
         WHERE
            b_pick.co_num       = ch_co     AND
            b_pick.wh_num       = ch_wh     AND
            b_pick.order        = ch_order  AND
            b_pick.order_suffix = ch_suffix AND
            b_pick.batch        = the_batch
         BY bin_num .
            
   pickloop:
   REPEAT:
      GET NEXT q_pick NO-LOCK .
      
      IF NOT AVAILABLE b_pick THEN
         LEAVE pickloop.
  
      CREATE order_line .

      ASSIGN
         order_line.pickid = b_pick.id
         order_line.id     = i_new_id
         order_line.line   = b_pick.line
         order_line.seq    = b_pick.line_sequence
         order_line.item   = b_pick.abs_num
         order_line.bin    = b_pick.bin_num
         order_line.qty    = IF (b_pick.pick_status = "O") THEN
                                b_pick.qty
                             ELSE 
                                0
         order_line.or_qty = b_pick.orig_qty
         i_new_id          = i_new_id + 1 
         order_line.zone   = b_pick.wh_zone 
         .

/****** Debug Code ****
 IF b_pick.order = '237002' THEN
 DO:
     message order_line with 2 columns.
     pause 10.
 END.
*** **/
      RUN find_item ( b_pick.abs_num ) NO-ERROR .

      IF AVAILABLE ( b_item ) THEN 
      DO:
         ASSIGN
            order_line.pack_req  = NOT b_item.l_self_ship
            order_line.item_desc = b_item.ch_item_desc 
            order_line.dim[1]    = b_item.d_height
            order_line.dim[2]    = b_item.d_width
            order_line.dim[3]    = b_item.d_length
            order_line.cube      = order_line.dim[1] *
                                   order_line.dim[2] *
                                   order_line.dim[3] * b_pick.qty 
            .

     /* 
     **  Assumption:  Self-ship items are always of quantity one.
     */                                        
            RUN sort3.p ( INPUT-OUTPUT order_line.dim[1] ,
                          INPUT-OUTPUT order_line.dim[2] ,
                          INPUT-OUTPUT order_line.dim[3] ) .

            /* See IF item will fit IN a box, ELSE split */
            FIND FIRST box_size 
               WHERE
                  box_size.cube >= order_line.dim[1] * order_line.dim[2] * 
                                   order_line.dim[3] AND
                  box_size.dim1 >= order_line.dim[1] AND
                  box_size.dim2 >= order_line.dim[2] AND
                  box_size.dim3 >= order_line.dim[3]  
               USE-INDEX i_large NO-ERROR.

            IF NOT AVAILABLE ( box_size ) THEN /* will NOT fit IN a box - self ship item */
               RUN no_box_for_item ( order_line.id , INPUT-OUTPUT i_new_id ) .
            ELSE /* One will fit check cube of all items */
               RUN fit_line_in_box ( order_line.id , INPUT-OUTPUT i_new_id ) .

      END .
      ELSE /* No item master for line */
         ASSIGN
            order_line.pack_req  = TRUE
            order_line.item_desc = "!!!UNKNOWN!!!" .

      IF order_line.qty = 0 THEN 
         ASSIGN 
            order_line.pack_req = TRUE .

/****** Debug Code ****
  message order_line with 2 columns.
  pause 10.
*** **/
   END . /* pickloop */
   
/******* Debug Code ***
   for each order_line: message order_line with 2 columns. end.
*** **/   
END PROCEDURE.


/******************************************************************/
PROCEDURE no_box_for_item:
/* 
** Take a line AND split it INto several self ship items FOR
** no box will hold the item
*/
   DEF INPUT        PARAMETER    i_id     AS INT NO-UNDO .
   DEF INPUT-OUTPUT PARAMETER    i_new_id AS INT NO-UNDO .
   DEF BUFFER       b_order_line FOR      order_line.

   FIND FIRST b_order_line 
      WHERE
         b_order_line.id = i_id
      NO-ERROR .

   IF NOT AVAILABLE ( b_order_line ) THEN
      RETURN .

   REPEAT WHILE b_order_line.qty > 1:
      CREATE order_line .

      ASSIGN
         order_line.pickid    = b_order_line.pickid
         order_line.id        = i_new_id
         order_line.line      = b_order_line.line
         order_line.seq       = b_order_line.seq
         order_line.item      = b_order_line.item
         order_line.bin       = b_order_line.bin
         order_line.qty       = 1
         order_line.pack_req  = FALSE
         order_line.item_desc = b_order_line.item_desc 
         order_line.dim[1]    = b_order_line.dim[1]
         order_line.dim[2]    = b_order_line.dim[2]
         order_line.dim[3]    = b_order_line.dim[3]
         order_line.cube      = order_line.dim[1] *
                                order_line.dim[2] *
                                order_line.dim[3] 
         i_new_id             = i_new_id + 1        .
     
      b_order_line.qty = b_order_line.qty - 1.
   END.
   
   ASSIGN
      b_order_line.pack_req = FALSE
      b_order_line.cube     = b_order_line.dim[1] *
                              b_order_line.dim[2] *
                              b_order_line.dim[3] .
END.


/*******************************************************************/
PROCEDURE fit_line_in_box:
/*
** Fit a line into a box.  if not one box, then split in as many boxes
** as necessary
*/
   DEF INPUT        PARAMETER i_id     AS INT NO-UNDO .
   DEF INPUT-OUTPUT PARAMETER i_new_id AS INT NO-UNDO .

   DEF VAR    d_count      AS  DEC        NO-UNDO .
   DEF BUFFER b_order_line FOR order_line .
   
   FIND FIRST b_order_line 
      WHERE
         b_order_line.id = i_id
      NO-ERROR .

   IF NOT AVAILABLE ( b_order_line ) THEN
      RETURN .

/***** Debug Code ***
   message b_order_line with 4 columns.
   pause 3.
**** ***/

   ASSIGN 
      d_count = b_order_line.qty.

   findboxloop:   /* Find quantity that will fit in largest box */
   REPEAT:     
      FIND FIRST box_size 
         WHERE
            box_size.cube >= b_order_line.dim[1] * b_order_line.dim[2] * 
                             b_order_line.dim[3] * d_count AND
            box_size.dim1 >= b_order_line.dim[1] AND
            box_size.dim2 >= b_order_line.dim[2] AND
            box_size.dim3 >= b_order_line.dim[3]  
         USE-INDEX i_large NO-ERROR.
      
      IF AVAILABLE box_size THEN
         LEAVE findboxloop.
      
      d_count = d_count - 1.
   END.
   
/****** Debug code***
   MESSAGE 'fit in box' b_order_line.item d_count view-as alert-box .
*** **/
   
   IF d_count = b_order_line.qty THEN
      RETURN.

/****** Debug code***
   MESSAGE 'fit in box needs adjustment' b_order_line.item b_order_line.qty d_count view-as alert-box .
*** **/

   /* 
   ** While more IN orderline than IN can fit IN a box, split 
   */
   REPEAT WHILE d_count <= b_order_line.qty:
      CREATE order_line .

      ASSIGN
         order_line.pickid    = b_order_line.pickid
         order_line.id        = i_new_id
         order_line.line      = b_order_line.line
         order_line.seq       = b_order_line.seq
         order_line.item      = b_order_line.item
         order_line.bin       = b_order_line.bin
         order_line.qty       = d_count
         order_line.pack_req  = TRUE
         order_line.item_desc = b_order_line.item_desc 
         order_line.dim[1]    = b_order_line.dim[1]
         order_line.dim[2]    = b_order_line.dim[2]
         order_line.dim[3]    = b_order_line.dim[3]
         order_line.cube      = order_line.dim[1] *
                                order_line.dim[2] *
                                order_line.dim[3] * 
                                order_line.qty
         i_new_id             = i_new_id + 1        
         b_order_line.qty     = b_order_line.qty - d_count   
         b_order_line.cube    = b_order_line.dim[1] *
                                b_order_line.dim[2] *
                                b_order_line.dim[3] * 
                                b_order_line.qty     .
/****** Debug Code ***
   message order_line with 3 columns.
   pause 3 .
**** ***/
   END. 
   
/***** Debug Code ***
   message b_order_line with 3 columns.
   pause 3.
**** ***/
END.


/*******************************************************************/
PROCEDURE get_order_info:
/* Loop through the order lines to assigns the charges to picks */
   DEF INPUT PARAMETER i_id  AS  INT  NO-UNDO .

   DEF VAR    d_unit_price   AS  DEC NO-UNDO .
   DEF VAR    d_total_qty    AS  DEC NO-UNDO .
   DEF VAR    d_total_price  AS  DEC NO-UNDO .
   DEF BUFFER b_line         FOR irms.orddtl .
   DEF QUERY  q_line         FOR b_line      .
          
   OPEN QUERY q_line
      FOR EACH b_line 
         WHERE
            b_line.id     = i_id   
      NO-LOCK .
   
   lineloop:
   REPEAT:
      GET NEXT q_line NO-LOCK.

      IF NOT AVAILABLE ( b_line ) THEN
         LEAVE lineloop.
      
      IF ( b_line.req_qty = 0 ) THEN /* Back Orders - will not affect order */
         NEXT lineloop.
         
      ASSIGN
         d_unit_price  = b_line.charges / b_line.orig_req_qty 
         d_total_qty   = b_line.req_qty                   
         d_total_price = b_line.charges                   . 
         
      FOR EACH order_line
         WHERE
            order_line.line = b_line.line 
         EXCLUSIVE-LOCK :

         IF ( d_total_qty = order_line.qty ) THEN
            ASSIGN
               order_line.price = d_total_price.
         ELSE
            ASSIGN
               order_line.price = order_line.qty * d_unit_price
               d_total_qty      = d_total_qty    - order_line.qty
               d_total_price    = d_total_price  - order_line.price
               d_unit_price     = d_total_price  / d_total_qty      .

      END.
   END.

/***
** FOR 1996, will NOT have any kit header lines.
**
**   order_line.kit   = CAN-FIND( FIRST b_kitline 
**                                 WHERE
**                                     b_kitline.id            =  b_line.id   AND
**                                     b_kitline.line          =  b_line.line AND
**                                     b_kitline.line_sequence GT 0               ) .
***/

END PROCEDURE. /*  get_order_line_info */


/**********************************************************/
PROCEDURE get_kit_dim:
/* Count all the items is the order associated to this kit,
** sum up the cube AND grab the largest dimension.
** NOT USED IN 1996 */

   DEF INPUT  PARAMETER i_id      AS INT NO-UNDO .
   DEF INPUT  PARAMETER i_line    AS INT NO-UNDO .
   DEF OUTPUT PARAMETER d_kitdim1 AS DEC NO-UNDO .
   DEF OUTPUT PARAMETER d_kitdim2 AS DEC NO-UNDO .
   DEF OUTPUT PARAMETER d_kitdim3 AS DEC NO-UNDO .
   DEF OUTPUT PARAMETER d_cube    AS DEC NO-UNDO .
   
   DEF VAR    d_kitdim  AS  DEC NO-UNDO EXTENT 3.
   DEF BUFFER b_kitline FOR irms.orddtl .
      
   FOR EACH b_kitline 
      WHERE
         b_kitline.id            =  i_id   AND
         b_kitline.line          =  i_line AND
         b_kitline.line_sequence GT 0        
      NO-LOCK :

      RUN find_item ( order_line.item ) .
                            
      IF AVAILABLE ( b_item ) THEN 
      DO: /* get new max dimension plus kit cube */
         ASSIGN
            d_kitdim[1] = b_item.d_height
            d_kitdim[2] = b_item.d_width
            d_kitdim[3] = b_item.d_length
            d_cube      = d_cube + ( d_kitdim[1] * d_kitdim[2] *
                                     d_kitdim[3] * b_kitline.req_qty ) .
                              
         RUN sort3.p ( INPUT-OUTPUT d_kitdim[1] ,
                       INPUT-OUTPUT d_kitdim[2] ,
                       INPUT-OUTPUT d_kitdim[3] ) .
                                       
         ASSIGN /* store Maximum size */
            d_kitdim1 = MAXIMUM ( d_kitdim1 , d_kitdim[1] )
            d_kitdim2 = MAXIMUM ( d_kitdim2 , d_kitdim[2] )
            d_kitdim3 = MAXIMUM ( d_kitdim3 , d_kitdim[3] ) .
      END .
   END .        /* For each item in kit    */
END PROCEDURE . /* kit dim count procedure */


/***********************************************************/
PROCEDURE clean_up_buffers:
   FOR EACH order_line:
      DELETE order_line .
   END .
   
   FOR EACH order_kit:
      DELETE order_kit .
   END .
   
   FOR EACH order_carton:
      DELETE order_carton .
   END .
   
   FOR EACH order_carton_size:
      DELETE order_carton_size .
   END .
END PROCEDURE .


/**********************************************************/
PROCEDURE carton_rest:
/*
** 8/20/96  Peter - How we cartonize
**   1) Find largest line and put in box
**   2) Find next largest 
**   3) put in same box or create new box.
**   4) Loop to 2 until completed.
*/
   DEF INPUT-OUTPUT PARAMETER i_page AS INT NO-UNDO.

   DEF VAR    i_first_page AS  INT        NO-UNDO .
   DEF VAR    l_ok         AS  LOG    NO-UNDO .
   DEF BUFFER b_line       FOR order_line         .   
   DEF BUFFER b_oc         FOR order_carton       .
   DEF QUERY  q_line       FOR b_line             .
   DEF QUERY  b_box_size   FOR box_size           .   

   ASSIGN
      i_first_page = i_page.
      
   OPEN QUERY q_line    /* Step one:  find largest line and carton */   
      FOR EACH b_line
      USE-INDEX index_cube
      BY b_line.zone . 

   carton_loop:
   REPEAT:
      GET NEXT q_line.
      
      IF NOT AVAILABLE ( b_line ) THEN
         LEAVE carton_loop.

      IF NOT b_line.pack_req THEN  
      DO:  /* Line w/o carton - self ships always size 1  */
         CREATE b_oc .

         ASSIGN
            b_oc.pickid    = b_line.pickid
            i_page         = i_page + 1         
            b_oc.box_num   = i_page
            b_oc.bin_num   = b_line.bin
            b_oc.qty       = b_line.qty
            b_oc.abs_num   = b_line.item
            b_oc.price     = b_line.price 
            b_oc.item_desc = b_line.item_desc .      
      END.
      ELSE                      
      DO: /* Put in carton */       

         FOR EACH order_carton_size:
             /*** See if line will fit in box ***/
            RUN comp_carton_with_order_line ( b_line.id                 , 
                                              order_carton_size.box_num ,
                                              OUTPUT l_ok               ) .

            IF l_ok THEN
            DO:
               RUN add_to_carton ( b_line.id, order_carton_size.box_num ) .
            
               NEXT carton_loop.
            END.
         END.

         RUN new_carton ( b_line.id, INPUT-OUTPUT i_page ) .
      END.             
   END.

   RUN shrink_boxes.
END PROCEDURE .


/********************************************************/
PROCEDURE comp_carton_with_order_line:
/***
**  - Compares a box and its contents with the line item to
**    see if they will fit in a box together.
***/  
   DEF INPUT  PARAMETER i_line_id AS INT     NO-UNDO .
   DEF INPUT  PARAMETER i_box_num AS INT     NO-UNDO .
   DEF OUTPUT PARAMETER l_ok      AS LOG NO-UNDO INITIAL FALSE.

   FIND FIRST order_line 
      WHERE
         order_line.id = i_line_id
      NO-LOCK.
   
   FIND FIRST tt_zone
      WHERE
         tt_zone.wh_zone = order_line.zone 
      NO-LOCK NO-ERROR .
      
   IF AVAILABLE ( tt_zone ) THEN  /* Find same contol pick zone */
      FIND FIRST order_carton_size
         WHERE
            order_carton_size.box_num    = i_box_num                 AND
            order_carton_size.ch_ctlzone = tt_zone.control_pick_area
         USE-INDEX index_ctl_zone 
         NO-LOCK NO-ERROR .
   ELSE
      FIND FIRST order_carton_size
         WHERE
            order_carton_size.box_num = i_box_num       AND
            order_carton_size.zone    = order_line.zone
         USE-INDEX index_box_num 
         NO-LOCK NO-ERROR .

   IF NOT AVAILABLE ( order_carton_size ) THEN
      RETURN .
      
   IF order_carton_size.d_num_line >= {&MAX_LINES_IN_FORM} THEN
      RETURN.

   FIND FIRST box_size 
      WHERE
         box_size.cube >= order_carton_size.d_used_cube + order_line.cube   AND
         box_size.dim1 >= MAXIMUM ( order_carton_size.d_max_dim1 , order_line.dim[1] ) AND
         box_size.dim2 >= MAXIMUM ( order_carton_size.d_max_dim2 , order_line.dim[2] ) AND
         box_size.dim3 >= MAXIMUM ( order_carton_size.d_max_dim3 , order_line.dim[3] )
      USE-INDEX i_small NO-ERROR.

   IF AVAILABLE ( box_size ) THEN
      l_ok = TRUE  .
   ELSE
      l_ok = FALSE .
END.


/********************************************************/
PROCEDURE new_carton:
/*** Create a new carton for a line ***/
   DEF INPUT        PARAMETER i_line_id AS INT NO-UNDO .
   DEF INPUT-OUTPUT PARAMETER i_box_num AS INT NO-UNDO .

   DEF BUFFER b_orline for order_line .

   FIND FIRST b_orline 
        WHERE 
           b_orline.id = i_line_id   
      NO-LOCK NO-ERROR 
      .

   FIND FIRST box_size 
      USE-INDEX i_large 
      NO-ERROR .
   
   IF NOT AVAILABLE ( box_size ) THEN
   DO:
      message "No box sizes are defined." .
      RETURN.
   END.

   FIND FIRST tt_zone
      WHERE
         tt_zone.wh_zone = b_orline.zone 
      NO-LOCK NO-ERROR .

   CREATE order_carton_size . /* create the largest box. */
   
   ASSIGN 
      i_box_num                     = i_box_num + 1 
      order_carton_size.box_num     = i_box_num
      order_carton_size.box_size    = box_size.box_id 
      order_carton_size.d_open_cube = box_size.cube 
      order_carton_size.zone        = b_orline.zone
      .

   IF AVAILABLE ( tt_zone ) THEN
      ASSIGN
         order_carton_size.ch_ctlzone = tt_zone.control_pick_area
         .

   RUN add_to_carton ( i_line_id, i_box_num ) .
END.


/****************************************************************/
PROCEDURE add_to_carton:
/* assign an order line to a carton */
   DEF INPUT PARAMETER i_line_id AS INT NO-UNDO . /* Line to add  */
   DEF INPUT PARAMETER i_box_num AS INT NO-UNDO . /* Box to add to */

   FIND FIRST order_line 
      WHERE
         order_line.id = i_line_id
      NO-LOCK.

/******** Debug Code ***
   message order_line with 2 columns.
   pause 3 .
*** **/

   CREATE order_carton.
   
   ASSIGN
      order_carton.box_num   = i_box_num
      order_carton.pickid    = order_line.pickid
      order_carton.bin_num   = order_line.bin
      order_carton.qty       = order_line.qty
      order_carton.or_qty    = order_line.or_qty
      order_carton.abs_num   = order_line.item
      order_carton.item_desc = order_line.item_desc
      order_carton.price     = order_line.price
      .

/****** Debug Code ***
  message order_carton with 3 columns .
  pause 6.
**** **/

   /*  Update AVAILABLE cube AND max dimension size */
   FIND FIRST order_carton_size
      WHERE
         order_carton_size.box_num = i_box_num
      USE-INDEX index_box_num .

   ASSIGN
      order_carton_size.d_max_dim1  = MAX ( order_carton_size.d_max_dim1, order_line.dim[1] ) 
      order_carton_size.d_max_dim2  = MAX ( order_carton_size.d_max_dim2, order_line.dim[2] ) 
      order_carton_size.d_max_dim3  = MAX ( order_carton_size.d_max_dim3, order_line.dim[3] ) 
      order_carton_size.d_open_cube = order_carton_size.d_open_cube - order_line.cube 
      order_carton_size.d_used_cube = order_carton_size.d_used_cube + order_line.cube
      order_carton_size.d_num_line  = order_carton_size.d_num_line  + 1
      order_carton_size.l_same_item = IF ( TRIM ( order_carton.abs_num ) EQ TRIM ( order_carton_size.ch_item ) ) AND 
                                           order_carton_size.l_same_item THEN
                                         YES
                                      ELSE
                                         NO
      order_carton_size.ch_item     = order_carton.abs_num
      .

/****** Debug Code ***
   message order_carton_size with 3 columns.
   pause 5.
**** **/
END.


/****************************************************************/      
PROCEDURE shrink_boxes:
/* 
** Since the order_carton_size table stores max dimensions for an
** item, and the used cube, we may compare the information against the
** box information stored in the box_size table
*/
   FOR EACH order_carton_size:
      
      FIND FIRST box_size 
         WHERE
            box_size.cube >= order_carton_size.d_used_cube AND
            box_size.dim1 >= order_carton_size.d_max_dim1  AND
            box_size.dim2 >= order_carton_size.d_max_dim2  AND
            box_size.dim3 >= order_carton_size.d_max_dim3  
         USE-INDEX i_small NO-ERROR.

      IF AVAILABLE ( box_size ) THEN
         ASSIGN
            order_carton_size.box_size    = box_size.box_id 
            order_carton_size.d_open_cube = box_size.cube - order_carton_size.d_used_cube 
            .
   END.
END.

/********************************************************/
PROCEDURE make_ups_barcode:
    DEF OUTPUT PARAMETER ups_barcode AS CHAR NO-UNDO .
    
    DEF VAR service_indicator AS CHAR NO-UNDO .
    DEF VAR reference_number  AS CHAR NO-UNDO .
    DEF VAR workstring        AS CHAR NO-UNDO .
    DEF VAR workint           AS INT  NO-UNDO .
    DEF VAR ch_shipper_id     AS CHAR NO-UNDO .

    IF b_header.carrier ne {&CARRIER_UPS} THEN
    DO:  
        ASSIGN ups_barcode = 
            TRIM  ( b_header.order )                      +
            TRIM  ( b_header.order_suffix)                + 
            FILL  ( '0', 12 - ( LENGTH ( TRIM ( b_header.order ) )+ 
                                LENGTH ( TRIM ( b_header.order_suffix ) ) ) ) +
            STRING( thispage,"99" ) .
            
         IF LENGTH ( ups_barcode ) NE 14 THEN
         DO:
            message 'USPS has invalid length ups barcode' ups_barcode .
              
            RETURN ERROR .
         END.
         RETURN .
    END .

    CASE b_header.service:
        WHEN {&SERVICE_NEXTDAY} THEN
            ASSIGN
                service_indicator = "01" .

        WHEN {&SERVICE_SECONDDAY} THEN
           ASSIGN
                service_indicator = "02" .

        WHEN {&SERVICE_GROUND} THEN
            ASSIGN /* Changed from 08 to 03 9/6/96 - pete */
                service_indicator = "03" .

        OTHERWISE
            ASSIGN
                service_indicator = "00" .
    END CASE .

    FIND FIRST tt_carrier 
       WHERE
          tt_carrier.carrier_id = 'UPS'
       NO-LOCK NO-ERROR .
       
    IF NOT AVAILABLE ( tt_carrier ) THEN
    DO:
       message 'using default carrier - UPS' .
       ASSIGN
          ch_shipper_id = {&UPS_SHIPPER_NUMBER} .
    END.
    ELSE
       ASSIGN
          ch_shipper_id = tt_carrier.shipper_id .
    
    checkloop:
    DO WHILE TRUE:
        CASE b_header.service:  /* 05dec95 12:17 glauber: diff. seq. for diff. UPS */
            WHEN {&SERVICE_NEXTDAY} THEN
               ASSIGN workint = next-value( cartonmst_red_id ) .

            WHEN {&SERVICE_SECONDDAY} THEN
               ASSIGN workint = next-value( cartonmst_blue_id ) .

            OTHERWISE
               ASSIGN workint = next-value( cartonmst_carton_id ) .
        END CASE .

        ASSIGN
            reference_number = STRING( workint, "9999999" )
            ups_barcode      = {&UPS_DATA_IDENTIFIER}       +
                               ch_shipper_id                +
                               service_indicator            +
                               reference_number             .

        RUN set_checkdigit( INPUT-OUTPUT ups_barcode ) .

        IF NOT CAN-FIND( first cartonmst WHERE
                            cartonmst.co_num = b_header.co_num AND
                            cartonmst.wh_num = b_header.wh_num AND
                            cartonmst.tracking_id = ups_barcode ) THEN
            LEAVE checkloop .
    END . /*checkloop*/

END PROCEDURE . /*make_ups_barcode*/

/**********************************************************/
PROCEDURE set_checkdigit :
    DEF INPUT-OUTPUT PARAMETER the_code AS CHAR NO-UNDO .

    DEF VAR checkdigit AS INT  NO-UNDO .
    DEF VAR pos        AS INT  NO-UNDO .
    DEF VAR maxpos     AS INT  NO-UNDO .
    DEF VAR thischar   AS CHAR NO-UNDO .
    DEF VAR charascii  AS INT  NO-UNDO .
    DEF VAR checkval   AS INT  NO-UNDO .

    ASSIGN
        pos        = 3 /*exclude the first 2 positions*/
        maxpos     = LENGTH(the_code)
        checkdigit = 0 .

    calcloop:
    DO WHILE pos LE maxpos:

        ASSIGN
            thischar = caps(substring(the_code, pos, 1))
            charascii = asc(thischar)
            checkval = ups_checktable[ charascii - 47 ]
        .

        /* even positions counted double */
        ASSIGN
            workint = (pos modulo 2)
            checkdigit = checkdigit + (IF workint = 0 THEN
                                          (2 * checkval)
                                       ELSE 
                                          (checkval))
            pos = pos + 1 .

    END . /*calcloop*/

    ASSIGN
        workint = 10 - (checkdigit modulo 10)
        checkdigit = (IF (workint = 10) THEN (0) ELSE (workint)).

    ASSIGN 
        the_code = the_code + STRING(checkdigit, "9") .
END PROCEDURE . /*set_checkdigit*/


/**********************************************************/
PROCEDURE outaddr:
    DEF INPUT PARAMETER which AS INT NO-UNDO .

    DEF VAR line0    AS DEC  NO-UNDO .
    DEF VAR line1    AS DEC  NO-UNDO .
    DEF VAR line2    AS DEC  NO-UNDO .
    DEF VAR line3    AS DEC  NO-UNDO .
    DEF VAR line4    AS DEC  NO-UNDO .
    DEF VAR line5    AS DEC  NO-UNDO .
    DEF VAR line6    AS DEC  NO-UNDO .
    DEF VAR line7    AS DEC  NO-UNDO .
    DEF VAR line8    AS DEC  NO-UNDO .
    DEF VAR line9    AS DEC  NO-UNDO .
    DEF VAR lskip    AS DEC  NO-UNDO .
    DEF VAR zipskip  AS DEC  NO-UNDO .
    DEF VAR thecol   AS DEC  NO-UNDO .
    DEF VAR thecol2  AS DEC  NO-UNDO .
    DEF VAR twoline  AS LOG  NO-UNDO .
    DEF VAR twoline2 AS LOG  NO-UNDO .
    DEF VAR twoline3 AS LOG  NO-UNDO .
    DEF VAR addr1    AS CHAR NO-UNDO .
    DEF VAR addr2    AS CHAR NO-UNDO .
    DEF VAR addr3    AS CHAR NO-UNDO .
    DEF VAR baddr1   AS CHAR NO-UNDO .
    DEF VAR baddr2   AS CHAR NO-UNDO .
    DEF VAR baddr3   AS CHAR NO-UNDO .
    DEF VAR bzip     AS CHAR NO-UNDO .
    DEF VAR thezip   AS CHAR NO-UNDO .

    CASE which:
    
       WHEN 1 THEN  /* ship to address */    
       DO:
          RUN addressfont IN printer_driver .

          ASSIGN
             thecol  = 11.3
             line1   = .35
             lskip   = 0.166
             zipskip = 0.208 - lskip
             line2   = line1 + lskip
             line3   = line2 + lskip
             line4   = line3 + lskip    .
       END .
       
       WHEN 2 THEN  /* ship to and ordered by address */ 
       DO:
          RUN addressfont IN printer_driver .

          ASSIGN
             thecol  = .5
             thecol2 = 7.7
             lskip   = 0.166
             line0   = 2.9
             line5   = .35
             line1   = line0 + lskip
             line2   = line1 + lskip
             line3   = line2 + lskip
             line4   = line3 + lskip 
             line6   = line5 + lskip
             line7   = line6 + lskip
             line8   = line7 + lskip.
       END .

       WHEN 3 THEN  /* wh name and address */
       DO:
                
          ASSIGN
             thecol   = 4.9 /*4.0*/
             lskip    = 0.166
             line0    = 0.3
             line1    = line0 + lskip
             line2    = line1 + lskip
             line3    = line2 + lskip
             line4    = line3 + lskip 
             
             thecol2  = .5 /*1.2*/
             line5    = 2.1 /*7.7*/
             line6    = line5 + lskip
             line7    = line6 + lskip
             line8    = line7 + lskip
             line9    = line8 + lskip
             twoline3 = (ch_wh3 ne "") .
 
        
          IF (twoline3) THEN
          DO:           
             RUN littlefont  IN printer_driver .
             RUN printat IN printer_driver ( (thecol2 - .125), 
                                             (line5 - .15 ), "From:").

             RUN addressfont IN printer_driver .

             RUN printat IN printer_driver ( thecol, line0, ch_wh1 ) .
             RUN printat IN printer_driver ( thecol2, line5, ch_wh1 ) .
  
       
             RUN printat IN printer_driver ( thecol, line1, ch_wh2 ) .
             RUN printat IN printer_driver ( thecol, line2, ch_wh3 ) .
             RUN printat IN printer_driver ( thecol, line3, ch_wh4 ) .
             RUN printat IN printer_driver ( thecol, line4, ch_wh5 ) .

             RUN printat IN printer_driver ( thecol2, line6, ch_wh2 ) .
             RUN printat IN printer_driver ( thecol2, line7, ch_wh3 ) .
             RUN printat IN printer_driver ( thecol2, line8, ch_wh4 ) .
             RUN printat IN printer_driver ( thecol2, line9, ch_wh5 ) .
          END .
          IF not twoline3 THEN
          DO:               
             RUN littlefont  IN printer_driver .
             RUN printat IN printer_driver ( (thecol2 - .15), 
                                             (line5 - .15 ), "From:").
             RUN addressfont IN printer_driver .
             RUN printat IN printer_driver ( thecol, line0, ch_wh1 ) .
             RUN printat IN printer_driver ( thecol2, line5, ch_wh1 ) .
             RUN printat IN printer_driver ( thecol, line1, ch_wh2 ) .
             RUN printat IN printer_driver ( thecol, line2, ch_wh4 ) .
             RUN printat IN printer_driver ( thecol, line3, ch_wh5 ) . 
         
             RUN printat IN printer_driver ( thecol2, line6, ch_wh2 ) .
             RUN printat IN printer_driver ( thecol2, line7, ch_wh4 ) .
             RUN printat IN printer_driver ( thecol2, line8, ch_wh5 ) .
                          
          END .
                  
       END .
       
    END CASE .

    ASSIGN 
        twoline  = (b_header.ship_addr2 ne "")
        twoline2 = (b_header.bill_addr2 ne "")
        addr1    = caps(b_header.ship_addr1)
        baddr1   = caps(b_header.bill_addr1)       
        thezip   = STRING( b_header.ship_zip, "X(11)" ) 
        bzip     = STRING( b_header.bill_zip, "X(11)" )
       .

    IF (twoline) THEN
        ASSIGN
            addr2 = caps(b_header.ship_addr2) 
            addr3 = b_header.ship_city  + "  " +
                    b_header.ship_state + "  " +
                    thezip
            addr3 = caps(addr3)                .

    ELSE 
        ASSIGN
            addr2 = b_header.ship_city  + "  " +
                    b_header.ship_state + "  " +
                    thezip
            addr2 = caps(addr2)
            addr3 = ""                         .
    
    IF (twoline2) THEN
       ASSIGN
          baddr2 = caps(b_header.bill_addr2) 
          baddr3 = b_header.bill_city + "  " +
                   b_header.bill_state + "  " +
                   bzip
          baddr3 = caps(baddr3)              .
    ELSE 
       ASSIGN
          baddr2 = b_header.bill_city + "  " +
                   b_header.bill_state + "  " +
                   bzip
          baddr2 = caps(baddr2)
          baddr3 = ""
          .
          
    IF  which ne 3 THEN
    DO:
       
       RUN printat IN printer_driver(thecol,line1,caps(b_header.ship_name)).
       RUN printat IN printer_driver ( thecol, line2, addr1 ) .
    
       IF (addr3 = "") AND (which = 1) THEN
       DO:
           ASSIGN line3 = line3 + zipskip .
           RUN zipfont IN printer_driver .
       END .
  
       RUN printat IN printer_driver ( thecol, line3, addr2 ) .

       IF (addr3 ne "") THEN
       DO:
           IF (which = 1) THEN
           DO:
               ASSIGN line4 = line4 + zipskip .
               RUN zipfont IN printer_driver .
           END .
           RUN printat IN printer_driver ( thecol, line4, addr3 ) .
        END .

       IF (which = 2) THEN
       DO:
        RUN littlefont IN printer_driver.
        RUN printat IN printer_driver( ( thecol - .125), line0, "To:").
        
        RUN addressfont IN printer_driver.
        RUN printat IN printer_driver(thecol2,line5,caps(b_header.bill_name)) .
        RUN printat IN printer_driver( thecol2, line6 , baddr1 ) .
        RUN printat IN printer_driver( thecol2, line7 , baddr2 ) .

        IF baddr3 ne "" THEN
               RUN printat IN printer_driver( thecol2, line8, baddr3 ) .
        run littlefont in printer_driver.
       END .
    END .
END PROCEDURE . /*outaddr*/


/**********************************************************/
PROCEDURE outupsbar:
    DEF VAR xpos      AS DEC  NO-UNDO INIT 0.8 /*0.75*/ .
    DEF VAR ypos      AS DEC  NO-UNDO INIT 6.4  . /* @mike .7 */
    DEF VAR barheight AS DEC  NO-UNDO INIT 1.15 .
    DEF VAR ch_line   AS CHAR NO-UNDO INIT ''   .
    DEF VAR ch_permit AS CHAR NO-UNDO INIT ''   .

    IF LENGTH(ups_barcode) = 0 THEN
        RETURN .

    IF b_header.carrier = {&CARRIER_UPS} THEN
    DO:
        IF b_header.service = {&SERVICE_GROUND} THEN
        DO:
         RUN servicefont IN printer_driver .
         RUN printat     IN printer_driver( 1.2, 6.1, " UPS GROUNDTRAC" ) .
         RUN trackidfont IN printer_driver .
         RUN printat     IN printer_driver( 1.4, 6.3, "IDENTIFICATION NUMBER" ).
        END .
        ELSE 
        DO:
           RUN trackidfont IN printer_driver .
           RUN printat IN printer_driver( 1.4, 6.3, "TRACKING NUMBER" ) .
        END .

        RUN littlefont IN printer_driver .
    END .

    RUN setbar_ups IN printer_driver .
    RUN barat IN printer_driver ( xpos, ypos, ups_barcode ) .

    IF b_header.carrier = {&CARRIER_UPS} THEN
    DO:
        ASSIGN
            workstring =
                substring(ups_barcode,  1, 2) + " " +
                substring(ups_barcode,  3, 3) + " " +
                substring(ups_barcode,  6, 3) + " " +
                substring(ups_barcode,  9, 2) + " " +
                substring(ups_barcode, 11, 4) + " " +
                substring(ups_barcode, 15, 4)
            ypos       = ypos + barheight           .
        RUN printat IN printer_driver ( 1.4, ypos, workstring ) .
    END .
    ELSE 
    DO:
        FIND FIRST tt_carrier 
           WHERE
              carrier_id = 'USPS'
           NO-LOCK .
        
        IF NOT AVAILABLE ( tt_carrier ) THEN
        DO:
           message 'Unable to find carrier- USPS' .
           ASSIGN 
              ch_line   = 'CHICAGO, IL' 
              ch_permit =  {&USPS_BULK_PERMIT} 
              .
        END.
        ELSE
            ASSIGN
                ch_line   = tt_carrier.city + ', ' + tt_carrier.state
                ch_permit = tt_carrier.shipper_id
                .

             
        ASSIGN
            ypos = ypos + barheight .
    
        RUN printat IN printer_driver ( 0.9, ypos, ups_barcode ) .

        /* bulk mail permit */
        RUN printat IN printer_driver ( 6.5, 0.15  , "PRIORITY MAIL"     ) .
        RUN printat IN printer_driver ( 6.5, 0.30, "U.S. POSTAGE PAID" ) .
        RUN printat IN printer_driver ( 6.7, 0.90, ch_line             ) .
        RUN printat IN printer_driver ( 6.7, 1.025,
                                        "Permit No. " + ch_permit ) .
    END .
END PROCEDURE . /*outupsbar*/                                                                    


/**********************************************************/
PROCEDURE outxofy:
    RUN littlefont IN printer_driver .

    IF totalpages = 1 THEN
        ASSIGN workstring = "Single package." .
    ELSE 
        ASSIGN
            workstring = STRING( thispage ) + " of " + STRING( totalpages ) +
                        " packages." .
        

    RUN printat IN printer_driver ( 0.5, 5.4, workstring ) . /*first from
                                                               left */
    RUN printat IN printer_driver ( 6.7, 7.9, workstring ) .

END PROCEDURE . /*outxofy*/


/**********************************************************/
PROCEDURE outcount:

    RUN littlefont IN printer_driver .
   
    IF print_only THEN
    DO:
       ASSIGN
           workstring = ch_x_of_y + ' (R)'. /* Maintain same reprint info... */

       RUN printat IN printer_driver ( 4.2, 7.5, workstring ) .
          
       RETURN .
    END.
    
    ASSIGN
        workstring = STRING( i_count ) + " of " + STRING( labels_printed ) . 

  /*  RUN printat IN printer_driver ( 4.2, 7.5, workstring ) . */
    
    FIND FIRST b_cms
      WHERE
         b_cms.co_num    = ch_co AND
         b_cms.wh_num    = ch_wh AND
         b_cms.carton_id = order_form.carton_id 
      EXCLUSIVE NO-ERROR .
   
   IF AVAILABLE ( b_cms ) THEN
      ASSIGN
         b_cms.x_of_y     = workstring 
         b_cms.sequence   = i_count 
         .

END PROCEDURE . /*outcount*/


/**********************************************************/
PROCEDURE outordbar:
    
    ASSIGN
        workstring = TRIM(b_header.order) + TRIM(b_header.order_suffix).

    RUN setbar_order_large IN printer_driver .
    RUN barat IN printer_driver ( 14.5, .9, workstring ) .
    
    ASSIGN 
        workstring = TRIM(b_cms.carton_id).
    RUN setbar_order in printer_driver .
    RUN barat IN printer_driver ( 5.05, 7.6, workstring) .
    RUN printat in printer_driver (5.05, 7.9, b_cms.carton_id).
END PROCEDURE . /*outordbar*/


/**********************************************************/
PROCEDURE outordinfo:
    /*customer number */
    ASSIGN 
       workstring = TRIM( b_header.cust_code ) .       
    RUN printat IN printer_driver( 5.1, 1.7, workstring ) .

    /*po_number*/
    ASSIGN 
       workstring = trim( b_header.customer_po ) .
      
    RUN printat IN printer_driver(9.25, 1.7, workstring ) .   

    ASSIGN 
       workstring = trim(b_header.class ) .
    RUN printat IN printer_driver(12.6, 1.7, workstring ) .

    ASSIGN
        workstring = TRIM(b_header.order) + " " + TRIM(b_header.order_suffix) .

    RUN printat IN printer_driver ( 14.5, .2, workstring ) .

    IF batch ne ? THEN
       ASSIGN workstring = workstring + " (batch " + STRING(batch) + ")" .
       
    RUN printat IN printer_driver( 8.0, 7.9, workstring ) .
    
    ASSIGN workstring = STRING( b_header.order_date, "99/99/9999" ) .

    RUN printat IN printer_driver ( 6.7, 1.7, workstring ) .

    ASSIGN workstring = STRING(b_header.ship_date, "99/99/9999" ).
    RUN printat IN printer_driver (15.9, .2, workstring ) .
    
    IF (b_header.partial = TRUE) or (totalpages gt 1) THEN 
    DO:
        RUN remarkfont IN printer_driver .
        RUN printat    IN printer_driver
           ( 6.2, 7.5, "** Other items shipped separately **" ) .
        RUN littlefont IN printer_driver .                                        
    END .

END PROCEDURE . /*outordinfo*/


/*
** This is the big bad one
*/
DEF VAR locpos       AS DEC NO-UNDO INITIAL 5.05 /*0.4*/ .
DEF VAR subtotalpos  AS DEC NO-UNDO INITIAL 10.7 .
DEF VAR taxpos       AS DEC NO-UNDO INITIAL 14.1 .
DEF VAR shippos      AS DEC NO-UNDO INITIAL 11.8 .
DEF VAR reqqtypos    AS DEC NO-UNDO INITIAL 13   /*15*/ .
DEF VAR secdescpos   AS DEC NO-UNDO INITIAL 5.8 .
DEF VAR qtypos       AS DEC NO-UNDO INITIAL 14.7 /*1.4*/ .
DEF VAR itempos      AS DEC NO-UNDO INITIAL 13.4 /*1.8*/ .
DEF VAR statuspos    AS DEC NO-UNDO INITIAL 3.0 /*3.3*/ .
DEF VAR descpos      AS DEC NO-UNDO INITIAL 9.0 /*3.7*/ .
DEF VAR pricepos     AS DEC NO-UNDO INITIAL 15.9 /*6.9*/ .

DEF VAR subtotalline AS DEC NO-UNDO INITIAL 7.9 /*4.167*/ .
DEF VAR statetaxline AS DEC NO-UNDO INITIAL 7.9 /*4.444*/ .
DEF VAR shiphandline AS DEC NO-UNDO INITIAL 7.9 /*4.722*/ .
DEF VAR totalline    AS DEC NO-UNDO INITIAL 7.9 /*5.0*/ .

DEF VAR linestart    AS DEC NO-UNDO INITIAL 3.15 .
DEF VAR lineadd      AS DEC NO-UNDO INITIAL 0.325 .
DEF VAR pos          AS INT NO-UNDO INITIAL 1 .
DEF VAR maxpos       AS INT NO-UNDO .


/**********************************************************/
PROCEDURE outlines:
    DEF VAR cluelist     AS  CHAR    NO-UNDO .
    DEF VAR cluerow      AS  DEC     NO-UNDO INITIAL 5 .
    DEF VAR cluecol      AS  DEC     NO-UNDO INITIAL 0.4 .
    DEF VAR clueinc      AS  DEC     NO-UNDO INITIAL 0.3 .
    DEF VAR sumcubes     AS  DEC     NO-UNDO .
    DEF VAR larg1        AS  DEC     NO-UNDO .
    DEF VAR larg2        AS  DEC     NO-UNDO .
    DEF VAR larg3        AS  DEC     NO-UNDO .
    DEF VAR goforlargest AS  LOG     NO-UNDO .
    
    DEF BUFFER b_ocs  FOR order_carton_size   .
    DEF BUFFER b_oc   FOR order_carton        .
    DEF BUFFER b_cdt  FOR irms.cartondtl      .

    DEF QUERY  q_data FOR b_oc                .

    ASSIGN
       pos          = 0
       cluelist     = ""
       the_sortkey  = ?
       sumcubes     = 0
       larg1        = 0
       larg2        = 0
       larg3        = 0
       goforlargest = FALSE 
       ch_zone      = '' 
       ch_box_size  = '' 
       ch_section   = '' 
       .

    OPEN QUERY q_data
       FOR EACH b_oc
          WHERE b_oc.box_num = thispage    
       EXCLUSIVE-LOCK .
      
   printloop:
   DO WHILE TRUE:
      GET NEXT q_data .

      IF NOT AVAILABLE( b_oc ) THEN
          LEAVE printloop .

/********** Debug Code          
   message b_oc.bin_num b_oc.qty b_oc.abs_num b_oc.price view-as alert-box.
*** **/

      IF NOT ( b_oc.kit ) THEN
      DO:     
          RUN outoneline( b_oc.pickid               ,
                          pos                       ,
                          0                         , /*not member of a kit*/
                          b_oc.bin_num              ,
                          b_oc.or_qty               ,
                          b_oc.qty                  ,
                          b_oc.abs_num              ,
                          b_oc.price                ,
                          INPUT-OUTPUT cluelist     ,
                          INPUT-OUTPUT larg1        ,
                          INPUT-OUTPUT larg2        ,
                          INPUT-OUTPUT larg3        ,
                          INPUT-OUTPUT sumcubes     ,
                          INPUT-OUTPUT goforlargest ) .

          ASSIGN pos = pos + 1 .
       END .

       DELETE b_oc .
    END . /*printloop*/

    /*** Format page for printing ***/
    RUN typewriterfont IN printer_driver .
    RUN fmtqty( INPUT subtotal, OUTPUT workstring ) .
    RUN printat IN printer_driver ( subtotalpos, subtotalline, workstring ) .

    IF ( thispage = totalpages ) THEN
    DO: /* print totals */
       RUN fmtqty( INPUT b_header.tax, OUTPUT workstring ) .
       RUN printat IN printer_driver ( taxpos, statetaxline, workstring ) .
       RUN fmtqty( INPUT b_header.customer_freight, OUTPUT workstring ) .
       RUN printat IN printer_driver ( shippos, shiphandline, workstring ) .

       ASSIGN
           grandtotal = subtotal + b_header.tax + b_header.customer_freight   .
            
       RUN fmtqty( INPUT grandtotal, OUTPUT workstring ) .
       RUN printat IN printer_driver ( pricepos, totalline, workstring ) .
    END .

    IF cluelist gt "" THEN
    DO:
       ASSIGN
          pos    = 1
          maxpos = NUM-ENTRIES( cluelist ) .

        RUN cluefont IN printer_driver .

        DO WHILE pos LE maxpos:
            RUN printat IN printer_driver ( cluecol, cluerow, entry(pos, cluelist) ) .
            ASSIGN
                pos     = pos + 1
                cluecol = cluecol + clueinc  .
        END .
    END .

    RUN littlefont IN printer_driver .

    FIND FIRST b_ocs 
       WHERE
          thispage = b_ocs.box_num 
       NO-ERROR.        
   
    IF AVAILABLE ( b_ocs ) THEN 
    DO:
       RUN setfont IN printer_driver( 26 ) . 

       RUN printat IN printer_driver( 5.1, 2.6, b_cms.box_id )       . 
       RUN littlefont IN printer_driver .
    END.  
    
END PROCEDURE . /*outlines*/


/* a duplicate :-( of outlines, used for reprints */        
/**********************************************************/
PROCEDURE outlines_reprint:
    DEF VAR cluelist AS CHAR NO-UNDO .
    DEF VAR cluerow AS DEC NO-UNDO INITIAL 5 .
    DEF VAR cluecol AS DEC NO-UNDO INITIAL 0.4 .
    DEF VAR clueinc AS DEC NO-UNDO INITIAL 0.3 .

    DEF VAR needbox AS LOG NO-UNDO .
    DEF VAR sumcubes AS DEC NO-UNDO .
    DEF VAR larg1 AS DEC NO-UNDO .
    DEF VAR larg2 AS DEC NO-UNDO .
    DEF VAR larg3 AS DEC NO-UNDO .
    DEF VAR goforlargest AS LOG NO-UNDO .
    DEF VAR ch_loc AS CHAR INIT '' .
    
    DEF BUFFER b_odt FOR irms.orddtl . 
    DEF BUFFER b_kit FOR irms.orddtl .

    DEF QUERY q_data FOR b_cdt .

    ASSIGN
        pos          = 0
        cluelist     = ""
        the_sortkey  = ?
        sumcubes     = 0
        larg1        = 0
        larg2        = 0
        larg3        = 0
        goforlargest = FALSE
        needbox      = TRUE  .

    OPEN QUERY q_data
       FOR EACH b_cdt NO-LOCK
          WHERE
             b_cdt.carton_num = b_cms.carton_num  
          .

    printloop:
    REPEAT:
        GET NEXT q_data .

        IF NOT AVAILABLE(b_cdt) THEN
           LEAVE printloop .

        RUN find_item ( b_cdt.abs_num ) NO-ERROR .
        
        FIND FIRST pick 
           WHERE
              pick.co_num      = b_header.co_num AND
              pick.wh_num      = b_header.wh_num AND
              pick.carton_id   = b_cms.carton_id AND
              pick.pick_status = 'O'             AND
              pick.abs_num     = b_cdt.abs_num    
           USE-INDEX co_wh_carton 
           NO-LOCK NO-ERROR   .
        
        ASSIGN
           ch_loc = pick.bin_num .
        
        FIND FIRST b_odt 
           WHERE
              b_odt.id            = b_header.id        AND
              b_odt.line          = pick.line          AND
              b_odt.line_sequence = pick.line_sequence 
           NO-LOCK NO-ERROR .
          
/**** No kits this year 
        IF CAN-FIND( first b_kit WHERE
                        b_kit.id   = b_odt.id      AND
                        b_kit.line = b_odt.line    AND
                        b_kit.line_sequence gt 0 ) THEN
        DO: /* (this is a kit) */
            RUN outoneline( ?                    , /* No pick id */
                            pos                  ,
                            1                    ,
                            ch_loc               , /* empty bin           */
                            b_odt.orig_req_qty   ,
                            b_odt.req_qty        , /* instead of quantity */
                            b_odt.abs_num        ,
                            b_odt.charges        ,
                            INPUT-OUTPUT cluelist,
                            INPUT-OUTPUT larg1   ,
                            INPUT-OUTPUT larg2   ,
                            INPUT-OUTPUT larg3   ,
                            INPUT-OUTPUT sumcubes,
                            INPUT-OUTPUT goforlargest ) .
            ASSIGN 
               pos = pos + 1 .

            FOR EACH b_kit NO-LOCK
               WHERE
                  b_kit.id            =  b_odt.id   AND
                  b_kit.line          =  b_odt.line AND
                  b_kit.line_sequence GT 0          :

                RUN outoneline( ?                       , /* No pick id */
                                pos                     ,
                                2                       , /*it is a member of a kit*/
                                b_kit.bin_num           ,
                                (IF b_odt.line_status = "S" THEN
                                    0
                                 ELSE 
                                    b_kit.req_qty)      ,
                                b_kit.abs_num           ,
                                ?                       , /*empty price*/
                                INPUT-OUTPUT cluelist   ,
                                INPUT-OUTPUT larg1      ,
                                INPUT-OUTPUT larg2      ,
                                INPUT-OUTPUT larg3      ,
                                INPUT-OUTPUT sumcubes   ,
                                INPUT-OUTPUT goforlargest ) .
                ASSIGN pos = pos + 1 .
            END .
        END .
        ELSE not a kit    */
        DO:
            IF (b_cdt.qty = 0) THEN /* a back-order */
                ASSIGN 
                   workdec = b_odt.charges .
            ELSE 
                ASSIGN 
                   workdec = (b_odt.charges * b_cdt.qty / b_odt.req_qty) .

            RUN outoneline( ?                    , /* No pick id */
                            pos                  ,
                            0                    , /*not member of a kit*/
                            ch_loc               ,
                            ?                    , /* orig req qty*/
                            b_cdt.qty            ,
                            b_cdt.abs_num        ,
                            workdec              ,
                            INPUT-OUTPUT cluelist,
                            INPUT-OUTPUT larg1   ,
                            INPUT-OUTPUT larg2   ,
                            INPUT-OUTPUT larg3   ,
                            INPUT-OUTPUT sumcubes,
                            INPUT-OUTPUT goforlargest ) .
            ASSIGN pos = pos + 1 .
        END .

        IF ((AVAILABLE(b_item)) AND (b_item.l_self_ship = FALSE)) THEN
           ASSIGN 
              needbox = FALSE .

    END . /* printloop */

    RUN typewriterfont IN printer_driver .
    
    RUN fmtqty( INPUT subtotal, OUTPUT workstring ) .
    RUN printat IN printer_driver ( pricepos, subtotalline, workstring ) .
                                                   
    IF ( thispage = totalpages AND NOT print_only ) THEN 
    DO: /* print totals */
        RUN fmtqty( INPUT b_header.tax, OUTPUT workstring ) .
        RUN printat IN printer_driver ( pricepos, statetaxline, workstring ) .
        RUN fmtqty( INPUT b_header.customer_freight, OUTPUT workstring ) .
        RUN printat IN printer_driver ( pricepos, shiphandline, workstring ) .

        ASSIGN
            grandtotal = subtotal + b_header.tax + b_header.customer_freight .
            
        RUN fmtqty( INPUT grandtotal, OUTPUT workstring ) .
        RUN printat IN printer_driver ( pricepos, totalline, workstring ) .
    END .

    IF cluelist gt "" THEN
    DO:
       ASSIGN
           pos = 1
           maxpos = NUM-ENTRIES(cluelist) .

       RUN cluefont IN printer_driver .

       DO WHILE pos LE maxpos:
          RUN printat IN printer_driver ( cluecol,
                                          cluerow,                    
                                          entry(pos, cluelist)  ) .                                         
          ASSIGN
             pos = pos + 1
             cluecol = cluecol + clueinc .
       END .
    END .

  /*  RUN littlefont IN printer_driver .
    RUN printat    IN printer_driver( 7.25, 7.65, "Box Size:" ) .  */
    RUN setfont    IN printer_driver( 36 ) .
    RUN printat    IN printer_driver( 5.1, 2.6, b_cms.box_id ) .
    RUN littlefont IN printer_driver .

END PROCEDURE . /*outlines*/  

/*
** outoneline: stuff that was part of outlines.
*/
/**********************************************************/

PROCEDURE outoneline:

    /*
    ** kitstat:
    **          0 == NOT kit
    **          1 == kit header
    **          2 == kit member
    */
    DEF INPUT        PARAMETER i_pickid     AS   INT            NO-UNDO .
    DEF INPUT        PARAMETER pos          AS   INT            NO-UNDO .
    DEF INPUT        PARAMETER kitstat      AS   INT            NO-UNDO .
    DEF INPUT        PARAMETER location     AS   CHAR           NO-UNDO .
    DEF INPUT        PARAMETER or_qty       LIKE orddtl.orig_req_qty NO-UNDO .
    DEF INPUT        PARAMETER qty          LIKE orddtl.req_qty NO-UNDO .
    DEF INPUT        PARAMETER item         AS   CHAR           NO-UNDO .
    DEF INPUT        PARAMETER price        AS   DEC            NO-UNDO .
    DEF INPUT-OUTPUT PARAMETER cluelist     AS   CHAR           NO-UNDO .
    DEF INPUT-OUTPUT PARAMETER larg1        AS   DEC            NO-UNDO .
    DEF INPUT-OUTPUT PARAMETER larg2        AS   DEC            NO-UNDO .
    DEF INPUT-OUTPUT PARAMETER larg3        AS   DEC            NO-UNDO .
    DEF INPUT-OUTPUT PARAMETER sumcubes     AS   DEC            NO-UNDO .
    DEF INPUT-OUTPUT PARAMETER goforlargest AS   LOG        NO-UNDO .

    DEF VAR    thislinepos AS  DEC  NO-UNDO      .
    DEF VAR    thestatus   AS  CHAR NO-UNDO      .
    DEF BUFFER b_item      FOR irms.item         .
    DEF BUFFER b_ocs       FOR order_carton_size .

    FIND FIRST b_ocs
       WHERE
          thispage = b_ocs.box_num
       NO-LOCK NO-ERROR.        
   
    FIND FIRST b_item
        WHERE
            b_item.co_num  = b_header.co_num AND
            b_item.wh_num  = b_header.wh_num AND
            b_item.abs_num = item
        NO-LOCK NO-ERROR .
        
    IF location BEGINS "SC" AND qty GT 0 THEN
    DO:
        ASSIGN 
           workstring = CAPS( SUBSTRING ( location, 1, 2) ) .

       IF ( the_sortkey =  ? ) AND 
          ( kitstat     NE 1 ) THEN
          ASSIGN          /* Sort by location then box size */
             the_sortkey = location 
             .
             
       IF LOOKUP (workstring, cluelist) = 0 THEN
          ASSIGN /* Add new item to the end of list */
             cluelist = cluelist + (IF cluelist gt "" THEN "," ELSE "") +
                        workstring .
    END.
    ELSE
    IF ((location begins "F") AND (qty gt 0)) THEN
    DO:                   /* flow rack */
       IF (the_sortkey = ?) AND (kitstat ne 1) THEN
          ASSIGN          /* Sort by location then box size */
             the_sortkey = location 
             .

        ASSIGN 
           workstring = CAPS( SUBSTRING ( location, 2, 1)) .

       IF LOOKUP (workstring, cluelist) = 0 THEN
          ASSIGN /* Add new item to the end of list */
             cluelist = cluelist + (IF cluelist gt "" THEN "," ELSE "") +
                        workstring .

    END .
    ELSE 
    IF ((location begins "G") AND (qty gt 0)) THEN
    DO:                   /* flow rack */
       IF (the_sortkey = ?) AND (kitstat ne 1) THEN
          ASSIGN          /* Sort by location then box size */
             the_sortkey = location 
             .

        ASSIGN 
           workstring = CAPS( SUBSTRING ( location, 2, 1)) .

       IF LOOKUP (workstring, cluelist) = 0 THEN
          ASSIGN /* Add new item to the end of list */
             cluelist = cluelist + (IF cluelist gt "" THEN "," ELSE "") +
                        workstring .

    END .
    ELSE
    DO:
       IF ((the_sortkey = ?) AND (qty gt 0) ) THEN
          ASSIGN 
             the_sortkey = location 
          .
    END.
    
    IF ch_zone = '' THEN
    DO:
       IF AVAILABLE ( b_ocs ) THEN
          ASSIGN
             ch_box_size = b_ocs.box_size
             ch_zone     = b_ocs.zone .
       ELSE
          ASSIGN
             ch_box_size = ''
             ch_zone     = '' .

       /* Dart Fix until they create the correct zones */
       ASSIGN
          ch_zone    = SUBSTRING ( location , 1 , 2 )  
          ch_section = SUBSTRING ( location , 3 , 2 ) .
    END.

    IF ((NOT print_only) AND (kitstat ne 2)) THEN
    DO: /* Create and populate carton detail */
        CREATE b_cdt .

        ASSIGN
            b_cdt.carton_num = b_cms.carton_num
            b_cdt.abs_num    = item
            b_cdt.qty        = qty
            .

        IF AVAILABLE ( b_item ) THEN
           ASSIGN
              b_cdt.uom      = b_item.uom
              b_cdt.case_qty = b_item.case_qty .
        ELSE 
           ASSIGN                /* use defaults */
              b_cdt.uom      = "EA"
              b_cdt.case_qty = 1               .

        IF ( i_pickid > 0 ) THEN /* Update cartonid in pick */
        DO: 
           RUN update_pick ( i_pickid ) .
        END.
        ELSE                     /* SHOULD NEVER HAPPEN */
        DO: 
           IF ( i_pickid > 1 ) THEN
              MESSAGE '> 1' view-as alert-box.
           ELSE
              MESSAGE '< 1' view-as alert-box.

           message 'NO PICK ID - ERROR' i_pickid .
        END.
    END .

    ASSIGN 
       thislinepos = linestart + ( lineadd * pos ) .

    RUN typewriterfont IN printer_driver .

    /*ASSIGN
       location = SUBSTRING ( location, 1, 2 ) + ' ' +
                  SUBSTRING ( location, 3, 2 ) + '-' +
                  SUBSTRING ( location, 5, 3 ) + '-' +
                  SUBSTRING ( location, 8, 1 ) .
    */
    /* IF qty > 0 THEN If qty is more than zero, print label */
       RUN printat IN printer_driver ( locpos, thislinepos, CAPS(location) ) . 

    IF (kitstat = 1) THEN
        ASSIGN workstring = "KIT" .
    ELSE 
    IF (qty = 0) THEN
        ASSIGN workstring = "B/O" .
    ELSE 
        ASSIGN workstring = STRING(qty, "ZZZ9") .

    RUN printat    IN printer_driver ( qtypos , thislinepos, workstring ) .
    RUN printat    IN printer_driver ( reqqtypos, thislinepos, or_qty) .
    RUN printat    IN printer_driver ( itempos, thislinepos, item ) .
    
    RUN littlefont IN printer_driver .

    IF (qty = 0) THEN
        ASSIGN thestatus = "B/O" .
    ELSE IF (b_header.gift = TRUE) THEN
        ASSIGN thestatus = "GFT" .

    RUN printat IN printer_driver ( statuspos, thislinepos, thestatus ) .

    IF AVAILABLE(b_item) THEN 
        ASSIGN workstring = TRIM( STRING( b_item.item_desc, "x(40)")) 
               workstring2 = TRIM( string(b_item.item_sec_desc, "x(30)")).
    ELSE 
        ASSIGN workstring = "!!! U N K N O W N !!!" 
               workstring2 = "!!! U N K N O W N !!!".
    
    
    IF (kitstat = 2) THEN 
       ASSIGN 
          workstring = "     " + workstring .
          
    IF (qty gt 1) THEN 
        ASSIGN
            workstring = " " + workstring
            workstring = FILL( "*", INTEGER(qty) ) + workstring
            workstring = STRING(workstring, "x(45)")            .

    RUN printat IN printer_driver ( descpos, thislinepos, workstring ) .
    RUN printat IN printer_driver ( secdescpos, thislinepos, workstring2) .

    IF (price ne ?) THEN
    DO:
        RUN fmtqty( INPUT price, OUTPUT workstring ) .
        RUN typewriterfont IN printer_driver .
        RUN printat        IN printer_driver (pricepos,thislinepos, workstring ) .
        RUN littlefont     IN printer_driver .
        
        ASSIGN 
           subtotal = subtotal + price .
    END .

END PROCEDURE . /*outoneline*/


/**********************************************************/
PROCEDURE fmtqty:
    DEF INPUT  PARAMETER numqty AS DEC NO-UNDO .
    DEF OUTPUT PARAMETER fmtqty AS CHAR NO-UNDO .

    DEF VAR strqty  AS CHAR NO-UNDO .
    DEF VAR thesize AS INT NO-UNDO .

    IF b_header.gift THEN 
    DO:
        ASSIGN fmtqty = "        gift" .
        RETURN .
    END .

    ASSIGN
        strqty  = STRING( numqty, "$>>>,>>9.99" )
        thesize = LENGTH(strqty)
        fmtqty  = FILL( " ", (12 - thesize) ) + strqty   .
        
END PROCEDURE . /*fmtqty*/



/**********************************************************/
PROCEDURE outdoornumber:
    IF b_header.carrier ne "UPS" THEN 
    DO:    
       RUN setfont IN printer_driver( 18 ) .
       RUN printat IN printer_driver( 6.7, 0.65, "US MAIL" ) .
    END .
    ELSE 
    DO:
        RUN setfont IN printer_driver( 54 ) .
        RUN printat IN printer_driver( 7.1, 0.6, door_number ) .
    END .

    RUN littlefont IN printer_driver .
END PROCEDURE . /*outdoornumber*/


/*
** 06dec95 12:57 glauber
** do_open_stream: open one stream
*/
/**********************************************************/
PROCEDURE do_open_stream:
    DEF INPUT PARAMETER thestream AS CHAR NO-UNDO .
    DEF INPUT PARAMETER i_count   AS INT  NO-UNDO .
    
    DEF VAR ch_version AS CHAR NO-UNDO INIT '' .
       
    IF i_count MODULO i_num_cartons_in_file = 0 AND
       i_count > 0                               THEN
    DO:
        ASSIGN
           ch_version = STRING ( i_count / i_num_cartons_in_file )
           .
    END.

    CASE opsys:     /* <<<<<<<<<< open the output streams >>>>>>>>>> */
        WHEN "msdos" THEN
            RUN msdos_stream_set ( thestream , ch_version ) .

        WHEN "unix" THEN
            RUN unix_stream_set  ( thestream , ch_version ) .

        OTHERWISE
        DO:
            MESSAGE "Unsupported: " + opsys .
            QUIT .
        END .
    END CASE .

END PROCEDURE . /*do_open_stream*/

/***
** MSDOS STREAM OPEN
***/
PROCEDURE msdos_stream_set:
    DEF INPUT PARAMETER thestream  AS CHAR NO-UNDO .
    DEF INPUT PARAMETER ch_version AS CHAR NO-UNDO .

    CASE thestream :
       WHEN "1d" THEN 
          ASSIGN
             ch_1d_file = STRING ( "c:/tmp/" + STRING( the_batch , '9999999' ) + "1d" + ch_version + "{&SUF_FILE}" , "X(20)" ) .
       WHEN "2d" THEN 
          ASSIGN
             ch_2d_file = STRING ( "c:/tmp/" + STRING( the_batch , '9999999' ) + "2d" + ch_version + "{&SUF_FILE}" , "X(20)" ) .
       WHEN "gd" THEN 
          ASSIGN
             ch_gd_file = STRING ( "c:/tmp/" + STRING( the_batch , '9999999' ) + "gd" + ch_version + "{&SUF_FILE}" , "X(20)" ) .
       OTHERWISE
          ASSIGN
             ch_ps_file = STRING ( "c:/tmp/" + STRING( the_batch , '9999999' ) + "ps" + ch_version + "{&SUF_FILE}" , "X(20)" ) .
    END CASE .                                           

    CASE thestream :
       WHEN "1d" THEN 
          OUTPUT STREAM idstream TO VALUE ( ch_1d_file ) NO-MAP NO-CONVERT .
       WHEN "2d" THEN 
          OUTPUT STREAM 2dstream TO VALUE ( ch_2d_file ) NO-MAP NO-CONVERT .
       WHEN "gd" THEN 
          OUTPUT STREAM gdstream TO VALUE ( ch_gd_file ) NO-MAP NO-CONVERT .
       OTHERWISE 
          OUTPUT STREAM psstream TO VALUE ( ch_ps_file ) NO-MAP NO-CONVERT .
    END CASE .                                           
    
END PROCEDURE . /* msdos_stream_set */


/**********************************************************/
PROCEDURE unix_stream_set:
    DEF INPUT PARAMETER thestream AS CHAR NO-UNDO .
    DEF INPUT PARAMETER ch_version AS CHAR NO-UNDO .    

    DEF VAR single_order_commands AS CHAR NO-UNDO .
    DEF VAR workstring AS CHAR NO-UNDO .
    DEF VAR ch_wave    AS CHAR NO-UNDO .
    
    ASSIGN
       ch_wave =  STRING( the_batch , '9999999' ) 
       .

    ASSIGN 
       single_order_commands = "" .

    CASE thestream:
       WHEN "1d" THEN 
       DO:
          ASSIGN 
             ch_1d_file = TRIM ( STRING ( file_prefix  + ch_wave    + 
                                  "{&RED_FILE}" + ch_version + 
                                  "{&SUF_FILE}", "X(20)" ) )  .
          OUTPUT STREAM idstream to value(ch_1d_file) no-map no-convert  .
       END  .
       WHEN "2d" THEN 
       DO:
          ASSIGN 
             ch_2d_file = TRIM ( STRING ( file_prefix + ch_wave + 
                                  "{&BLUE_FILE}" + ch_version + 
                                   "{&SUF_FILE}" , "X(20)" ) ) .
    
          OUTPUT STREAM 2dstream to value(ch_2d_file) no-map no-convert .
          
       END .
       WHEN "gd" THEN
       DO:
          ASSIGN   
             ch_gd_file = TRIM ( STRING ( file_prefix + ch_wave + 
                                 "{&BLACK_FILE}" + ch_version + 
                                 "{&SUF_FILE}" , "X(20)" ) ) .
          OUTPUT STREAM gdstream to value(ch_gd_file) no-map no-convert .
       END .
       
       OTHERWISE
       DO:
          ASSIGN 
             ch_ps_file = TRIM ( STRING ( file_prefix + ch_wave + 
                                  "{&PS_FILE}" + ch_version + 
                                  "{&SUF_FILE}", "X(20)" ) ) .
          OUTPUT STREAM psstream to value(ch_ps_file) no-map no-convert .
       END .
    END .  
    
    /* PUT UNFORMATTED 'opening stream...' workstring '~n' . */
 
END PROCEDURE . /* unix_stream_set */


/**********************************************************/
PROCEDURE update_pick:
    DEF INPUT  PARAMETER i_pickid AS  INT       NO-UNDO .
    DEF BUFFER           b_pick   FOR irms.pick .

    IF print_only THEN RETURN error .

    FIND FIRST b_pick 
       WHERE 
          b_pick.id           = i_pickid              AND
          b_pick.order        = b_header.order        AND
          b_pick.order_suffix = b_header.order_suffix AND
          b_pick.batch        = the_batch             AND
          b_pick.co_num       = b_header.co_num       AND
          b_pick.wh_num       = b_header.wh_num 
       USE-INDEX id EXCLUSIVE-LOCK NO-ERROR.

    IF NOT AVAILABLE ( b_pick ) THEN
    DO: 
       FIND FIRST b_pick
          WHERE
             b_pick.ID = i_pickid
          USE-INDEX id
          NO-LOCK.
       
       IF ( b_pick.batch NE the_batch ) THEN
          MESSAGE 'MESSAGE: WV1 -' b_pick.batch '::' the_batch VIEW-AS ALERT-BOX INFO.

       RETURN.
    END.

    ASSIGN 
       b_pick.carton_id = b_cms.carton_id .

    RELEASE b_pick NO-ERROR .     

END PROCEDURE . /*update_pick*/


/**********************************************************/
PROCEDURE create_carton:
   
      
    DEF BUFFER b_ocs    FOR order_carton_size .
    DEF BUFFER b_oc     FOR order_carton      . 
    DEF BUFFER b_cnew   FOR irms.cartonmst    .
    DEF VAR    hold_num AS  INT NO-UNDO       .
    DEF VAR    d_d1     AS  DEC NO-UNDO       INIT ? .
    DEF VAR    d_d2     AS  DEC NO-UNDO       INIT ? .
    DEF VAR    d_d3     AS  DEC NO-UNDO       INIT ? .

    IF print_only THEN 
       RETURN error .

    FIND FIRST b_ocs
       WHERE
          thispage = b_ocs.box_num
       NO-ERROR.        
    
    IF AVAILABLE ( b_ocs ) THEN 
    DO:
       FIND FIRST box_size 
          WHERE
             box_size.box_id = b_ocs.box_size
          NO-ERROR .
       
       IF AVAILABLE ( box_size ) THEN
          ASSIGN
             d_d1 = box_size.dim1 
             d_d2 = box_size.dim2 
             d_d3 = box_size.dim3 
             .
    END.
    ELSE   /* Self Ship, load item dimensions... */
    DO:   
       FIND FIRST b_oc
          WHERE b_oc.box_num = thispage 
          NO-ERROR .

       IF AVAILABLE ( b_oc ) THEN   
       DO:
          RUN find_item ( b_oc.abs_num ) .
       
          IF AVAILABLE ( b_item ) THEN
             ASSIGN
                d_d1 = b_item.d_height 
                d_d2 = b_item.d_width
                d_d3 = b_item.d_length 
                .
       END.
    END.
   
    define var ch_newctn_id  as char no-undo. 
    seq_loop:
    repeat:
        assign ch_newctn_id = "C" + 
                              string(next-value(cartonmst_carton_id), 
                              "999999999").
        find first cartonmst no-lock where
            cartonmst.co_num = b_header.co_num and
            cartonmst.wh_num = b_header.wh_num and
            cartonmst.carton_id = ch_newctn_id no-error.
        
            
        if avail cartonmst then next seq_loop.
        
        else leave seq_loop.    
    
    end.
    
    CREATE b_cnew .

    ASSIGN
        b_cnew.co_num       = b_header.co_num
        b_cnew.wh_num       = b_header.wh_num
        b_cnew.carton_id    = ch_newctn_id
        b_cnew.tracking_id  = ups_barcode 
        b_cnew.order        = b_header.order
        b_cnew.order_suffix = b_header.order_suffix
        b_cnew.carrier_id   = TRIM( b_header.carrier ) + "/" 
                            + TRIM( b_header.service )
        b_cnew.full         = TRUE
        b_cnew.box_id       = IF AVAILABLE ( b_ocs ) THEN
                                 b_ocs.box_size
                              ELSE
                                 ''
        b_cnew.batch        = the_batch
        b_cnew.height       = d_d1
        b_cnew.width        = d_d2 
        b_cnew.length       = d_d3
        hold_num            = b_cnew.carton_num    
        .
    
    RELEASE b_cnew .

    FIND FIRST b_cms  
       WHERE 
          b_cms.carton_num = hold_num
       NO-LOCK   .
END PROCEDURE . /*create_carton*/


/**********************************************************/
PROCEDURE close_transaction:
    DEF BUFFER close_transa FOR irms.AuditLog .

    IF print_only THEN RETURN error .

    FOR EACH close_transa EXCLUSIVE-LOCK WHERE
        close_transa.co_num     = b_header.co_num AND
        close_transa.wh_num     = b_header.wh_num AND
        close_transa.po_num     = b_header.order AND
        close_transa.po_suffix  = b_header.order_suffix AND
        close_transa.trans_type = "IP" AND
        close_transa.row_status = "O"
       :
   
        ASSIGN close_transa.row_status = "C" .
        RELEASE close_transa .
    END .
    
END PROCEDURE . /*close_transaction*/

/**********************************************************/
PROCEDURE outcomments:
   DEF VAR ch_workstring AS CHAR NO-UNDO .
   DEF VAR ch_rest       AS CHAR NO-UNDO .
   DEF VAR d_pos         AS DEC  NO-UNDO INIT 2.125 .
   DEF VAR i_length      AS INT  NO-UNDO .   
   
   ASSIGN
      ch_rest = ch_ord_comment
      .

   RUN remarkfont IN printer_driver .
   
   REPEAT WHILE LENGTH ( TRIM ( ch_rest ) ) > 0:

      ASSIGN
         i_length = 50 .
      
      IF LENGTH ( ch_rest ) > 50 THEN
      REPEAT WHILE SUBSTRING ( ch_rest, i_length, 1 ) NE ' ' :
         ASSIGN
            i_length = i_length - 1 
            .
      END.
      
      ASSIGN
         ch_workstring = LEFT-TRIM ( SUBSTRING ( ch_rest , 1 , i_length ) )
         ch_rest       = SUBSTRING ( ch_rest , i_length + 1 ) 
         d_pos         = d_pos + .15 
         .
       
      RUN printat IN printer_driver( 5.8, d_pos , ch_workstring ) .
   END.

   RUN littlefont IN printer_driver .
   
END.


/**********************************************************/
PROCEDURE mark_order_printed:

    DEF BUFFER mod_order FOR irms.ordhdr .
    DEF VAR order_address as recid NO-UNDO .

    IF print_only THEN RETURN error .

    ASSIGN order_address = recid(b_header) .

    FIND FIRST mod_order
        WHERE recid(mod_order) = order_address
        EXCLUSIVE-LOCK NO-ERROR NO-WAIT
    .

    IF AVAILABLE ( mod_order ) THEN
       ASSIGN 
          mod_order.printed = TRUE .
          
    RELEASE mod_order .

END PROCEDURE . /*mark_order_printed*/


/**********************************************************/
PROCEDURE outboxtype:
    DEF INPUT PARAMETER larg1 AS DEC NO-UNDO .
    DEF INPUT PARAMETER larg2 AS DEC NO-UNDO .
    DEF INPUT PARAMETER larg3 AS DEC NO-UNDO .
    DEF INPUT PARAMETER sumcubes AS DEC NO-UNDO .
    DEF INPUT PARAMETER goforlargest AS LOG NO-UNDO .

    RUN printat IN printer_driver( 7, 6.25, "Box Size:" ) .
    RUN setfont IN printer_driver( 36 ) .

    RUN printat IN printer_driver( 7, 6.8, "A") .  /* box_size.box_id ) . */
    RUN littlefont IN printer_driver .

    RETURN .

    IF goforlargest THEN
    DO:
        FIND last box_size .
        RUN printat    IN printer_driver( 7, 6.8, box_size.box_id ) .
        RUN littlefont IN printer_driver .
        RETURN .
    END .

    IF b_header.carrier = {&CARRIER_USPS} THEN
    DO:
        /*
        ** Try the postal service box first. Its dimensions
        ** are 3.25 x 12.5 x 15.5 (cube 630)
        */
        IF  (larg1 lt 3.25) AND (larg2 lt 12.5)   AND
            (larg3 lt 15.5) AND (sumcubes lt 630) THEN 
        DO:
            RUN printat    IN printer_driver( 7, 6.8, "PS" ) .
            RUN littlefont IN printer_driver .
            RETURN .
        END .
    END .

    FIND FIRST box_size
        WHERE
            box_size.dim1 gt larg1 AND
            box_size.dim2 gt larg2 AND
            box_size.dim3 gt larg3 AND
            box_size.cube gt sumcubes
        NO-ERROR .
    
    IF NOT AVAILABLE ( box_size ) THEN  
          /* couldn't FIND box, probably won't fit anyway... */
       FIND last box_size .

    RUN printat    IN printer_driver( 7, 6.8, box_size.box_id ) .
    RUN littlefont IN printer_driver .

    RETURN .

END PROCEDURE . /*outboxtype*/

/**********************************************************/
PROCEDURE goprint:
   DEF INPUT PARAMETER ch_form AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_file AS CHAR NO-UNDO .
&IF {&DEBUG} &THEN
   MESSAGE 'Printing...' ch_form ch_file .
&ELSE
   RUN prntloop.p ( ch_co, ch_wh, ch_form, ch_file ) . 
&ENDIF
END.


/**********************************************************/
PROCEDURE load_comments:     
/***
** Load comments 
***/
   DEF OUTPUT PARAMETER ch_comments AS CHAR NO-UNDO .
   
   DEF BUFFER b_com FOR irms.comment .
   DEF QUERY  q_com FOR b_com .

   OPEN QUERY q_com FOR EACH b_com
      WHERE 
         b_com.type          = 'O'           AND
         b_com.id            = b_header.id   AND
         b_com.line          = ?             AND
         b_com.line_sequence = ? 
      NO-LOCK
      BY b_com.comment_line .
   
   com_loop:
   REPEAT:
      GET NEXT q_com .
      
      IF NOT AVAILABLE b_com THEN
         LEAVE com_loop .

      ASSIGN 
         ch_comments = ch_comments + b_com.comment_text + " " .
   END .   
END .   


/**********************************************************/
PROCEDURE find_item :    
/*** 
** Uses temp-table tt_item and item table to quickly reference all the vital 
** item information 
***/
   DEF INPUT PARAMETER ch_find AS CHAR NO-UNDO .
   
   DEF BUFFER b_finditem FOR irms.item .
   
   FIND FIRST b_item
      WHERE
         b_item.ch_abs = ch_find 
      NO-ERROR .
   
   IF AVAILABLE ( b_item ) THEN
      RETURN .
   
   FIND FIRST b_finditem 
      WHERE
         b_finditem.co_num  = ch_co AND
         b_finditem.wh_num  = ch_wh AND
         b_finditem.abs_num = ch_find 
      NO-LOCK NO-ERROR .
   
   IF NOT AVAILABLE ( b_finditem ) THEN
   DO:  /*  return nothing... */
      RELEASE b_item .
      RETURN .
   END.
   
   CREATE b_item .
   
   ASSIGN
       b_item.ch_abs       = b_finditem.abs_num 
       b_item.d_width      = b_finditem.width
       b_item.d_height     = b_finditem.height 
       b_item.d_length     = b_finditem.length
       b_item.l_self_ship  = b_finditem.self_ship
       b_item.ch_item_desc = b_finditem.item_desc
       .
END.


/**********************************************************/
PROCEDURE printbuffer:
/***
**   read the commands for printer initialization 
**   output only once for each of the queues 
***/
   ASSIGN 
      apage = "" .
   
   RUN initialize IN printer_driver .

   RUN title_page IN printer_driver ( orders_printed ,
                                      labels_printed ,
                                  the_batch      ,
             timestamp + 'Starting At: ' + STRING ( i_count, ">,>>>,>>9" ) ) .
   ASSIGN
       init_cmd = apage
       apage    = "" .

   RUN formfeed IN printer_driver .

   ASSIGN
       eject_cmd = apage + apage
       apage     = "" 
       .
/***
** Reprint Selection...
***/
   
   IF i_sequence > 0 THEN
   DO:
&IF {&DEBUG} &THEN      
      OUTPUT TO /tmp/rp.out . 
&ELSE
      OUTPUT STREAM 2dstream TO /tmp/rp.out NO-MAP NO-CONVERT. 
&ENDIF      
      FOR EACH order_form 
         EXCLUSIVE 
         USE-INDEX ind_zone .

         ASSIGN /* Reset ***/
            apage = '' 
            .
         ASSIGN 
            i_count = i_count + 1 
            apage   = order_form.printimage .
          
         RUN outcount .
        
         RUN formfeed IN printer_driver .     /* form feed */   
   
         ASSIGN 
            order_form.printimage = apage .
&IF {&DEBUG} &THEN      
         PUT UNFORMATTED order_form.printimage .
&ELSE         
         PUT STREAM 2dstream UNFORMATTED order_form.printimage .         
&ENDIF         
         DELETE order_form .
      END.
&IF {&DEBUG} &THEN      
      OUTPUT CLOSE .
&ELSE
      OUTPUT STREAM 2dstream CLOSE .
&ENDIF
      RUN goprint ( 'Reprint', SESSION:TEMP-DIRECTORY + 'rp.out' ) . 

      ASSIGN
         apage = '' .
         
      RETURN .
   END.

/***
** Gold Room Separation...
***/
   DEF VAR l_goldrm AS LOG NO-UNDO INIT FALSE .

   FIND FIRST order_form
      WHERE
         order_form.zone = 'GR' 
      NO-ERROR .

   IF AVAILABLE order_form THEN
   DO:  
      l_goldrm = TRUE . 
      OUTPUT TO VALUE ( SESSION:TEMP-DIRECTORY + '/' + STRING( 
           the_batch , '9999999' ) + "gr" + "{&SUF_FILE}" ) .
      RUN title_page IN printer_driver ( orders_printed , labels_printed ,
                                         the_batch      ,
                    timestamp + " GDRM " + STRING ( i_count , ">>>,>>9" ) ) .
   END.
 
   FOR EACH order_form 
      WHERE
         order_form.zone = 'GR' 
      EXCLUSIVE 
      USE-INDEX ind_zone .

      ASSIGN 
         i_count = i_count + 1 
         apage   = order_form.printimage .
          
      RUN outcount .
   
      RUN formfeed IN printer_driver .     /* form feed */   
   
      ASSIGN 
         order_form.printimage = apage .
   
      PUT UNFORMATTED order_form.printimage .
   
      DELETE order_form .
   END.

   IF l_goldrm THEN
   DO:
      RUN title_page IN printer_driver ( orders_printed , labels_printed ,
                                         the_batch      ,
                    timestamp + " EOF " + STRING ( i_count - 1 , ">>>,>>9") ) .
      RUN formfeed IN printer_driver . 
      OUTPUT CLOSE .
      RUN goprint ( 'GoldRm', SESSION:TEMP-DIRECTORY + '/' + 
          STRING( the_batch , '9999999' ) + "gr" + "{&SUF_FILE}" ) .    
   END.
/***
** Output non-gold room orders...
***/
FOR EACH order_form EXCLUSIVE-LOCK 
   USE-INDEX i_loc : 

   ASSIGN 
      i_count = i_count + 1 
      apage   = order_form.printimage .

   RUN outcount .
   RUN formfeed IN printer_driver .     /* form feed */
    
   ASSIGN 
      order_form.printimage = apage  
      apage                 = ''   .
    
   CASE order_form.queue:
      WHEN "idstream" THEN
      DO:                                
         IF i_oneday_ord                               > 0 AND
            i_oneday_ord MODULO i_num_cartons_in_file = 0 THEN
         DO:
            ASSIGN
               any_1d = FALSE 
               apage  = ""    .
   
            RUN title_page IN printer_driver ( orders_printed , labels_printed , the_batch      ,
                                               timestamp + " EOF" + STRING ( i_count - 1 , ">>>,>>9" ) ) .

            PUT    STREAM idstream UNFORMATTED apage eject_cmd .
            OUTPUT STREAM idstream CLOSE .
            
            RUN goprint ( 'ND', ch_1d_file ) . 
            
            ASSIGN
               any_1d = FALSE 
               apage  = ""   .
         END.

         IF NOT any_1d THEN
         DO:
            RUN do_open_stream( "1d", i_oneday_ord ) .
      
            ASSIGN 
               any_1d = TRUE
               apage  = "" .
                
            RUN title_page IN printer_driver ( orders_printed , 
                labels_printed , the_batch      ,
                timestamp 
                + '(UPS/ND) Starting At: ' + STRING ( i_count , ">>>,>>9" ) ) .
                   
            PUT STREAM idstream UNFORMATTED apage .
         END .                     
            
         PUT STREAM idstream UNFORMATTED order_form.printimage      .

         ASSIGN
            i_oneday_ord = i_oneday_ord + 1 .
      END .
        
      WHEN "2dstream" THEN
      DO:
         IF i_twoday_ord                               > 0 AND
            i_twoday_ord MODULO i_num_cartons_in_file = 0 THEN
         DO:
            ASSIGN
               any_2d = FALSE 
               apage  = ""  .

            RUN title_page IN printer_driver ( orders_printed , labels_printed , the_batch,
                                               timestamp + " EOF" + STRING ( i_count - 1 , ">>>,>>9" ) ) .
            
            PUT    STREAM 2dstream UNFORMATTED apage eject_cmd .
            OUTPUT STREAM 2dstream CLOSE .
            
            RUN goprint ( '2d', ch_2d_file ) .  
            
            ASSIGN
               any_2d = FALSE 
               apage  = ""  .
         END.

         IF NOT any_2d THEN
         DO:
            RUN do_open_stream( "2d", i_twoday_ord ) .

            ASSIGN 
               any_2d = TRUE 
               apage  = "" .

            RUN title_page IN printer_driver ( orders_printed , 
                                               labels_printed , 
                                               the_batch      ,
                                        timestamp + '(UPS/2D) Starting At: ' + 
                                        STRING ( i_count, ">>>,>>9" ) ) .
                   
            PUT STREAM 2dstream UNFORMATTED apage .
         END .
            
         PUT STREAM 2dstream UNFORMATTED order_form.printimage       .

         ASSIGN
            i_twoday_ord = i_twoday_ord + 1 .
      END .
        
      WHEN "gdstream" THEN
      DO:
         IF i_ground_ord                               > 0 AND
            i_ground_ord MODULO i_num_cartons_in_file = 0 THEN
         DO:
            ASSIGN
               any_gd = FALSE 
               apage  = ""  .

            RUN title_page IN printer_driver ( orders_printed , 
                                               labels_printed , 
                                               the_batch      ,
                                               timestamp + " EOF" + 
                                         STRING ( i_count - 1 , ">>>,>>9" ) ) .
              
            PUT    STREAM gdstream UNFORMATTED apage eject_cmd .
            OUTPUT STREAM gdstream CLOSE .
          RUN goprint ( 'GD', ch_gd_file ) . 
            ASSIGN
               any_gd = FALSE 
               apage  = ""  .
         END.

         IF NOT any_gd THEN
         DO:
            RUN do_open_stream( "gd", i_ground_ord ) .

            ASSIGN 
               any_gd = TRUE
               apage  = "" .               

            RUN title_page IN printer_driver ( orders_printed , 
                                               labels_printed , 
                                               the_batch      ,
                                        timestamp + '(UPS/GD) Starting At: ' + 
                                        STRING ( i_count, ">>>,>>9" ) ) .

            PUT STREAM gdstream UNFORMATTED apage .


         END .
            
         PUT STREAM gdstream UNFORMATTED order_form.printimage .
            
         ASSIGN
            i_ground_ord = i_ground_ord + 1 .
      END .
        
      OTHERWISE
      DO:
         IF i_postal_ord                               > 0 AND
            i_postal_ord MODULO i_num_cartons_in_file = 0 THEN
         DO:
            ASSIGN
               any_ps = FALSE 
               apage  = ""  .

            RUN title_page IN printer_driver ( orders_printed , 
                                               labels_printed , 
                                               the_batch      ,
                                               timestamp + " EOF" + 
                                         STRING ( i_count - 1 , ">>>,>>9" ) ) .
              
            PUT    STREAM psstream UNFORMATTED apage eject_cmd .
            OUTPUT STREAM psstream CLOSE .

            RUN goprint ( 'GD', ch_ps_file ) . 

            ASSIGN
               any_ps = FALSE 
               apage  = "" .
         END.

         IF NOT any_ps THEN
         DO: 
            RUN do_open_stream( "ps" , i_postal_ord ) .

            ASSIGN 
               any_ps = TRUE 
               apage  = "" .
                   
            RUN title_page IN printer_driver ( orders_printed , 
                                               labels_printed , 
                                               the_batch      ,
                                       timestamp + '(USPS) Starting At: ' + 
                                       STRING ( i_count, ">>>,>>9" ) ) .

            PUT STREAM psstream UNFORMATTED apage .
         END .
            
         PUT STREAM psstream UNFORMATTED order_form.printimage      .

         ASSIGN
            i_postal_ord = i_postal_ord + 1 .
      END .
   END CASE .
END .

ASSIGN
    apage     = ""
    timestamp = STRING( today, "99/99/9999" ) + " " +
                STRING( time , "HH:MM AM"   )       .

RUN title_page IN printer_driver ( orders_printed             , 
                                   labels_printed             , 
                                   the_batch                  ,
                                   timestamp + " (END RUN)" ) .

IF any_1d THEN
DO:
   PUT STREAM idstream UNFORMATTED apage eject_cmd .
   OUTPUT STREAM idstream CLOSE     .
   RUN goprint ( 'ND', ch_1d_file ) . 
END.

IF any_2d THEN
DO:
   PUT STREAM 2dstream UNFORMATTED apage eject_cmd .
   OUTPUT STREAM 2dstream CLOSE     .
   RUN goprint ( '2D', ch_2d_file ) .  
END.

IF any_gd THEN
DO:
   PUT STREAM gdstream UNFORMATTED apage eject_cmd .
   OUTPUT STREAM gdstream CLOSE      .
   RUN goprint ( 'GD' , ch_gd_file ) . 
END.

IF any_ps THEN
DO:
   PUT STREAM psstream UNFORMATTED apage eject_cmd .
   OUTPUT STREAM psstream CLOSE     .
   RUN goprint ( 'GD', ch_ps_file ) . 
END.

/* PUT UNFORMATTED 'Closed Streams...~n'. */

END.

/***** Undo carton Program... *****
def var i_wave as int no-undo init 197.

for each ordhdr 
   where 
      co_num = ch_co  and
      wh_num = ch_wh  and
      batch  = i_wave
   exclusive
   use-index co_wh_batch :
      
   for each cartonmst  
      where
         cartonmst.co_num       = ordhdr.co_num       and
         cartonmst.wh_num       = ordhdr.wh_num       and
         cartonmst.order        = ordhdr.order        and
         cartonmst.order_suffix = ordhdr.order_suffix 
      use-index co_wh_order_suffix :
      
      for each cartondtl of cartonmst :
         delete cartondtl .
      end.
      
      delete cartonmst.
   end.
end.


for each pick 
   where 
      co_num = ch_co and
      wh_num = ch_wh and
      batch  = i_wave :
    assign
       pick.carton_id = ''.
end.
*/
