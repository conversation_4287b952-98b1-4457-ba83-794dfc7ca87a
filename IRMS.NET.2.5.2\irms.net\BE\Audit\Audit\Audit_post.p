/**=================================================================**
* Y:\irms.net.1.3.1\irms.net\BE\Audit\Audit\Audit_Post.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 02/26/14, 15:56 PM
**=================================================================**/


/* Business Entity Definintions */
{Audit/Audit/Audit_ds.i}
{Audit/Audit/Audit_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsAudit .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN DO:
        CREATE ds_Control.
        ASSIGN ds_Control.PropName = 'COMMAND'
               ds_Control.PropValue = 'POST'.
    END.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


