    DEFINE INPUT PARAMETER iprtguid AS DECIMAL NO-UNDO.
    <PERSON>FINE INPUT PARAMETER ipconum AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipwhnum AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipretitem AS CHARACTER NO-UNDO.
    <PERSON>FINE INPUT PARAMETER iporder AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER iporders AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipid AS INTEGER NO-UNDO.
    DEFINE INPUT PARAMETER ipretqty AS DECIMAL NO-UNDO.
    DEFINE INPUT PARAMETER ipactions AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER iprtnreason AS CHARACTER NO-UNDO.
    DEFINE INPUT PARAMETER ipguids AS CHARACTER NO-UNDO.
    DEFINE OUTPUT PARAMETER operrmessage AS CHARACTER NO-UNDO.

    DEFINE VAR dtl_line AS INTEGER NO-UNDO.
    DEFINE VAR ictr AS INTEGER NO-UNDO.
    DEFINE VAR rtn_reason AS CHARACTER NO-UNDO.


    IF ipactions = "Remove" THEN DO:
        IF iprtguid = ? THEN DO:
            operrmessage = "Please select a return line to remove.".
            RETURN.
        END.

        FIND IRMS.rtdet WHERE IRMS.rtdet.GUID = iprtguid NO-LOCK NO-ERROR.
        IF NOT AVAILABLE IRMS.rtdet THEN DO:
            operrmessage = "Return item record not available.".
            RETURN.
        END.

        DO:
            IF NUM-ENTRIES(ipguids) <> 0 THEN DO:
                DO ictr = 1 TO NUM-ENTRIES(ipguids):
                    FIND FIRST IRMS.serial_history WHERE IRMS.serial_history.GUID = DECIMAL(ENTRY(ictr,ipguids)) EXCLUSIVE-LOCK NO-ERROR.
                    IF AVAILABLE IRMS.serial_history THEN DO:
                        ASSIGN IRMS.serial_history.rma = " ".
                    END.
                END.
            END.
        
            FIND rtdet WHERE IRMS.rtdet.GUID = iprtguid EXCLUSIVE-LOCK NO-ERROR.
            IF NOT AVAILABLE IRMS.rtdet THEN DO:
                operrmessage = "Return item record not available.".
                RETURN.
            END.    
            IF LOCKED IRMS.rtdet THEN DO:
                operrmessage = "Return item record is locked. Please try again later.".
                RETURN.
            END.    

            IF IRMS.rtdet.exp_quantity = ipretqty THEN DO:
                /* if expected qty goes to zero delete rt detail */
                DELETE IRMS.rtdet.
            END.
            ELSE DO:
                ASSIGN IRMS.rtdet.exp_quantity = IRMS.rtdet.exp_quantity - ipretqty.
                RELEASE IRMS.rtdet NO-ERROR. 
            END.

            /* decrement the ret qty in order detail */

            FIND IRMS.ordhdr WHERE IRMS.ordhdr.co_num = ipconum AND IRMS.ordhdr.wh_num = ipwhnum AND IRMS.ordhdr.order = iporder AND 
            IRMS.ordhdr.order_suffix = iporders NO-LOCK NO-ERROR.
            IF NOT AVAILABLE IRMS.ordhdr THEN DO:
                operrmessage = "Order header not available.".
                RETURN. 
            END.        

            FIND IRMS.orddtl WHERE IRMS.orddtl.id = ipid AND IRMS.orddtl.abs_num = ipretitem EXCLUSIVE-LOCK NO-ERROR.
            IF NOT AVAILABLE IRMS.orddtl THEN DO:
                operrmessage = "Order detail not available.".
                RETURN.
            END.
            IF LOCKED IRMS.orddtl THEN DO:
                operrmessage = "Order detail record is locked. Please try again later.".
                RETURN.
            END.    

            ASSIGN IRMS.orddtl.ret_qty = IRMS.orddtl.ret_qty - ipretqty.
    
            RELEASE orddtl NO-ERROR.
        END.
    END.
    ELSE DO:
        IF iprtguid EQ ? THEN DO:
            operrmessage = "Please select a return line to change the return reason.".
            RETURN.
        END.
        
        FIND FIRST IRMS.rtdet WHERE IRMS.rtdet.GUID = iprtguid EXCLUSIVE-LOCK NO-WAIT NO-ERROR.
        IF NOT AVAILABLE IRMS.rtdet THEN DO:
            operrmessage = "Returns record is not available.".
            RETURN.    
        END. 
        IF LOCKED IRMS.rtdet THEN DO:
            operrmessage = "Returns record is in use. Please try back later.".
            RETURN.    
        END.    
        
        ASSIGN 
            IRMS.rtdet.comments = iprtnreason.

        RELEASE rtdet.
    END.




