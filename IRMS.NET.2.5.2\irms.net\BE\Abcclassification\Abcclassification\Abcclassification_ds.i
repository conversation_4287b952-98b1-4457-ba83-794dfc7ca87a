/**=================================================================**
* Y:\BE_Area\src\be\Abcclassification\Abcclassification\Abcclassification_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/10/06, 16:46 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE ABC NO-UNDO
    BEFORE-TABLE ABC_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   A_count_interval                 AS  INTEGER             
                                                 COLUMN-LABEL "A Interval"
                                                 LABEL "A Interval"
                                                 FORMAT ">>9"
                                                 INIT 45
                                                 HELP "Enter the A Interval"
    FIELD   a_count_loc                      AS  INTEGER             
                                                 COLUMN-LABEL "A Count Locations"
                                                 LABEL "A Count Locations"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the A Count Locations"
    FIELD   A_count_percent                  AS  DECIMAL             
                                                 COLUMN-LABEL "A Percent By Count"
                                                 LABEL "A Percent By Count"
                                                 FORMAT ">>%"
                                                 INIT 5
                                                 HELP "Enter the A Percent By Count"
    FIELD   A_dollar_percent                 AS  DECIMAL             
                                                 COLUMN-LABEL "A Percent By Dollar"
                                                 LABEL "A Percent By Dollar"
                                                 FORMAT ">>%"
                                                 INIT 70
                                                 HELP "Enter the A Percent By Dollar"
    FIELD   B_count_interval                 AS  INTEGER             
                                                 COLUMN-LABEL "B Interval"
                                                 LABEL "B Interval"
                                                 FORMAT ">>9"
                                                 INIT 90
                                                 HELP "Enter the B Interval"
    FIELD   b_count_loc                      AS  INTEGER             
                                                 COLUMN-LABEL "B Count Loc"
                                                 LABEL "B Count Loc"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the B Count Loc"
    FIELD   B_count_percent                  AS  DECIMAL             
                                                 COLUMN-LABEL "B Percent By Count"
                                                 LABEL "B Percent By Count"
                                                 FORMAT ">>9%"
                                                 INIT 10
                                                 HELP "Enter the B Percent By Count"
    FIELD   B_dollar_percent                 AS  DECIMAL             
                                                 COLUMN-LABEL "B Percent By Dollar"
                                                 LABEL "B Percent By Dollar"
                                                 FORMAT ">>9%"
                                                 INIT 20
                                                 HELP "Enter the B Percent By Dollar"
    FIELD   count_type                       AS  CHARACTER           
                                                 COLUMN-LABEL "Count Type"
                                                 LABEL "Count Type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Count Type"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Company"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "Custom Fields"
                                                 LABEL "Custom Fields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Custom Fields"
    FIELD   C_count_interval                 AS  INTEGER             
                                                 COLUMN-LABEL "C Interval"
                                                 LABEL "C Interval"
                                                 FORMAT ">>9"
                                                 INIT 180
                                                 HELP "Enter the C Interval"
    FIELD   c_count_loc                      AS  INTEGER             
                                                 COLUMN-LABEL "C Count Loc"
                                                 LABEL "C Count Loc"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the C Count Loc"
    FIELD   C_count_percent                  AS  DECIMAL             
                                                 COLUMN-LABEL "C Percent By Count"
                                                 LABEL "C Percent By Count"
                                                 FORMAT ">>9%"
                                                 INIT 65
                                                 HELP "Enter the C Percent By Count"
    FIELD   C_dollar_percent                 AS  DECIMAL             
                                                 COLUMN-LABEL "C Percent By Dollar"
                                                 LABEL "C Percent By Dollar"
                                                 FORMAT ">>9%"
                                                 INIT 10
                                                 HELP "Enter the C Percent By Dollar"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID"
    FIELD   O_count_interval                 AS  INTEGER             
                                                 COLUMN-LABEL "Other Interval"
                                                 LABEL "Other Interval"
                                                 FORMAT ">>9"
                                                 INIT 360
                                                 HELP "Enter the Other Interval"
    FIELD   o_count_loc                      AS  INTEGER             
                                                 COLUMN-LABEL "Other Count Loc"
                                                 LABEL "Other Count Loc"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the Other Count Loc"
    FIELD   O_count_percent                  AS  DECIMAL             
                                                 COLUMN-LABEL "Other Percent By Count"
                                                 LABEL "Other Percent By Count"
                                                 FORMAT ">>%"
                                                 INIT 20
                                                 HELP "Enter the Other Percent By Count"
    FIELD   O_dollar_percent                 AS  DECIMAL             
                                                 COLUMN-LABEL "Other Percent By Dollar"
                                                 LABEL "Other Percent By Dollar"
                                                 FORMAT ">>%"
                                                 INIT 0
                                                 HELP "Enter the Other Percent By Dollar"
    FIELD   recalc_interval                  AS  INTEGER             
                                                 COLUMN-LABEL "Recalculation Interval"
                                                 LABEL "Recalculation Interval"
                                                 FORMAT ">>9"
                                                 INIT 1
                                                 HELP "Enter the Recalculation Interval"
    FIELD   recalc_last                      AS  DATE                
                                                 COLUMN-LABEL "Last Recalculation"
                                                 LABEL "Last Recalculation"
                                                 FORMAT "99/99/9999"
                                                 HELP "Enter the Last Recalculation"
    FIELD   recalc_timeframe                 AS  CHARACTER           
                                                 COLUMN-LABEL "Recalculation Time-frame"
                                                 LABEL "Recalculation Time-frame"
                                                 FORMAT "X"
                                                 INIT "M"
                                                 HELP "Enter the Recalculation Time-frame"
    FIELD   recalc_type                      AS  CHARACTER           
                                                 COLUMN-LABEL "Recalc Type"
                                                 LABEL "Recalc Type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Recalc Type"
    FIELD   valforinclude                    AS  CHARACTER           
                                                 COLUMN-LABEL "valforinclude"
                                                 LABEL "valforinclude"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the valforinclude"
    FIELD   valforreport                     AS  CHARACTER           
                                                 COLUMN-LABEL "valforreport"
                                                 LABEL "valforreport"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the valforreport"
    FIELD   valforupdate                     AS  CHARACTER           
                                                 COLUMN-LABEL "valforupdate"
                                                 LABEL "valforupdate"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the valforupdate"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse"

                    .
TEMP-TABLE ABC:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsAbcclassification
    FOR ABC,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


