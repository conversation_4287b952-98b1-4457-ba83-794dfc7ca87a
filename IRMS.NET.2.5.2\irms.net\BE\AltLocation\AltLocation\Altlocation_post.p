/**=================================================================**
* Y:\irms.net.1.4.0\irms.net\BE\AltLocation\AltLocation\Altlocation_post.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 17/07/07, 10:39 PM
**=================================================================**/


/* Business Entity Definintions */
{AltLocation/AltLocation/Altlocation_ds.i}
{AltLocation/AltLocation/Altlocation_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsAltlocation .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN DO:
        CREATE ds_Control.
        ASSIGN ds_Control.PropName = 'COMMAND'
               ds_Control.PropValue = 'POST'.
    END.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


