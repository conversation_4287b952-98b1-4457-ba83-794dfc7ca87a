/*
** 13dec95 17:48 glauber
** lj128.p -- code128 barcodes
** Most of the processing is done by HLC -- see src subdirectory.
*/

/*
** Mod History:
**
** 5sep97 glauber: added maxicode HLC call
**
** 24Sep98 07:36 glauber
** The new version of Neomedia's maxicode library (libencoder.a)
** has a different syntax from the older version which we first used
** to implement Maxicode at Dart. The older version is in the files in
** the /pdb/8.02/rf directory. The files in the /pdb/9.01 directory
** will not be backwards compatible. If we implement IRMS 9 at
** Dart, we should upgrade their Maxicode library too.
*/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/lj128.p,v 1.1 2001-04-06 16:23:19-05 wayneb Exp $~n"
.

define new global shared variable bar_workspace as character no-undo .

/* no initialization */
return .

/*
** Takes the element width in mils (1/100")
** (roundoff errors will happen because the laser dot is 1/300")
*/
procedure 128_set_width:                       

    define input parameter wmil as integer no-undo .

    define variable smil as character no-undo .

    assign smil = string( wmil ) .

    call barwidth smil .

end procedure . /*128_set_width*/



/* takes the bar height in inches */
procedure 128_set_heigth:         

    define input parameter hin as decimal no-undo .

    define variable hmil as integer no-undo .
    define variable smil as character no-undo .
    
    assign
        hmil = round(( hin * 1000 ), 0)
        smil = string( hmil )
    .

    call barheight smil .

end procedure . /*128_set_heigth*/


procedure 128_bar:

    define input parameter xpos as decimal no-undo .
    define input parameter ypos as decimal no-undo .
    define input parameter source as character no-undo .      
    define input parameter type as character no-undo .
    define output parameter destination as character no-undo .


    define variable sx as character no-undo .
    define variable sy as character no-undo .

    if ((source eq ?) or (source eq ""))
    then do:
        assign destination = "" .
    end .
    else do:
        assign
            sx = string( round(xpos * 1000, 0) )
            sy = string( round(ypos * 1000, 0) )
        .

        call bar sx sy source type "bar_workspace" .

        assign
            destination = bar_workspace
            bar_workspace = ?
        .
    end .

end procedure . /*128_bar*/


procedure maxicode:
    define input parameter ch_postal_code as character no-undo .
    define input parameter i_country_code as integer no-undo .
    define input parameter i_service_code as integer no-undo .
    define input parameter ch_tracking_id as character no-undo .
    define input parameter ch_shipper_id as character no-undo .
    define input parameter i_package_number as integer no-undo .
    define input parameter i_package_count as integer no-undo .
    define input parameter i_package_weight as integer no-undo .
    define input parameter i_julian_day as integer no-undo .
    define input parameter ch_irms_carton_id as character no-undo .
    define input parameter ch_ship_to_city as character no-undo .
    define input parameter ch_ship_to_state as character no-undo .

    define output parameter ch_maxicode as character no-undo .

    call maxicode
        ch_postal_code
        i_country_code
        i_service_code
        ch_tracking_id
        ch_shipper_id
        i_package_number
        i_package_count
        i_julian_day
        ch_irms_carton_id
        ch_ship_to_city
        ch_ship_to_state
        "bar_workspace"
    .

    assign ch_maxicode = bar_workspace .
end procedure . /*maxicode*/



procedure destroy:
    delete procedure this-procedure .
end procedure . /*destroy*/
