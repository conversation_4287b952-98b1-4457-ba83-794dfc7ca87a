/** Print pick tickets program **/
/**
*** File Name: pic_iws.p 
*** 
*** Input    : company, warehouse, wave, order_id , reprint 
***
*** Output   : None
***
*** Author   : <PERSON> yang
*** 
*** Date     : September 07, 2000 10:03
***
**/
/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
    define variable SCCS_ID
        as character
        no-undo
        initial "@(#) $Header: /pdb/9.01/remote/RCS/pic_iws.p,v 1.1 2000/09/11 12:12:46 jyy8301 Exp $~n"
    .

    define input parameter ch_co             as character no-undo .
    define input parameter ch_wh             as character no-undo .
    define input parameter i_wave            as integer   no-undo .
    define input parameter ord_id            as integer   no-undo .
    define input parameter reprint           as character no-undo .
    
    define stream out_to_file . 
/**
*** the current printer should be setup in printer master
*** with print_form = "PK", type = true (for report) .
**/
    /**Globals parameters **/
    define new Global shared variable apage as character no-undo .

    define temp-table print_image no-undo
        field printimage as character 
    .

    define temp-table ord_head no-undo 
        field ord_co_num as character
        field ord_wh_num as character
        field ord_wave   as integer   
        field cust_id    as character 
        field cust_name  as character 
        field order      as character 
        field suffix     as character 
        field sales_ord  as character 
        field cust_po    as character 
        field ord_date   as date      
        field terms      as character 
        field ord_carrier as character 
        field ord_service as character 
        field wh_name     as character 
        field wh_addr     as character 
        field wh_addr1    as character 
        field wh_addr2    as character 
        field wh_city     as character 
        field wh_state    as character 
        field wh_zip      as character 
        field wh_country  as character
        field shp_name    as character 
        field shp_addr    as character 
        field shp_addr1   as character 
        field shp_addr2   as character 
        field shp_city    as character 
        field shp_state   as character 
        field shp_zip     as character 
        field shp_country as character
        field line_comm   as integer             
        field comm        as character extent 30 
    .
        
    define temp-table pick_dtl no-undo
        field line_num  as integer 
        field ch_zone   as character 
        field ch_loc    as character 
        field gift      as character format "X" 
        field Item      as character
        field chdesc    as character 
        field sn        as character 
        field pick_qty  as decimal 
        field pick_unit as character 
    .

    define variable printer_driver as handle .
    define variable errproc as handle .

    /** buffer defination **/
    define buffer bf_ord for irms.ordhdr.

    /** Loacl variable **/
    define variable total_page as integer initial 1 no-undo .  /* page number */

    /**Load external programe **/
    run lprintor.p PERSISTENT SET printer_driver.

    /** Main block **/
    /* check for the order */
        
    if ( ord_id NE ? ) AND (ord_id NE 0)
    then do:
        find first bf_ord no-lock 
            where 
                bf_ord.id = ord_id 
            no-error 
        .
        if not available (bf_ord)
        then do:
            message "The order is not exist. " .     
            return error .
        end .
        run cleanup .
        assign 
            ch_co = bf_ord.co_num 
            ch_wh = bf_ord.wh_num .

        /*  generate the temp table */ 
        run gen_tmp_table no-error  .

        if error-status:error
        then do:
            return error .
        end .

        /* Reset the printer */
        run reset .
        
        /* beginning print */
        run print_iws no-error   .
            
    end .
    else do:
        if ( (ch_co EQ "") OR (ch_co EQ ?) OR (ch_wh EQ "") OR (ch_wh EQ ?) OR
             (i_wave LE 0) )            /* check the input */
        then do :
            message "The input error. Please check input" .
            return error .
        end .
        for each bf_ord no-lock 
            where 
                bf_ord.co_num = ch_co and 
                bf_ord.wh_num = ch_wh and 
                bf_ord.batch  = i_wave
        :
            run cleanup .
            assign 
                ord_id = bf_ord.id 
                ch_co  = bf_ord.co_num 
                ch_wh  = bf_ord.wh_num 
            .
            run gen_tmp_table no-error .

            if error-status:error
            then do:
                return error .
            end .

            /* Reset the printer */
            run reset .

            run print_iws no-error . 
        end .
    end .
    
    return .  /* end Job */

/******* PROCEDURE ********/

procedure reset:   /* reset the printer */
    
    assign 
        apage = ""
        apage = apage + "~EE~E&10L~E&148D~E&k0S~E&12A~E&l1E" .

end procedure .
procedure cleanup :
    for each ord_head  
    :
        delete ord_head no-error .
    end .
    for each pick_dtl 
    :
        delete pick_dtl no-error .
    end .
    for each print_image
    :
        delete print_image no-error .
    end .

end procedure .

procedure gen_tmp_table:  /* generat the temp table for print */ 
    
    define buffer bf_ord for irms.ordhdr.
    define buffer bf_dtl for irms.orddtl.
    define buffer bf_wh  for irms.whmst . 
    define buffer bf_pick   for irms.pick .
    define buffer bf_item   for irms.item .
    define buffer bf_comm   for irms.comment .

    define variable comm_line as integer no-undo .
    find first bf_ord no-lock 
        where 
            bf_ord.id = ord_id 
        no-error       /*** have to exist here ***/
    .
    find first bf_wh no-lock 
        where 
            bf_wh.co_num = ch_co and 
            bf_wh.wh_num = ch_wh 
        no-error
    .
    if not available (bf_wh)
    then do:
        message "Company and warehouse information is wrong ." .
        return error .
    end .

    create ord_head no-error.
    assign
        ord_head.ord_co_num  = ch_co  
        ord_head.ord_wh_num  = ch_wh      
        ord_head.ord_wave    = bf_ord.batch 
        ord_head.cust_id     = bf_ord.ship_cust_code  
        ord_head.cust_name   = bf_ord.ship_name  
        ord_head.order       = bf_ord.order 
        ord_head.suffix      = bf_ord.order_suffix 
/*
        ord_head.sales_ord   = bf_ord.custom_data[1]
        ord_head.terms       = bf_ord.custom_data[3] 
*/        
        ord_head.cust_po     = bf_ord.customer_po  
        ord_head.ord_date    = bf_ord.order_date
        ord_head.ord_carrier = bf_ord.carrier 
        ord_head.ord_service = bf_ord.service 
        ord_head.wh_name     = bf_wh.wh_desc
        ord_head.wh_addr     = bf_wh.addr1
        ord_head.wh_addr1    = bf_wh.addr2
        ord_head.wh_city     = bf_wh.city 
        ord_head.wh_state    = bf_wh.state
        ord_head.wh_zip      = bf_wh.zip 
        ord_head.wh_country  = bf_wh.country
        ord_head.shp_name    = bf_ord.ship_name
        ord_head.shp_addr    = bf_ord.ship_addr1
        ord_head.shp_addr1   = bf_ord.ship_addr2
        ord_head.shp_addr2   = bf_ord.ship_addr_ext1
        ord_head.shp_city    = bf_ord.ship_city 
        ord_head.shp_state   = bf_ord.ship_state
        ord_head.shp_zip     = bf_ord.ship_zip 
        ord_head.shp_country = bf_ord.ship_country
    .
    /*** get comments for the order ***/
    assign comm_line = 0 .
    for each bf_comm no-lock
        where
            bf_comm.type = "O" and
            bf_comm.id = bf_ord.id
    :
        if comm_line GT 29
        then do:
            message "Too many comments." .
            leave .
        end .
        assign
            comm_line = comm_line + 1 
            ord_head.comm[comm_line] = bf_comm.comment_text 
        .
    end .
    assign ord_head.line_comm = comm_line .
    release ord_head no-error.

    for each bf_pick no-lock
        where 
            bf_pick.co_num        = ch_co and 
            bf_pick.wh_num        = ch_wh and 
            bf_pick.order         = bf_ord.order and 
            bf_pick.order_suffix  = bf_ord.order_suffix
    :
        create pick_dtl no-error .
        assign 
            pick_dtl.line_num = bf_pick.line 
            pick_dtl.ch_loc   = bf_pick.bin_num 
            pick_dtl.ch_zone  = bf_pick.wh_zone 
            pick_dtl.item     = bf_pick.abs_num 
            pick_dtl.pick_qty = bf_pick.qty 
        .
        find first bf_item no-lock 
            where 
                bf_item.co_num = ch_co and 
                bf_item.wh_num = ch_wh and 
                bf_item.abs_num = bf_pick.abs_num 
            no-error
        .
        if available (bf_item) 
        then do:
            assign 
                pick_dtl.chdesc = bf_item.item_desc 
                pick_dtl.sn     = (if bf_item.serial_flag EQ YES then "Y" 
                               else "N" )
            .
            if bf_pick.qty EQ bf_item.pallet_qty and bf_item.pallet_qty GT 1  
            then assign pick_dtl.pick_unit = "PALLET" .
            else if bf_pick.qty EQ bf_item.case_qty and bf_item.case_qty GT 1 
            then assign pick_dtl.pick_unit = "CASE" .
            else if bf_pick.qty EQ bf_item.box_qty and bf_item.box_qty GT 1 
            then assign pick_dtl.pick_unit = "BOX" .
            else assign pick_dtl.pick_unit = "EACH" .

        end .
        else do:
            assign 
                pick_dtl.chdesc = "Unknown Item"  
                pick_dtl.sn     = "N" 
                pick_dtl.pick_unit = "E" 
            .
        end .
        find first bf_dtl no-lock 
            where 
                bf_dtl.id = ord_id and 
                bf_dtl.line = bf_pick.line and 
                bf_dtl.line_sequence = bf_pick.line_sequence 
            no-error
        .
        if available (bf_dtl) AND bf_dtl.gift_wrap EQ true
        then assign pick_dtl.gift = "X"  .
        else assign pick_dtl.gift = "" .

        release pick_dtl no-error .
    end .
end procedure .

procedure print_iws:    /* print the pomeroy */

    define buffer bf_dtl for pick_dtl .
        
    define variable xpos as decimal no-undo .
    define variable ypos as decimal no-undo .
    define variable row_num as decimal no-undo .
    define variable page_num as integer no-undo .

    define variable ch_file as character no-undo .
    define variable prn_command as character no-undo .
    define variable ch_printer as character no-undo .

    /*** define output temp file ****/
    assign 
        page_num = 1 
        ch_file = session:temp-directory + 
                    string (ord_id , "9999999999") + ".out" 
    .   

    /* loop for print */
    run prn_head (input page_num, output row_num ) .

    create print_image no-error.
    assign 
        print_image.printimage = apage 
        apage = "" 
    .
    release print_image no-error.

    assign ypos = row_num .

    for each bf_dtl no-lock
        by bf_dtl.gift
        by bf_dtl.ch_zone 
        by bf_dtl.ch_loc 
        by bf_dtl.item 
    :
        if ypos > 10.0 /*** add aother page ****/ 
        then do:
            create print_image no-error.
            assign 
                print_image.printimage = apage + "~f"  
                apage = "" 
            .
            release print_image no-error.
            assign page_num = page_num + 1 .
            run prn_head (input page_num, output row_num ) .
            assign ypos = row_num .
        end .
        run prn_body (input ypos, input recid(bf_dtl)) .
        create print_image no-error.
        assign 
            print_image.printimage = apage 
            apage = "" 
        .
        release print_image no-error.
        assign ypos = ypos + 0.20 .
    end .

    output stream out_to_file to value(ch_file) .

    for each print_image 
    :
        put stream out_to_file unformatted 
            print_image.printimage + "~n" .
    end .
        put stream out_to_file unformatted 
            "~EE" 
        .
    output stream out_to_file close .
    
    run go_print (input ch_file ) no-error .
    
end procedure .
procedure go_print :

    define input parameter my_file as character no-undo .

    define buffer bf_prn for irms.printmst .
    define variable ch_printer as character no-undo .
    define variable prn_command as character no-undo .

    if search (my_file) EQ ?
    then do:
        message "The file not create yet . " .
        return error .
    end .
    /*** find a printer ***/
    for each bf_prn exclusive-lock 
        where 
            bf_prn.co_num     = ch_co and 
            bf_prn.wh_num     = ch_wh and 
            bf_prn.type       = true  and 
            bf_prn.print_form = "PK" 
        by bf_prn.last_time_used
    :            
        if available (bf_prn)
        then do:
            assign bf_prn.last_time_used = 
                substring(string(today, "99/99/9999"), 7, 4) +
                substring(string(today, "99/99/9999"), 1, 2) +
                substring(string(today, "99/99/9999"), 4, 2) +
                substring(string(time , "HH:MM"), 1, 2) + 
                substring(string(time , "HH:MM"), 4, 2)
            .
            assign ch_printer = bf_prn.printer .
            release bf_prn no-error .                   
            leave .
        end .
    end .
    if length(ch_printer) EQ 0 
    then do:
        message "No printer available for pick ticket." .
        return error .
    end .
    
    assign prn_command = "lp -c -s -d " + ch_printer + " " + my_file .
        
    if opsys EQ "UNIX"          /**** for different system ***/
    then do:
        OS-COMMAND silent VALUE ( prn_command ) .     
    end .
    else do:
        define variable finaldir as character no-undo .

        assign finaldir = 
            trim(ch_printer) .

        os-copy value(my_file) value(finaldir) .   
    end .
    return .
end .
procedure prn_head:      /* print the header */
    
    define input  parameter my_page as integer no-undo .
    define output parameter row_num as decimal no-undo .

    define buffer bf_head for ord_head  .

    define variable x_at as decimal initial 0.0 no-undo .
    define variable y_at as decimal initial 0.0 no-undo .
    define variable i    as integer no-undo .

    find first bf_head no-lock .

    if not available (bf_head)
    then do:
        message "No order for print.".
        return error .
    end .
   /* the title */ 
    assign y_at = 0.5 .

    if reprint EQ "R"
    then do:
        run universfont in printer_driver(12) .
        assign apage = apage + "~E(s3B" .   
        run printat in printer_driver (6.00, 0.60, "* REPRINT *") .
    end .

    run universfont in printer_driver(10) .

    run printat in printer_driver (3.00, y_at, "IRMS --- PICK TICKET" ) .
    assign y_at = y_at + 0.15 .
    run printat in printer_driver (3.00, y_at, "==========") .

    run universfont in printer_driver(8) .
    
    assign y_at = 1.0 . 
    /* second column */      

    run printat in printer_driver (3.30, y_at, "WH.:") . 
    run printat in printer_driver (3.60, y_at, string(trim(bf_head.wh_name))).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (3.60, y_at, string(trim(bf_head.wh_addr))).
    assign y_at = y_at + 0.1 .
    if length(trim(bf_head.wh_addr1)) NE 0
    then do:
        run printat in printer_driver (3.60, y_at,
                    string(trim(bf_head.wh_addr1))).
        assign y_at = y_at + 0.1 .
    end .
    run printat in printer_driver (3.60, y_at ,
                string(trim(bf_head.wh_city) + "," + trim(bf_head.wh_state) +
                           " " + trim(bf_head.wh_zip))).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (3.60, y_at , 
                    string(trim(bf_head.wh_country))) .
    /* Third column */
    assign y_at = 1.00 .      
    run printat in printer_driver (6.00, y_at, "Date:") .
    run printat in printer_driver (6.60, y_at, string(bf_head.ord_date)) . 
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (6.00, y_at, "Cust Po:" ) . 
    run printat in printer_driver (6.60, y_at, string(trim(bf_head.cust_po))) . 
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (6.00, y_at, "Wave:"). 
    run printat in printer_driver (6.60, y_at, string(bf_head.ord_wave)).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (6.00, y_at, "Terms:") .
    run printat in printer_driver (6.60, y_at, string(bf_head.terms)).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (6.00, y_at, "Ship Via:"). 
    run printat in printer_driver (6.60, y_at, 
                string(bf_head.ord_carrier + " " + bf_head.ord_service)).
    
    /* first column */

    assign y_at = 1.0 . 
    run printat in printer_driver (0.35, y_at, "Page:" ).
    run printat in printer_driver (1.10, y_at, string(my_page)).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (0.35, y_at, "Customer ID:") .
    run printat in printer_driver (1.10, y_at, string(bf_head.cust_id) ).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (0.35, y_at, "Name:") .
    run printat in printer_driver (1.10, y_at, string(trim(bf_head.shp_name))).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (0.35, y_at, "Sales Order:") .
    run printat in printer_driver (1.10, y_at, string(trim(bf_head.sales_ord))).
    assign y_at = y_at + 0.1 .
    run printat in printer_driver (0.35, y_at, "Order Num:") .
    run printat in printer_driver (1.10, y_at, string(trim(bf_head.order))).
    run printat in printer_driver (2.00, y_at, "Suffix:") .   
    run printat in printer_driver (2.50, y_at, string(trim(bf_head.suffix))).

    assign y_at = y_at + 0.05.
    
    run setbar in printer_driver (input 0.40, input 16.0) .

    run barat in printer_driver (1.10, y_at, string(trim(bf_head.order))) .
    assign y_at = y_at + 0.5 .
    
    run printat in printer_driver (0.10, y_at,
        "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------") .

    /* shipping infomation */
    assign y_at =  y_at + 0.1 .
    run printat in printer_driver (0.35, y_at, "Ship To :") .
    run printat in printer_driver (1.10, y_at, trim(bf_head.shp_name)).
    assign y_at =  y_at + 0.1 .
    run printat in printer_driver (1.10, y_at, trim(bf_head.shp_addr)).
    assign y_at =  y_at + 0.1 .
    if length(trim(bf_head.shp_addr1)) NE 0
    then do:
        run printat in printer_driver (1.10, y_at, trim(bf_head.shp_addr1)).
        assign y_at = y_at + 0.1 .
    end .
    if length(trim(bf_head.shp_addr2)) NE 0
    then do:
        run printat in printer_driver (1.10, y_at, trim(bf_head.shp_addr2)).
        assign y_at = y_at + 0.1 .
    end .
    run printat in printer_driver (1.10, y_at ,
                    trim(bf_head.shp_city) + "," + trim(bf_head.shp_state) + 
                    " " + trim(bf_head.shp_zip)) .
    assign y_at =  y_at + 0.1 .
    run printat in printer_driver (1.10, y_at , trim(bf_head.shp_country)).

    /* comments */
    assign y_at = y_at + 0.10.
    assign i = 1 .
    do while i LE bf_head.line_comm 
    :
        if i EQ 1 
        then do:
            run printat in printer_driver (0.35, y_at , "Order Remarks:") .
            assign y_at = y_at  +  0.10  .
        end .
        run printat in printer_driver (1.10, y_at,trim(bf_head.comm[i])) .
        assign 
            y_at = y_at  +  0.10 
            i = i + 1 
        .
    end .
    assign y_at = y_at  +  0.20 .
                    
    run printat in printer_driver (0.10, y_at , "") . /* gift */
    run printat in printer_driver (0.40, y_at , "Zone").
    run printat in printer_driver (0.80, y_at , "Loc.").
    run printat in printer_driver (1.60, y_at , "Item").
    run printat in printer_driver (3.00, y_at , "Description") .
    run printat in printer_driver (5.10, y_at , "SN" ) .  
    run printat in printer_driver (5.50, y_at , "Pick Qty" ).
    run printat in printer_driver (6.15, y_at , "Unit").
    run printat in printer_driver (6.70, y_at , "Qty Picked" ). 
    assign y_at = y_at  +  0.10 .

    run printat in printer_driver (0.10, y_at ,
        "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------") .
    assign row_num = y_at + 0.20 .   /* may be chang */    

end procedure.

procedure prn_body :    /* print the body   */

    define input parameter y_at as decimal no-undo .
    define input parameter my_num as recid no-undo .

    define buffer my_dtl for pick_dtl .
    
    find first my_dtl no-lock 
        where 
            recid(my_dtl) = my_num 
        no-error
    .
    run printat in printer_driver(0.35, y_at, my_dtl.gift ).
    run printat in printer_driver(0.50, y_at, my_dtl.ch_zone).
    run printat in printer_driver(0.80, y_at, my_dtl.ch_loc).
    run printat in printer_driver(1.50, y_at, my_dtl.item).     
    run printat in printer_driver(2.75, y_at, my_dtl.chdesc). 
    run printat in printer_driver(5.10, y_at, my_dtl.sn ).      
    run printat in printer_driver(5.65, y_at, my_dtl.pick_qty ).
    run printat in printer_driver(6.15, y_at, my_dtl.pick_unit) .
    run printat in printer_driver(6.80, y_at, "_________" ).    
end procedure.

