<?xml version="1.0" encoding="utf-8"?>
<XMLScript Version="2.0">
   <Command>
       <Print JobName="Job1">
           <PrintSetup>
               <Printer>\\panther\zebra220</Printer>
           </PrintSetup>
           <Format CloseAtEndOfJob="true">itemlabel.btw</Format>
           <RecordSet Name="Text File 1" Type="btTextFile">
               <Delimitation>btDelimCustom</Delimitation>
               <FieldDelimiter>|</FieldDelimiter>
               <UseFieldNamesFromFirstRecord>true</UseFieldNamesFromFirstRecord>
               <TextData>
               <![CDATA[ItemNumber|ItemDescription|RcdDate|ReceivedBy|PONumber|POSuffix|Lot|Expiry|Serial|Quantity|Status|ItemType|MSRP|UOM|Company|Warehouse|Zone|AltNum|UPC|Group|Line|SubLine|Class|MSDS|Country|SecDesc|LongDesc|NumofLabels|Sell Price
               nmtest|desc for nmtest|04/06/09|IRMS||  || |0||S|0|EA|MDC|CRP|02||nm|||||sec desc for nmtest|long desc for nmtest|1|$0.0000
               ]]>
               </TextData>
           </RecordSet>
       </Print>
   </Command>
</XMLScript>
