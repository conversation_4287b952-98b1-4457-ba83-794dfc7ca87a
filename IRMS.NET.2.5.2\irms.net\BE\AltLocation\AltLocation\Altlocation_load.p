/**=================================================================**
* Y:\irms.net.1.4.0\irms.net\BE\AltLocation\AltLocation\Altlocation_load.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 17/07/07, 10:39 PM
**=================================================================**/


/* Business Entity Definintions */
{AltLocation/AltLocation/Altlocation_ds.i}
{AltLocation/AltLocation/Altlocation_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT        PARAM ipcGUID AS  CHAR .
    DEF       OUTPUT PARAM DATASET FOR dsAltlocation .


    DEF VAR hDataSet   AS HANDLE   NO-UNDO. 
    DEF VAR cTable     AS CHAR     NO-UNDO. 


    hDataSet = DYNAMIC-FUNCTION('getProperty' IN THIS-PROCEDURE,'DataSetHandle').
    IF hDataSet:NUM-RELATIONS > 0 THEN
       cTable = hDataSet:GET-RELATION(1):PARENT-BUFFER:NAME.
    ELSE
       cTable = hDataSet:GET-BUFFER-HANDLE(1):NAME.


    CREATE ds_Filter.
    ASSIGN
       ds_Filter.TableName  = cTable
       ds_Filter.FieldName  = 'GUID'
       ds_Filter.Operand    = '='
       ds_Filter.FieldValue = ipcGUID
       .


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


