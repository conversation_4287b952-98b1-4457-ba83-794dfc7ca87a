/**=================================================================**
* S:\IRMS.NET.2.6.0\irms.net\BE\ActionTable\ActionTable_proxy.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 09/19/08, 10:35 PM
**=================================================================**/


/* Business Entity Definintions */
{BE/ActionTable/ActionTable_ds.i}
{BE/ActionTable/ActionTable_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsActionTable .


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


