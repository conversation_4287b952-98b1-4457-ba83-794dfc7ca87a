/**=================================================================**
* Y:\irms.net.1.3.1\irms.net\BE\Audit\Search\Search_GetSchema.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 02/26/14, 17:20 PM
**=================================================================**/


/* Business Entity Definintions */
{Audit/Search/Search_ds.i}
{Audit/Search/Search_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Schema.


    RUN Schema_Fill .


/**************************** END OF FILE ****************************/


