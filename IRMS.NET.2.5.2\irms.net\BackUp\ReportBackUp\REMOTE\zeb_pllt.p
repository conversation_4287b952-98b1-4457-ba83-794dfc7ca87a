/**
*** <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tote labels for Zebra printer .
*** File name is hard coded in lbl_prnt.w .
**/


/* Version control Id */
DEFINE VARIABLE SCCS_ID AS CHARACTER
    NO-UNDO
    INITIAL "@(#) $Header: /pdb/9.01/remote/RCS/zeb_pllt.p,v 1.4 2000-02-24 10:46:57-06 tanya Exp $~n"
.

DEF INPUT PARAMETER ch_type    AS CHAR NO-UNDO .
DEF INPUT PARAMETER i_lbl_size AS INT  NO-UNDO .
DEF INPUT PARAMETER i_qty      AS INT  NO-UNDO .
DEF INPUT PARAMETER i_exact    AS INT  NO-UNDO .
DEF INPUT PARAMETER ch_printer AS CHAR NO-UNDO .


DEF VAR ch_file    AS CHAR NO-UNDO .
DEF VAR ch_command AS CHAR NO-UNDO .
DEF VAR ch_label   AS CHAR NO-UNDO .
DEF VAR ch_text    AS CHAR NO-UNDO .
DEF VAR i_count    AS INT  NO-UNDO .



/*
** Read parameters from the environment
*/

MESSAGE "Starting Printing..." .

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .

OUTPUT TO VALUE ( ch_file ).    

/* Main block */
   CASE ch_type :
      WHEN 'P' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Pallet' .

      WHEN 'C' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Carton' .

      WHEN 'T' THEN  /* Valid options for type */
         ASSIGN
            ch_label = 'Tote' .

      OTHERWISE
      DO:
        MESSAGE "Invalid type passed".
        return error .
      END.

   END CASE .

   IF i_exact > 0 THEN
   DO: /* Exact item - only print it... */
      ASSIGN
        ch_text = ch_type + STRING ( i_exact, "999999999" ) .

      RUN do_label ( ch_label , ch_text ) .
   END.
   ELSE
   DO: /* Print as many specified... */
      ASSIGN 
         i_count = 1 .

      printmanyloop:
      DO WHILE TRUE:
         IF ( i_count > i_qty ) THEN 
            LEAVE printmanyloop.
         
         CASE ch_type :
            WHEN 'P' THEN
               ASSIGN
                  ch_text = 'P' + STRING ( 
                         NEXT-VALUE ( print_pallet ) , '999999999' ) .

            WHEN 'C' THEN
               ASSIGN
                  ch_text = 'C' + STRING ( 
                         NEXT-VALUE ( cartonmst_carton_id ) , '999999999' ) .

            WHEN 'T' THEN
               ASSIGN
                  ch_text = 'T' + STRING (
                         NEXT-VALUE ( print_pallet ) , '999999999' ) .
         END CASE .

         RUN do_label ( ch_label , ch_text ) .
         
         i_count = i_count + 1 . 
      END.
   END.

/* Print Carton or Pallet Label to file in temp dir. */

OUTPUT CLOSE . 
    
if (opsys eq "UNIX")
then do:
    ASSIGN
       ch_command = "lp -c -d" + ch_printer + " " + ch_file .
       message ch_command .
       OS-COMMAND SILENT VALUE ( ch_command ) .

       OS-DELETE         VALUE ( ch_file    ) .  
end .
else do: /*NT*/
   
    /********************* old code using spooler *********************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .
    *****************************************************************/

       message "copying file  " ch_file "to printer " ch_printer .

       OS-COPY VALUE( ch_file ) VALUE( ch_printer) .

       OS-DELETE  VALUE ( ch_file  ) .   


end .


message "Done!" .
return .


                             
/************************ Do Label *************************/
PROCEDURE do_label:
   DEF INPUT PARAMETER ch_label AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_text  AS CHAR NO-UNDO .
   
   DEF VAR ch_prnt_label AS CHAR NO-UNDO .
   DEF VAR ch_prnt_text  AS CHAR NO-UNDO .

   IF i_lbl_size = 0 THEN /* 4x6 Standard Label*/
      PUT UNFORMATTED  "^XA^CFD^FS"
                       "^FO3,8"
                       "^GB792,1206,4^FS"
                       "^FO553,8"
                       "^GB0,1206,4^FS"
                       "^FO725,28"
                       "^AGR^FD" + ch_label + " ID:" "^FS"
                       "^FO580,130"
                       "^AGR,120,80^FD" + ch_text + "^FS"
                       "^BY7^FO90,100^BCR,400,N,N,N^FD" +
                       ch_text + "^FS"
                       "^XZ".
   ELSE  /* 2x4 Standard Label */
      PUT UNFORMATTED "^XA^CFD^FS"                               +
                      "^FO3,8^GB792,390,4^FS"                    +
                      "^FO3,8^GB792,155,4^FS"                    +
                      "^FO150,70^A0N,100,100^FD" + ch_text "^FS" +
                      "^FO20,20^A0N,50,50^FD" + ch_label + "^FS" +
                      "^BY4^FO110,175^BCN,200,N,N,N^FD" + ch_text + 
                      "^FS^XZ"
                      .
                             
END PROCEDURE.


/* End Of Program */

