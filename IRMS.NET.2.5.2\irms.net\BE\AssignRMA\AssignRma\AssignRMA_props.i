/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\AssignRMA\AssignRMA_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:38 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qttordhdrr FOR ttordhdrr SCROLLING . 
QUERY qttordhdrr:QUERY-PREPARE("FOR EACH ttordhdrr").
QUERY qttordhdrr:QUERY-OPEN.


DEF  QUERY qttordhdrr_BEFORE FOR ttordhdrr_BEFORE SCROLLING . 
QUERY qttordhdrr_BEFORE:QUERY-PREPARE("FOR EACH ttordhdrr_BEFORE").
QUERY qttordhdrr_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "srcordhdrs" */
DEFINE BUFFER ordhdr_1 FOR irms.ordhdr.
DEFINE QUERY qSrcsrcordhdrs
    FOR ordhdr_1
        SCROLLING.
DEFINE DATA-SOURCE srcordhdrs
    FOR QUERY qSrcsrcordhdrs
        ordhdr_1 KEYS (id)        .
DATA-SOURCE srcordhdrs:PREFER-DATASET = no.
DATA-SOURCE srcordhdrs:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttordhdrr                    AS  HANDLE              
    FIELD   hQry_ttordhdrr                   AS  HANDLE              
    FIELD   hQry_ttordhdrr_BEFORE            AS  HANDLE              
    FIELD   ttordhdrr_DataSourceHdl          AS  HANDLE              
    FIELD   ttordhdrr_BatchSize              AS  INTEGER             
                                                 INIT 1
    FIELD   ttordhdrr_Fill                   AS  LOGICAL             
                                                 INIT yes
    FIELD   ttordhdrr_CanRead                AS  LOGICAL             
                                                 INIT yes
    FIELD   ttordhdrr_CanCreate              AS  LOGICAL             
                                                 INIT no
    FIELD   ttordhdrr_CanUpdate              AS  LOGICAL             
                                                 INIT no
    FIELD   ttordhdrr_CanDelete              AS  LOGICAL             
                                                 INIT no
    FIELD   ttordhdrr_Src_Names              AS  CHARACTER           
                                                 INIT ""
    FIELD   ttordhdrr_Src_Hdls               AS  CHARACTER           
                                                 INIT ""
    FIELD   ttordhdrr_CurrentSource          AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   ttordhdrr_UniqueKey              AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ttordhdrr_srcordhdrs_Map         AS  CHARACTER           
                                                 INIT ""
    FIELD   ttordhdrr_srcordhdrs_CF          AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   ttordhdrr_srcordhdrs_NoP         AS  CHARACTER           
                                                 INIT ""
    FIELD   srcordhdrs_hdl                   AS  HANDLE              
    FIELD   srcordhdrs_UseQuery              AS  LOGICAL             
                                                 INIT yes
    FIELD   srcordhdrs_PostTable             AS  CHARACTER           
                                                 INIT "ordhdr_1"
    FIELD   srcordhdrs_qhdl                  AS  HANDLE              
    FIELD   srcordhdrs_ordhdr_1_W            AS  CHARACTER           
                                                 INIT ""
    FIELD   srcordhdrs_ordhdr_1_S            AS  CHARACTER           
                                                 INIT ""
    FIELD   srcordhdrs_Buffs                 AS  CHARACTER           
                                                 INIT "ordhdr_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "ordhdr,ttordhdrr"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "ttordhdrr,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsAssignRMA:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_ttordhdrr                    = QUERY qttordhdrr:HANDLE
       htt_ttordhdrr                     = TEMP-TABLE ttordhdrr:HANDLE
       hQry_ttordhdrr_BEFORE             = QUERY qttordhdrr_BEFORE:HANDLE
       ttordhdrr_src_Names               = 'srcordhdrs,Default'
       ttordhdrr_src_Hdls                =         STRING(DATA-SOURCE srcordhdrs:HANDLE)
                                           + ',' + STRING(DATA-SOURCE srcordhdrs:HANDLE)
       ttordhdrr_srcordhdrs_Map          =         'actual_freight,ordhdr_1.actual_freight'
                                           + ',' + 'assigned,ordhdr_1.assigned'
                                           + ',' + 'batch,ordhdr_1.batch'
                                           + ',' + 'bill_addr1,ordhdr_1.bill_addr1'
                                           + ',' + 'bill_addr2,ordhdr_1.bill_addr2'
                                           + ',' + 'bill_addr_ext1,ordhdr_1.bill_addr_ext1'
                                           + ',' + 'bill_addr_ext2,ordhdr_1.bill_addr_ext2'
                                           + ',' + 'bill_addr_ext3,ordhdr_1.bill_addr_ext3'
                                           + ',' + 'bill_city,ordhdr_1.bill_city'
                                           + ',' + 'bill_country,ordhdr_1.bill_country'
                                           + ',' + 'bill_email,ordhdr_1.bill_email'
                                           + ',' + 'bill_name,ordhdr_1.bill_name'
                                           + ',' + 'bill_phone,ordhdr_1.bill_phone'
                                           + ',' + 'bill_state,ordhdr_1.bill_state'
                                           + ',' + 'bill_zip,ordhdr_1.bill_zip'
                                           + ',' + 'box_id,ordhdr_1.box_id'
                                           + ',' + 'branch_id,ordhdr_1.branch_id'
                                           + ',' + 'cancelled_by,ordhdr_1.cancelled_by'
                                           + ',' + 'cancel_date_time,ordhdr_1.cancel_date_time'
                                           + ',' + 'cancel_flag,ordhdr_1.cancel_flag'
                                           + ',' + 'card,ordhdr_1.card'
                                           + ',' + 'card_type,ordhdr_1.card_type'
                                           + ',' + 'carrier,ordhdr_1.carrier'
                                           + ',' + 'charges,ordhdr_1.charges'
                                           + ',' + 'charge_type,ordhdr_1.charge_type'
                                           + ',' + 'class,ordhdr_1.class'
                                           + ',' + 'clearance_code,ordhdr_1.clearance_code'
                                           + ',' + 'clearance_required,ordhdr_1.clearance_required'
                                           + ',' + 'cod_addr1,ordhdr_1.cod_addr1'
                                           + ',' + 'cod_addr2,ordhdr_1.cod_addr2'
                                           + ',' + 'cod_addr3,ordhdr_1.cod_addr3'
                                           + ',' + 'cod_addr4,ordhdr_1.cod_addr4'
                                           + ',' + 'cod_addr5,ordhdr_1.cod_addr5'
                                           + ',' + 'cod_amount,ordhdr_1.cod_amount'
                                           + ',' + 'cod_charge,ordhdr_1.cod_charge'
                                           + ',' + 'cod_city,ordhdr_1.cod_city'
                                           + ',' + 'cod_country,ordhdr_1.cod_country'
                                           + ',' + 'cod_email,ordhdr_1.cod_email'
                                           + ',' + 'cod_flag,ordhdr_1.cod_flag'
                                           + ',' + 'cod_name,ordhdr_1.cod_name'
                                           + ',' + 'cod_phone,ordhdr_1.cod_phone'
                                           + ',' + 'cod_state,ordhdr_1.cod_state'
                                           + ',' + 'cod_zip,ordhdr_1.cod_zip'
                                           + ',' + 'comment,ordhdr_1.comment'
                                           + ',' + 'consignee_attn,ordhdr_1.consignee_attn'
                                           + ',' + 'co_num,ordhdr_1.co_num'
                                           + ',' + 'customer_freight,ordhdr_1.customer_freight'
                                           + ',' + 'customer_po,ordhdr_1.customer_po'
                                           + ',' + 'custom_selector,ordhdr_1.custom_selector'
                                           + ',' + 'cust_code,ordhdr_1.cust_code'
                                           + ',' + 'del_route,ordhdr_1.del_route'
                                           + ',' + 'dept_num,ordhdr_1.dept_num'
                                           + ',' + 'discount,ordhdr_1.discount'
                                           + ',' + 'DiscountAmt,ordhdr_1.DiscountAmt'
                                           + ',' + 'doc_id,ordhdr_1.doc_id'
                                           + ',' + 'drop_cube,ordhdr_1.drop_cube'
                                           + ',' + 'drop_type,ordhdr_1.drop_type'
                                           + ',' + 'drop_weight,ordhdr_1.drop_weight'
                                           + ',' + 'exp_ship_date,ordhdr_1.exp_ship_date'
                                           + ',' + 'freight_terms,ordhdr_1.freight_terms'
                                           + ',' + 'gift,ordhdr_1.gift'
                                           + ',' + 'gift_wrap,ordhdr_1.gift_wrap'
                                           + ',' + 'gift_wrap_type,ordhdr_1.gift_wrap_type'
                                           + ',' + 'guaranteed_del_time,ordhdr_1.guaranteed_del_time'
                                           + ',' + 'GUID,ordhdr_1.GUID'
                                           + ',' + 'hold_reason,ordhdr_1.hold_reason'
                                           + ',' + 'host_batch,ordhdr_1.host_batch'
                                           + ',' + 'host_origin,ordhdr_1.host_origin'
                                           + ',' + 'host_selector,ordhdr_1.host_selector'
                                           + ',' + 'host_sequence,ordhdr_1.host_sequence'
                                           + ',' + 'id,ordhdr_1.id'
                                           + ',' + 'image_name,ordhdr_1.image_name'
                                           + ',' + 'international,ordhdr_1.international'
                                           + ',' + 'kit_build_type,ordhdr_1.kit_build_type'
                                           + ',' + 'line_count,ordhdr_1.line_count'
                                           + ',' + 'lot,ordhdr_1.lot'
                                           + ',' + 'max_days,ordhdr_1.max_days'
                                           + ',' + 'my_desc1,ordhdr_1.my_desc1'
                                           + ',' + 'my_desc2,ordhdr_1.my_desc2'
                                           + ',' + 'num_cartons,ordhdr_1.num_cartons'
                                           + ',' + 'order,ordhdr_1.order'
                                           + ',' + 'order_date,ordhdr_1.order_date'
                                           + ',' + 'order_status,ordhdr_1.order_status'
                                           + ',' + 'order_suffix,ordhdr_1.order_suffix'
                                           + ',' + 'orig_cube,ordhdr_1.orig_cube'
                                           + ',' + 'orig_order_date,ordhdr_1.orig_order_date'
                                           + ',' + 'orig_weight,ordhdr_1.orig_weight'
                                           + ',' + 'partial,ordhdr_1.partial'
                                           + ',' + 'pay_method,ordhdr_1.pay_method'
                                           + ',' + 'pool,ordhdr_1.pool'
                                           + ',' + 'printed,ordhdr_1.printed'
                                           + ',' + 'priority,ordhdr_1.priority'
                                           + ',' + 'product,ordhdr_1.product'
                                           + ',' + 'product_qty,ordhdr_1.product_qty'
                                           + ',' + 'pro_number,ordhdr_1.pro_number'
                                           + ',' + 'rate_type,ordhdr_1.rate_type'
                                           + ',' + 'row_status,ordhdr_1.row_status'
                                           + ',' + 'service,ordhdr_1.service'
                                           + ',' + 'ship_addr1,ordhdr_1.ship_addr1'
                                           + ',' + 'ship_addr2,ordhdr_1.ship_addr2'
                                           + ',' + 'ship_addr_ext1,ordhdr_1.ship_addr_ext1'
                                           + ',' + 'ship_addr_ext2,ordhdr_1.ship_addr_ext2'
                                           + ',' + 'ship_addr_ext3,ordhdr_1.ship_addr_ext3'
                                           + ',' + 'ship_city,ordhdr_1.ship_city'
                                           + ',' + 'ship_country,ordhdr_1.ship_country'
                                           + ',' + 'ship_cube,ordhdr_1.ship_cube'
                                           + ',' + 'ship_cust_code,ordhdr_1.ship_cust_code'
                                           + ',' + 'ship_date,ordhdr_1.ship_date'
                                           + ',' + 'ship_email,ordhdr_1.ship_email'
                                           + ',' + 'ship_msg,ordhdr_1.ship_msg'
                                           + ',' + 'ship_name,ordhdr_1.ship_name'
                                           + ',' + 'ship_phone,ordhdr_1.ship_phone'
                                           + ',' + 'ship_state,ordhdr_1.ship_state'
                                           + ',' + 'ship_weight,ordhdr_1.ship_weight'
                                           + ',' + 'ship_zip,ordhdr_1.ship_zip'
                                           + ',' + 'shp_by_irms,ordhdr_1.shp_by_irms'
                                           + ',' + 'store,ordhdr_1.store'
                                           + ',' + 'tax,ordhdr_1.tax'
                                           + ',' + 'type,ordhdr_1.type'
                                           + ',' + 'wh_num,ordhdr_1.wh_num'
       srcordhdrs_hdl                    = DATA-SOURCE srcordhdrs:HANDLE
       srcordhdrs_qhdl                   = QUERY qSrcsrcordhdrs:HANDLE
       TopLevelTables                    = 'ttordhdrr'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/AssignRMA_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAssignRMA .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAssignRMA BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAssignRMA .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAssignRMA BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttordhdrr_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsAssignRMA .
     RUN ttordhdrr_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAssignRMA BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttordhdrr_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsAssignRMA .
     RUN ttordhdrr_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAssignRMA BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_ttordhdrr_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAssignRMA .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('ttordhdrr') NO-ERROR .
     RUN ttordhdrr_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAssignRMA BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttordhdrr_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsAssignRMA .
     RUN AfterRowFill  IN THIS-PROCEDURE ('ttordhdrr') NO-ERROR .
     RUN ttordhdrr_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsAssignRMA BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


