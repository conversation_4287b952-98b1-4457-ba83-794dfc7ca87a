/**=================================================================**
* Y:\irms.net\src\be\Abcclassification\Abcclassification\Abcclassification_load.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 12/07/04, 15:30 PM
**=================================================================**/


/* Business Entity Definintions */
{Abcclassification/Abcclassification/Abcclassification_ds.i}
{Abcclassification/Abcclassification/Abcclassification_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT        PARAM ipcGUID AS  CHAR .
    DEF       OUTPUT PARAM DATASET FOR dsAbcclassification .


    DEF VAR hDataSet   AS HANDLE   NO-UNDO. 
    DEF VAR cTable     AS CHAR     NO-UNDO. 
    DEF VAR vconum AS CHAR NO-UNDO.
    DEF VAR vwhnum AS CHAR NO-UNDO.

    hDataSet = DYNAMIC-FUNCTION('getProperty' IN THIS-PROCEDURE,'DataSetHandle').
    IF hDataSet:NUM-RELATIONS > 0 THEN
       cTable = hDataSet:GET-RELATION(1):PARENT-BUFFER:NAME.
    ELSE
       cTable = hDataSet:GET-BUFFER-HANDLE(1):NAME.

    ASSIGN vconum = DYNAMIC-FUNCTION('$getcontext' IN TARGET-PROCEDURE,ipcContextID,"co_num")
           vwhnum = DYNAMIC-FUNCTION('$getcontext' IN TARGET-PROCEDURE,ipcContextID,"wh_num").

    FIND LAST IRMS.abc WHERE IRMS.abc.co_num = vconum AND IRMS.abc.wh_num = vwhnum NO-LOCK NO-ERROR.
    IF AVAILABLE IRMS.abc THEN
        ASSIGN ipcGUID = STRING(IRMS.abc.GUID).
    ELSE
    DO:
        CREATE IRMS.abc NO-ERROR.
        ASSIGN IRMS.abc.co_num           = vconum
               IRMS.abc.wh_num           = vwhnum
               IRMS.abc.a_dollar_percent = 70
               IRMS.abc.b_dollar_percent = 20
               IRMS.abc.c_dollar_percent = 10
               IRMS.abc.o_dollar_percent = 0
               IRMS.abc.a_count_percent  = 5
               IRMS.abc.b_count_percent  = 10
               IRMS.abc.c_count_percent  = 65
               IRMS.abc.o_count_percent  = 20
               IRMS.abc.a_count_interval = 45
               IRMS.abc.b_count_interval = 90
               IRMS.abc.c_count_interval = 180
               IRMS.abc.o_count_interval = 360
               IRMS.abc.recalc_interval  = 1
               IRMS.abc.recalc_timeframe = "M"
               IRMS.abc.recalc_type      = 'Q'.

        ASSIGN ipcGUID = STRING(IRMS.abc.GUID).
    END.

    CREATE ds_Filter.
    ASSIGN
       ds_Filter.TableName  = cTable
       ds_Filter.FieldName  = 'GUID'
       ds_Filter.Operand    = '='
       ds_Filter.FieldValue = ipcGUID.


    RELEASE IRMS.abc NO-ERROR.

    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


