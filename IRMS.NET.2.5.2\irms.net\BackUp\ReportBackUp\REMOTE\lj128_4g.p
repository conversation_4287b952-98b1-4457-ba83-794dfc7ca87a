/*
** glauber Wed 06Sep95 16:48
** lj128.p -- graphics output for "code 128" barcodes
** (to be run persistently, like a driver).
**
** The only procedures that should be accessed from the outside are:
** 128_set_width, 128_set_height, and 128_bar.
**
** 12sep95 glauber: optimized to almost unreadability
**                  (still runs too slow, though...).
**
** 13sep95 glauber: inlining a few things.
*/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.0/rf/RCS/lj128_4g.p,v 1.2 1997/09/05 18:04:06 glauber Exp $~n"
.

/* maximum barcode length, in characters, including start, stop, etc */
&Scoped-Define MAX_BAR_LEN 80 

&Scoped-Define C128_STOP 106
&Scoped-Define C128_START_A 103
&Scoped-Define C128_START_B 104
&Scoped-Define C128_START_C 105
&Scoped-Define C128_SHIFT_TO_A 101
&Scoped-Define C128_SHIFT_TO_B 100
&Scoped-Define C128_SHIFT_TO_C 99

define variable outstring as character no-undo .




/* base (smallest) width, in dots (1/300") */     
/*
** 1 mil corresponds to 3.33 
** 15 mil is 5.45
** 18 mil is 5.45
** 21 mil is 6.3
*/
define variable basew as integer no-undo initial 5 .

/* current position, in dots */
define variable currow as integer no-undo initial 100 .
define variable curcol as integer no-undo initial 100 .

/* barcode height, in dots */
define variable baseh as integer no-undo initial 200 .


/*
** This is encoded as patterns of bars and spaces
** (the first character is always a bar). Each element
** (bar or space) may be 1 to 4 times the basic element
** width. For instance, the "!" character may be encoded
** as "222122" (one 2-width bar, one 1-width space, 1 2-width
** bar, etc.). This is stored as an integer in reverse
** order so that elements may be retrieved by using modulo.
** So, the "!" character is encoded 221222.
**
** Progress arrays are 1-based, so we encode the 0 ("space")
** at the end instead.
*/
define variable encoding as integer no-undo extent 107
    initial [
        221222,
        122222,
        322121,
        223121,
        222131,
        312221,
        213221,
        212231,
        312122,
        213122,
        212132,
        232211,
        231221,
        132221,
        222311,
        221321,
        122321,
        112322,
        231122,
        132122,
        212312,
        211322,
        131213,
        222113,
        221123,
        122123,
        212213,
        211223,
        112223,
        321212,
        123212,
        121232,
        323111,
        321131,
        123131,
        313211,
        311231,
        113231,
        313112,
        311132,
        113132,
        331211,
        133211,
        131231,
        321311,
        123311,
        121331,
        121313,
        133112,
        131132,
        311312,
        113312,
        131312,
        321113,
        123113,
        121133,
        311213,
        113213,
        111233,
        111413,
        114122,
        111134,
        422111,
        224111,
        421121,
        124121,
        221141,
        122141,
        412211,
        214211,
        411221,
        114221,
        211241,
        112241,
        112142,
        411122,
        111314,
        211142,
        111431,
        242111,
        241121,
        142121,
        212411,
        211421,
        112421,
        212114,
        211124,
        112124,
        141212,
        121412,
        121214,
        341111,
        143111,
        141131,
        311411,
        113411,
        311114,
        113114,
        141311,
        131411,
        141113,
        131114,
        214112,
        412112,
        232112,
        2111332,  /* STOP character (longer than the others) */
        222212    /* space (value zero) */
    ] .

/* calculated widths for spaces and bars */
define variable calcwidth as integer no-undo extent 4 .

/* temporary storage for the bar code */
define variable b_array as integer no-undo extent {&MAX_BAR_LEN} .


return .



/*
** Takes the element width in mils (1/100")
** (roundoff errors will happen because the laser dot is 1/300")
*/
procedure 128_set_width:                       

    define input parameter win as decimal no-undo .
    
    assign
        basew = round((win * 300 / 1000), 0)
        calcwidth[1] = basew
        calcwidth[2] = calcwidth[1] + basew
        calcwidth[3] = calcwidth[2] + basew
        calcwidth[4] = calcwidth[3] + basew
    .

end procedure . /*128_set_width*/



/* takes the bar height in inches */
procedure 128_set_heigth:         

    define input parameter hin as decimal no-undo .
    
    assign baseh = round(( hin * 300 ), 0) .

end procedure . /*128_set_heigth*/



/*
** Takes a position, string, barcode type ("B" or "C"), returns the
** LJ3 commands to produce that barcode.
*/
procedure 128_bar:

    define input parameter xpos as decimal no-undo .
    define input parameter ypos as decimal no-undo .
    define input parameter source as character no-undo .      
    define input parameter type as character no-undo .
    define output parameter destination as character no-undo .
    
    define variable codestring as character no-undo .
    
    if ((source eq ?) or (source eq ""))
    then do:
        assign destination = "" .
    end .
    else do:
        assign
            outstring = "" 
            currow = round( (ypos * 300), 0 )
            curcol = round( (xpos * 300), 0 )
        .

        if type eq "C"
        then do:
            run encode_128_c( input source ) .
        end .
        else if type eq "B"
        then do:
            run encode_128_b( input source ) .
        end .
        else do:
            /* unsupported type */
            assign destination = "" .
            return error .
        end .

        run out128 .
        assign
            destination = outstring
            outstring = ?
        .
    end .

    return .

end procedure . /*128_bar*/


/*
** Stub, so programs can run in a test environment.
** The real maxicode call is in lj128.p, and requires HLC with
** NeoMedia's maxicode routine.
*/
procedure maxicode:
    define input parameter ch_postal_code as character no-undo .
    define input parameter i_country_code as integer no-undo .
    define input parameter i_service_code as integer no-undo .
    define input parameter ch_tracking_id as character no-undo .
    define input parameter ch_shipper_id as character no-undo .
    define input parameter ch_scac as character no-undo .

    define output parameter ch_maxicode as character no-undo .

    assign ch_maxicode = "~n~n~n~n~n~nmaxicode" .
end procedure . /*maxicode*/



procedure destroy:
    delete procedure this-procedure .
end procedure . /*destroy*/


procedure output:
    define input parameter it as character no-undo .
    
    assign outstring = outstring + it .
end .



procedure out128char:

    define input parameter thecode as integer no-undo .

    define variable codeint as integer no-undo .
    define variable curw as integer no-undo .
    define variable isbar as logical no-undo initial false .
    
    assign
        codeint = (if thecode eq 0
                        then encoding[107]
                        else encoding[thecode])
    .                                       

    /* extract one digit at a time */
    do while codeint gt 0:
    
        assign
            curw = calcwidth[ (codeint modulo 10) ]
            codeint = truncate(codeint / 10, 0)
            isbar = not isbar
        .
        
        if isbar
        then do:
            /* run outbar(curw) . */
            assign
                outstring = outstring +
                    "~E*p" + string(currow) + "Y" +
                    "~E*p" + string(curcol) + "X" +
                    "~E*c" + string(curw) + "A" +
                    "~E*c" + string(baseh) + "B" +
                    "~E*c0P"
                curcol = curcol + curw
            .          
        end .
        else do:              
            assign curcol = curcol + curw .
        end .
            
    end .
    
end .


procedure outbar:

    define input parameter barwidth as integer no-undo .
   
    run output(
            "~E*p" + string(currow) + "Y" +
            "~E*p" + string(curcol) + "X" +
            "~E*c" + string(calcwidth[barwidth]) + "A" +
            "~E*c" + string(baseh) + "B" +
            "~E*c0P"
            ) .

    assign
        curcol = curcol + calcwidth[barwidth]
    .

end procedure . /*outbar*/




/*
** out128:
** Takes a string which is a comma-separated list of
** codes to output (including any start codes, but
** excluding check-character and stop code) .
** Outputs them, calculates and
** outputs the check-character, and outputs the stop code.
** Note, the codes are 128 codes, not ASCII codes.
** In subset B of code 128, the 128 codes are (ASCII - 32).
** Subsets A and C are their own animals.
*/
procedure out128:

    define variable pos as integer no-undo .
    define variable weight as integer no-undo .
    define variable thiscode as integer no-undo .
    define variable codeval as integer no-undo .
    define variable thecheck as integer no-undo .
    define variable sum as integer no-undo .

    assign
        pos = 2
        weight = 1
        thiscode = b_array[1]
        sum = thiscode
    .
    run out128char(thiscode) .
    
    outloop:
    do while true:
        assign
            thiscode = b_array[pos]
        .
        if thiscode eq (-1)
        then do:
            leave outloop .
        end .

        assign 
            codeval = thiscode * (weight)
            sum = sum + codeval
            weight = weight + 1
            pos = pos + 1
        .

        run out128char(thiscode) .
    end . /*outloop*/

    assign thecheck = (sum modulo 103) .
    run out128char( thecheck ) .

    /* output the stop character */
    run out128char( {&C128_STOP} ) .

end procedure . /*out128*/



/*
** Takes a string, outputs the list
** expected by out128, encoded in subset B.
** In subset B, all characters are represented
** in same sequence as ASCII, but starting with
** space (ASCII 32) in position zero.
*/
procedure encode_128_b:

    define input parameter it as character no-undo .

    define variable pos as integer no-undo .
    define variable apos as integer no-undo .
    define variable maxpos as integer no-undo .
    define variable thischar as character no-undo .
    define variable thecode as integer no-undo .
    
    assign
        pos = 1
        apos = 2
        maxpos = length(it)
        b_array[1] = {&C128_START_B} 
    .

    codeloop:
    do while pos le maxpos:

        assign
            thischar = substring( it, pos, 1 )
            thecode = asc(thischar) - 32
            b_array[apos] = thecode
            apos = apos + 1
            pos = pos + 1
        .
        
    end . /*codeloop*/

    b_array[apos] = -1 .

end procedure . /*encode_128_b*/




/*
** This encodes a package id, following UPS' intructions:
** Start with subset B, then encode as many digits as possible
** using subset C.
** Note: This procedure will work for proper UPS package ids, and
** may fail miserably for other things.
** The idea is to encode using as little space as possible a string
** which ends in digits. Subset C allows us to encode pairs of digits.
** We need to find the beginning of the longest sequence of trailing digits,
** then advance 1 if this is in a even position.
** After doing that, we encode the part before the position we found 
** using subset B,  the remainder using subset C.
*/
procedure encode_128_c:

    define input parameter it as character no-undo .
    
    define variable pos as integer no-undo .
    define variable pos2 as integer no-undo .
    define variable maxpos as integer no-undo .
    define variable thecode as integer no-undo .    
    define variable thischar as character no-undo .
        
    define variable last_b as integer no-undo .
          
    /* find the point where we shift from subset B to C */
    assign
        pos = 1
        maxpos = length( it )
    .
    
    findloop:         
    do while pos le maxpos: 
        /* we are searching backwards */
        assign
            thischar = substring( it, maxpos - pos + 1, 1 )
        .
        
        if ((thischar lt "0") or (thischar gt "9"))
        then do:
            leave findloop .
        end .

        assign pos = pos + 1 .
    end . /*findloop*/

    /* move back, so that we have the address of the last digit */
    assign pos = pos - 1 .

    /* found it, see that we have an even number of digits enclosed */
    if ((pos modulo 2) eq 1) then assign pos = pos - 1 .
                       
    /* now set things up for the real show */
    assign
        last_b = maxpos - pos     /*the last character for subset b*/
        pos = 1
    .

    
    /* encode the subset B part */
    if (last_b gt 0)
    then do:
        assign b_array[1] = {&C128_START_B} .
        
        bloop:         
        do while pos le last_b:
    
            assign
                thischar = substring( it, pos, 1 )
                thecode = asc(thischar) - 32
                b_array[pos + 1] = thecode
                pos = pos + 1
            .

        end . /*bloop*/                 
    
        assign
            b_array[pos + 1] = {&C128_SHIFT_TO_C}
            pos2 = pos
            pos = pos + 2
        .
    end .
    else do:
        assign
            b_array[1] = {&C128_START_C}
            pos2 = 1
            pos = 2
        .
    end .
    
    /* encode the subset c part */
    cloop:
    do while pos2 lt maxpos:
    
        assign
            thischar = substring( it, pos2, 2 )
            thecode = integer(thischar)
            b_array[pos] = thecode
            pos = pos + 1
            pos2 = pos2 + 2
        .        
    end .

    b_array[pos] = -1 .
    
end procedure . /*encode_128_c*/
