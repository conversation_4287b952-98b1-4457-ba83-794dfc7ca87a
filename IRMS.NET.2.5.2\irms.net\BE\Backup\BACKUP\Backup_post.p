/**=================================================================**
* Y:\BE_Area\src\be\Backup\Backup\Backup_post.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:42 PM
**=================================================================**/


/* Business Entity Definintions */
{Backup/Backup/Backup_ds.i}
{Backup/Backup/Backup_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsBackup .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN DO:
        CREATE ds_Control.
        ASSIGN ds_Control.PropName = 'COMMAND'
               ds_Control.PropValue = 'POST'.
    END.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


