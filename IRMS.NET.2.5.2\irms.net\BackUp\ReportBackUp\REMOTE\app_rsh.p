define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/app_rsh.p,v 1.1 1999/03/04 14:27:05 tanya Exp $~n"
.                                                                         
def input parameter ch_command as char  .

def var ch_line as char format 'X(600)' view-as editor size 60 by 10 .

assign
    ch_command = ch_command + ' > /tmp/log.txt ' 
    ch_line    =  ch_command .


output to /tmp/dump.txt .
put unformatted ch_command format 'X(600)' .
output close .
                                                                    
os-command value ( ch_command + '> /tmp/log.txt ')  .
