/*** 
** Location labels for Zebra printer .
** File name is hard coded in lbl_prnt.w .
***/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/zeb_locs.p,v 1.4 2000-02-24 10:42:26-06 tanya Exp $"
.

DEF INPUT PARAMETER i_arrow       AS INT   NO-UNDO .
DEF INPUT PARAMETER ch_format     AS CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_value_locs AS CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_printer    AS CHAR  NO-UNDO .
DEF INPUT PARAMETER i_type        AS INT   NO-UNDO .


DEF VAR i_num_ent AS INT  NO-UNDO        .
DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
DEF VAR ch_loc    AS CHAR NO-UNDO        .
DEF VAR ch_print  AS CHAR NO-UNDO        .
DEF VAR ch_file   AS CHAR NO-UNDO        .


MESSAGE "Starting Printing..." .


ASSIGN
   i_num_ent = NUM-ENTRIES ( ch_value_locs )
   .

IF i_num_ent = 0 THEN
DO:
   MESSAGE 'No location label list passed for printing...' .
   
   RETURN .
END.
      
IF ( ch_format = ? ) OR ERROR-STATUS:ERROR THEN
   ASSIGN
      ch_format = ''  . 


IF ch_printer = ? THEN
DO:
   MESSAGE 'No printer specified...' .
   RETURN .
END.

/***
**   Build print File 
***/

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LOC", ".txt", output ch_file ) .

DO WHILE i_num_ent >= i_entry :
 
   ASSIGN
      ch_loc  = ENTRY ( i_entry , ch_value_locs ) .
      i_entry = i_entry + 1 .                          
   MESSAGE 'Generating ' ch_loc  'Label...'.   
      
   RUN print_label ( ch_loc , ch_file ) .
END.

/***
**  Print the File
***/
message "About to print location labels:" ch_file .

if (opsys eq "UNIX")
then do:
    ASSIGN
        ch_print = "lp -d" + ch_printer + " " + ch_file .
    
    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .

    pause 5 no-message .

    OS-DELETE         VALUE ( ch_file    ) .
end .
else do: /*NT*/
    
   /************************* old code using spooler *******************
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        message "Copying" ch_file "to" ch_spool_dir .
        
        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .

   *********************************************************************/
   
    message "copying file  " ch_file "to printer " ch_printer .

    OS-COPY VALUE( ch_file) VALUE( ch_printer) .

    OS-DELETE  VALUE ( ch_file ) .   

end .

/* the end */
return .



/* <<<<<<<<<<<<<<<<<<<<<< PROCEDURES >>>>>>>>>>>>>>>>>>>>> */

PROCEDURE print_label:                         
    DEF INPUT PARAMETER ch_loc   AS CHAR NO-UNDO .
    DEF INPUT PARAMETER ch_file  AS CHAR NO-UNDO .

    DEF VAR             ch_value AS CHAR NO-UNDO .

    ASSIGN
       ch_loc = CAPS ( ch_loc ) 
       .
       
    RUN format_string ( ch_loc, OUTPUT ch_value ) .



    OUTPUT TO VALUE ( ch_file )  APPEND. 
    CASE i_type :
       WHEN 1 THEN   /* 1x4 Standard Label... */
       DO:
          PUT UNFORMATTED
            "^XA^CFD^LH1,1^FS"                  +
            "^FO0,202^GB798,0,4^FS"             +
            "^FO10,50^A0N,130,50"               +
            "^FD" + ch_value + "^FS"            +
            "^FO475,20^BY2^BCN,165,N,N,N"       +
            "^FD" + ch_loc + "^FS^XZ~n"
            .
       END .
              
       WHEN 2 THEN       
       DO:   /* 2x4 Narrow label ( 1x4 printed on 2x4 label size)*/
          PUT UNFORMATTED
            "^XA^CFD^LH1,1^FS"                  +
            "^FO0,202^GB798,0,4^FS"             +
            "^FO400,0^GB0,200,4^FS"             +
            "^FO10,50^A0N,130,55"               +
            "^FD" + ch_value + "^FS"            +
            "^FO450,20^BY2^BCN,150,N,N,N"       +
            "^FD" + ch_loc + "^FS^XZ~n"
            . 
       END.       
       
       WHEN 3 THEN
       DO:   /* 2X4 Standard Label*/

          PUT UNFORMATTED "^XA^CFD^FS" .
          
          CASE i_arrow:   /* ARROW UP*/
              WHEN 1 THEN
              DO:
                 PUT UNFORMATTED
                    "^FO716,30^GB2,0,2^FS^FO714,32^GB6,0,2^FS ~n"     +
                    "^FO712,34^GB10,0,2^FS^FO710,36^GB14,0,2^FS ~n"   +
                    "^FO708,38^GB18,0,2^FS^FO706,40^GB22,0,2^FS ~n"   +
                    "^FO704,42^GB26,0,2^FS^FO702,44^GB30,0,2^FS ~n"   +
                    "^FO700,46^GB34,0,2^FS^FO698,48^GB38,0,2^FS ~n"   +
                    "^FO696,50^GB42,0,2^FS^FO694,52^GB46,0,2^FS ~n"   +
                    "^FO692,54^GB50,0,2^FS^FO690,56^GB54,0,2^FS ~n"   +
                    "^FO688,58^GB58,0,2^FS^FO686,60^GB62,0,2^FS ~n"   +
                    "^FO684,62^GB66,0,2^FS^FO682,64^GB70,0,2^FS ~n"   +
                    "^FO680,66^GB74,0,2^FS^FO678,68^GB78,0,2^FS ~n"   +
                    "^FO676,70^GB82,0,2^FS^FO674,72^GB86,0,2^FS ~n"   +
                    "^FO672,74^GB90,0,2^FS^FO670,76^GB94,0,2^FS ~n"   +
                    "^FO668,78^GB98,0,2^FS^FO666,80^GB102,0,2^FS ~n"  +
                    "^FO664,82^GB106,0,2^FS^FO662,84^GB110,0,2^FS ~n" +
                    "^FO689,84^GB56,82,56^FS ~n" 
                    .
               END.
               
               
               WHEN 2 THEN   /* ARROW DOWN */
               DO:
                  PUT UNFORMATTED
                     "^FO716,166^GB2,0,2^FS^FO714,164^GB6,0,2^FS ~n"     +
                     "^FO712,162^GB10,0,2^FS^FO710,160^GB14,0,2^FS ~n"   +
                     "^FO708,158^GB18,0,2^FS^FO706,156^GB22,0,2^FS ~n"   +
                     "^FO704,154^GB26,0,2^FS^FO702,152^GB30,0,2^FS ~n"   +
                     "^FO700,150^GB34,0,2^FS^FO698,148^GB38,0,2^FS ~n"   +
                     "^FO696,146^GB42,0,2^FS^FO694,144^GB46,0,2^FS ~n"   +
                     "^FO692,142^GB50,0,2^FS^FO690,140^GB54,0,2^FS ~n"   +
                     "^FO688,138^GB58,0,2^FS^FO686,136^GB62,0,2^FS ~n"   +
                     "^FO684,134^GB66,0,2^FS^FO682,132^GB70,0,2^FS ~n"   +
                     "^FO680,130^GB74,0,2^FS^FO678,128^GB78,0,2^FS ~n"   +
                     "^FO676,126^GB82,0,2^FS^FO674,124^GB86,0,2^FS ~n"   +
                     "^FO672,122^GB90,0,2^FS^FO670,120^GB94,0,2^FS ~n"   +
                     "^FO668,118^GB98,0,2^FS^FO666,116^GB102,0,2^FS ~n"  +
                     "^FO664,114^GB106,0,2^FS^FO662,112^GB110,0,2^FS ~n" +
                     "^FO689,30^GB56,82,56^FS ~n".
                END.
            END CASE .

            PUT UNFORMATTED       /*PRINT LOCATION AND BAR CODE */
               "^FO0,202^GB798,0,4^FS^FO625,0^GB0,202,4^FS ~n" +
               "^FO10,15^A0N,40,40^FDLOCATION:^FS ~n"
               "^FO20,80^A0N,90,70"
               "^FD" + ch_value + "^FS ~n"
               "^FO100,230^BY4^BCN,150,N,N,N^FD" + 
               ch_loc + "^FS" +   "^XZ~n".

         END.
      WHEN 4 THEN  /* 4x6 Standard Label */
      DO:
         PUT UNFORMATTED "^XA^CFD^FS" .

            CASE i_arrow :
               WHEN 1 THEN     /* ARROW UP */

                  PUT UNFORMATTED
                     "^FO80,174^GB0,2,2^FS^FO82,172^GB0,6,2^FS"           +
                     "^FO84,170^GB0,10,2^FS^FO86,168^GB0,14,2^FS"         +
                     "^FO88,166^GB0,18,2^FS^FO90,164^GB0,22,2^FS"         +
                     "^FO92,162^GB0,26,2^FS^FO94,160^GB0,30,2^FS"         +
                     "^FO96,158^GB0,34,2^FS^FO98,156^GB0,38,2^FS"         +
                     "^FO100,154^GB0,42,2^FS^FO102,152^GB0,46,2^FS"       +
                     "^FO104,150^GB0,50,2^FS^FO106,148^GB0,54,2^FS"       +
                     "^FO108,146^GB0,58,2^FS^FO110,144^GB0,62,2^FS"       +
                     "^FO112,142^GB0,66,2^FS^FO114,140^GB0,70,2^FS"       +
                     "^FO116,138^GB0,74,2^FS^FO118,136^GB0,78,2^FS"       +
                     "^FO120,134^GB0,82,2^FS^FO122,132^GB0,86,2^FS"       +
                     "^FO124,130^GB0,90,2^FS^FO126,128^GB0,94,2^FS"       +
                     "^FO128,126^GB0,98,2^FS^FO130,124^GB0,102,2^FS"      +
                     "^FO132,122^GB0,106,2^FS^FO134,120^GB0,110,2^FS"     +
                     "^FO136,118^GB0,114,2^FS^FO138,116^GB0,118,2^FS"     +
                     "^FO140,114^GB0,122,2^FS^FO142,112^GB0,126,2^FS"     +
                     "^FO144,110^GB0,130,2^FS^FO146,108^GB0,134,2^FS"     +
                     "^FO148,106^GB0,138,2^FS^FO150,104^GB0,142,2^FS"     +
                     "^FO152,102^GB0,146,2^FS^FO154,100^GB0,150,2^FS"     +
                     "^FO156,98^GB0,154,2^FS^FO158,96^GB0,158,2^FS"       +
                     "^FO160,94^GB0,162,2^FS^FO162,92^GB0,166,2^FS"       +
                     "^FO164,90^GB0,170,2^FS^FO166,88^GB0,174,2^FS"       +
                     "^FO168,86^GB0,178,2^FS^FO170,84^GB0,182,2^FS"       +
                     "^FO172,82^GB0,186,2^FS^FO174,80^GB0,190,2^FS"       +
                     "^FO174,127^GB140,0,96^FS" .                         

               WHEN 2 THEN      /*ARROW DOWN*/

                  PUT UNFORMATTED
                     "^FO314,174^GB0,2,2^FS^FO312,172^GB0,6,2^FS"      +
                     "^FO310,170^GB0,10,2^FS^FO308,168^GB0,14,2^FS"    +
                     "^FO306,166^GB0,18,2^FS^FO304,164^GB0,22,2^FS"    +
                     "^FO302,162^GB0,26,2^FS^FO300,160^GB0,30,2^FS"    +
                     "^FO298,158^GB0,34,2^FS^FO296,156^GB0,38,2^FS"    +
                     "^FO294,154^GB0,42,2^FS^FO292,152^GB0,46,2^FS"    +
                     "^FO290,150^GB0,50,2^FS^FO288,148^GB0,54,2^FS"    +
                     "^FO286,146^GB0,58,2^FS^FO284,144^GB0,62,2^FS"    +
                     "^FO282,142^GB0,66,2^FS^FO280,140^GB0,70,2^FS"    +
                     "^FO278,138^GB0,74,2^FS^FO276,136^GB0,78,2^FS"    +
                     "^FO274,134^GB0,82,2^FS^FO272,132^GB0,86,2^FS"    +
                     "^FO270,130^GB0,90,2^FS^FO268,128^GB0,94,2^FS"    +
                     "^FO266,126^GB0,98,2^FS^FO264,124^GB0,102,2^FS"   +
                     "^FO262,122^GB0,106,2^FS^FO260,120^GB0,110,2^FS"  +
                     "^FO258,118^GB0,114,2^FS^FO256,116^GB0,118,2^FS"  +
                     "^FO254,114^GB0,122,2^FS^FO252,112^GB0,126,2^FS"  +
                     "^FO250,110^GB0,130,2^FS^FO248,108^GB0,134,2^FS"  +
                     "^FO246,106^GB0,138,2^FS^FO244,104^GB0,142,2^FS"  +
                     "^FO242,102^GB0,146,2^FS^FO240,100^GB0,150,2^FS"  +
                     "^FO238,98^GB0,154,2^FS^FO236,96^GB0,158,2^FS"    +
                     "^FO234,94^GB0,162,2^FS^FO232,92^GB0,166,2^FS"    +
                     "^FO230,90^GB0,170,2^FS^FO228,88^GB0,174,2^FS"    +
                     "^FO226,86^GB0,178,2^FS^FO224,84^GB0,182,2^FS"    +
                     "^FO222,82^GB0,186,2^FS^FO220,80^GB0,190,2^FS"    +
                     "^FO80,127^GB140,0,96^FS".

            END CASE .
          
            /* PRINT LOCATION AND BAR CODE */
                  
          PUT UNFORMATTED
          "^FO400,0^GB3,1400,3^FS"
          "^FO0,350^GB400,0,3^FS"
          "^FO20,890^A0B,70,70^FDLOCATION:^FS"
          "^FO160,380^A0B,150,100"
          "^FD" +  ch_value + "^FS"
          "^FO125,750^A0B,275,200"
          "^FO425,100^BY7,1,1^BCB,350,N,N,Y"
          "^FD" + ch_loc + "^FS"
          "^XZ".


      END.   
   END CASE .


END PROCEDURE.


PROCEDURE format_string :
/* -----------------------------------------------------------
  Purpose:    Format a location string ...
  Parameters: Old Location (in) New formated Location (out)
  Notes:
-------------------------------------------------------------*/
 DEF INPUT  PARAMETER ch_loc    AS CHAR NO-UNDO.
 DEF OUTPUT PARAMETER ch_newloc AS CHAR NO-UNDO .
     
 DEF VAR ch_build    AS CHAR NO-UNDO .
 DEF VAR i_charpos   AS INT  NO-UNDO .
 DEF VAR i_numdashes AS INT  NO-UNDO .
 DEF VAR i_length    AS INT  NO-UNDO .
 DEF VAR i_count     AS INT  NO-UNDO .
                         
   ASSIGN
      i_numdashes = NUM-ENTRIES ( ch_format )
      ch_newloc   = ch_loc          
      .
      
   IF i_numdashes < 1 THEN
      RETURN .
               
   buildloop:
   REPEAT WHILE i_count < i_numdashes :
                     
      ASSIGN
         i_count   = i_count + 1
         i_length  = LENGTH ( ch_newloc )
         i_charpos = INT ( ENTRY (  i_count , ch_format ) )
         NO-ERROR .
    
         IF ERROR-STATUS:ERROR THEN
         DO:
            MESSAGE "Problem with format string.  Please validate the format" +              "string in system parameters."
            .
            RETURN .
         END .
                                         
         IF i_charpos > i_length THEN
            NEXT buildloop .
                                                        
         ASSIGN
            ch_newloc = SUBSTRING ( ch_newloc, 1, i_charpos - 1 ) + '-' +
            SUBSTRING ( ch_newloc, i_charpos )
            .
      END.
                                             
END PROCEDURE.
                                                                  
