/**=================================================================**
* S:\IRMS.NET.2.6.0\irms.net\BE\AssignRMA\SearchItem\SearchItem_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 07/11/08, 09:56 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE ttorddtlSrch NO-UNDO
    BEFORE-TABLE ttorddtlSrch_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   abs_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Item Number"
                                                 LABEL "Item Number"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the company-wide unique code for this item.|no|yes"
    FIELD   act_qty                          AS  DECIMAL             
                                                 COLUMN-LABEL "Actual Qty"
                                                 LABEL "Actual Quantity"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Actual quantity picked for this order line.|no|yes"
    FIELD   assigned                         AS  LOGICAL             
                                                 COLUMN-LABEL "Allocated"
                                                 LABEL "Allocated"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the Allocated|no|yes"
    FIELD   bin_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Location"
                                                 LABEL "Location"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Location where this order line will be picked.|no|yes"
    FIELD   charges                          AS  DECIMAL             
                                                 COLUMN-LABEL "Item Price"
                                                 LABEL "Item Price"
                                                 FORMAT "$ >>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Price charged for this item.|no|yes"
    FIELD   comment                          AS  CHARACTER           
                                                 COLUMN-LABEL "Comment"
                                                 LABEL "Comment"
                                                 FORMAT "X(30)"
                                                 INIT ""
                                                 HELP "Enter the comment|no|yes"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the company number.|no|yes"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "Custom Fields"
                                                 LABEL "Custom Fields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Custom Fields|no|no"
    FIELD   discount                         AS  DECIMAL             
                                                 COLUMN-LABEL "Item Discount"
                                                 LABEL "Item Discount"
                                                 FORMAT ">>9.<<%"
                                                 INIT 0
                                                 HELP "Item discount.|no|yes"
    FIELD   DiscountAmt                      AS  DECIMAL             
                                                 COLUMN-LABEL "Discount Amt"
                                                 LABEL "Discount Amt"
                                                 FORMAT "$->>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Discount Amt|no|yes"
    FIELD   drop_cube                        AS  DECIMAL             
                                                 COLUMN-LABEL "Drop Cube"
                                                 LABEL "Drop Cube"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the drop_cube|no|yes"
    FIELD   drop_weight                      AS  DECIMAL             
                                                 COLUMN-LABEL "Drop Weight"
                                                 LABEL "Drop Weight"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Weight calculated at order drop time.|no|yes"
    FIELD   ExtdPrice                        AS  DECIMAL             
                                                 COLUMN-LABEL "Extd Price"
                                                 LABEL "Extd Price"
                                                 FORMAT "$>>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the ExtdPrice|no|yes"
    FIELD   filler_flag                      AS  LOGICAL             
                                                 COLUMN-LABEL "Use as filler?"
                                                 LABEL "Use as filler?"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Use as filler?|no|yes"
    FIELD   fl_zone                          AS  CHARACTER           
                                                 COLUMN-LABEL "Full Case Location"
                                                 LABEL "Full Case Location"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "The Full Case Location for this order line.|no|yes"
    FIELD   gift_wrap                        AS  LOGICAL             
                                                 COLUMN-LABEL "Gift Wrap"
                                                 LABEL "Gift Wrap"
                                                 FORMAT "yes/no"
                                                 HELP "Gift wrap this line?|no|yes"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID|no|no"
    FIELD   GUID1                            AS  DECIMAL             
                                                 COLUMN-LABEL "GUID-ordhdr"
                                                 LABEL "GUID-ordhdr"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID-ordhdr|no|no"
    FIELD   host_origin                      AS  CHARACTER           
                                                 COLUMN-LABEL "Host Origin"
                                                 LABEL "Host Origin"
                                                 FORMAT "X(25)"
                                                 INIT ""
                                                 HELP "Enter the Host Origin|no|yes"
    FIELD   id                               AS  INTEGER             
                                                 COLUMN-LABEL "Id"
                                                 LABEL "Id"
                                                 FORMAT ">>>>>>9"
                                                 INIT 0
                                                 HELP "DO NOT EDIT THIS FIELD.|no|yes"
    FIELD   item_desc                        AS  CHARACTER           
                                                 COLUMN-LABEL "Item Description"
                                                 LABEL "Item Description"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter a description for this item.|no|yes"
    FIELD   line                             AS  INTEGER             
                                                 COLUMN-LABEL "Line"
                                                 LABEL "Line"
                                                 FORMAT ">>>9"
                                                 INIT 1
                                                 HELP "Order Line number.|no|yes"
    FIELD   line_alt_number                  AS  INTEGER             
                                                 COLUMN-LABEL "Alt Line Num"
                                                 LABEL "Alt Line Num"
                                                 FORMAT ">>9"
                                                 INIT 0
                                                 HELP "R&D field|no|no"
    FIELD   Line_sequence                    AS  INTEGER             
                                                 COLUMN-LABEL "Line Sequence"
                                                 LABEL "Line Sequence"
                                                 FORMAT ">9"
                                                 INIT 0
                                                 HELP "Order Line Sequence|no|yes"
    FIELD   line_status                      AS  CHARACTER           
                                                 COLUMN-LABEL "Status"
                                                 LABEL "Status"
                                                 FORMAT "x(12)"
                                                 INIT "O"
                                                 HELP "Enter the status for this line.|no|yes"
    FIELD   lot                              AS  CHARACTER           
                                                 COLUMN-LABEL "Lot"
                                                 LABEL "Lot"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Lot Number.|no|yes"
    FIELD   msds_employee                    AS  CHARACTER           
                                                 COLUMN-LABEL "MSDS Employee"
                                                 LABEL "MSDS Employee"
                                                 FORMAT "x(5)"
                                                 INIT ""
                                                 HELP "Employee who handled this MSDS sheet.|no|yes"
    FIELD   msds_packed                      AS  LOGICAL             
                                                 COLUMN-LABEL "MSDS Packed"
                                                 LABEL "MSDS Packed"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "MSDS sheet packed?|no|yes"
    FIELD   msds_required                    AS  LOGICAL             
                                                 COLUMN-LABEL "MSDS Required"
                                                 LABEL "MSDS Required"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the MSDS Required|no|yes"
    FIELD   msds_sheet                       AS  CHARACTER           
                                                 COLUMN-LABEL "MSDS Sheet"
                                                 LABEL "MSDS Sheet"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the MSDS Sheet|no|yes"
    FIELD   ordered_qty                      AS  DECIMAL             
                                                 COLUMN-LABEL "Quantity Ordered"
                                                 LABEL "Quantity Ordered"
                                                 FORMAT "->>,>>9.99"
                                                 HELP "Quantity ordered.|no|yes"
    FIELD   order_alt_num                    AS  INTEGER             
                                                 COLUMN-LABEL "order_alt_num"
                                                 LABEL "order_alt_num"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "R&D field|no|no"
    FIELD   order_alt_suf                    AS  INTEGER             
                                                 COLUMN-LABEL "order_alt_suf"
                                                 LABEL "order_alt_suf"
                                                 FORMAT ">9"
                                                 INIT 0
                                                 HELP "R&D field|no|no"
    FIELD   orig_cube                        AS  DECIMAL             
                                                 COLUMN-LABEL "Original Cube"
                                                 LABEL "Original Cube"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the orig_cube|no|yes"
    FIELD   orig_req_qty                     AS  DECIMAL             
                                                 COLUMN-LABEL "Original Quantity"
                                                 LABEL "Original Quantity"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Quantity originally requested.|no|yes"
    FIELD   orig_weight                      AS  DECIMAL             
                                                 COLUMN-LABEL "Orig. Calc. Weight"
                                                 LABEL "Orig. Calc. Weight"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Original Calculated Weight|no|yes"
    FIELD   package_code                     AS  CHARACTER           
                                                 COLUMN-LABEL "Package Code"
                                                 LABEL "Package Code"
                                                 FORMAT "X"
                                                 INIT "B"
                                                 HELP "Enter the Package Code|no|yes"
    FIELD   pick_line                        AS  LOGICAL             
                                                 COLUMN-LABEL "Pick Line?"
                                                 LABEL "Pick Line?"
                                                 FORMAT "yes/no"
                                                 INIT yes
                                                 HELP "Pick this order line?|no|yes"
    FIELD   pool                             AS  CHARACTER           
                                                 COLUMN-LABEL "Pool"
                                                 LABEL "Pool"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Pool|no|yes"
    FIELD   po_line                          AS  INTEGER             
                                                 COLUMN-LABEL "PO Line"
                                                 LABEL "PO Line"
                                                 FORMAT ">>>9"
                                                 HELP "Enter the po_line|no|yes"
    FIELD   po_line_sequence                 AS  INTEGER             
                                                 COLUMN-LABEL "PO Line Seq"
                                                 LABEL "PO Line Seq"
                                                 FORMAT ">9"
                                                 HELP "Enter the po_line_sequence|no|yes"
    FIELD   po_number                        AS  CHARACTER           
                                                 COLUMN-LABEL "PO Number"
                                                 LABEL "PO Number"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the PO Number|no|yes"
    FIELD   po_suffix                        AS  CHARACTER           
                                                 COLUMN-LABEL "PO Suffix"
                                                 LABEL "PO Suffix"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Enter the PO Suffix|no|yes"
    FIELD   req_emp                          AS  CHARACTER           
                                                 COLUMN-LABEL "Requesting Emp"
                                                 LABEL "Requesting Emp"
                                                 FORMAT "x(6)"
                                                 INIT ""
                                                 HELP "Employee performing a requisition|no|yes"
    FIELD   req_qty                          AS  DECIMAL             
                                                 COLUMN-LABEL "Requested Qty"
                                                 LABEL "Requested Quantity"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Requested Quantity for this order line.|no|yes"
    FIELD   ret_qty                          AS  DECIMAL             
                                                 COLUMN-LABEL "Quantity Returned"
                                                 LABEL "Quantity Returned"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Quantity returned for this order line|no|yes"
    FIELD   rt_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "RT Number"
                                                 LABEL "RT Number"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "RT id|no|yes"
    FIELD   same_lot                         AS  LOGICAL             
                                                 COLUMN-LABEL "Same Lot"
                                                 LABEL "Same Lot"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Force picking all quantity from the same lot.|no|yes"
    FIELD   serialflag                       AS  LOGICAL             
                                                 COLUMN-LABEL "Serialized"
                                                 LABEL "Serialized"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Serialized|no|yes"
    FIELD   serial_num                       AS  CHARACTER           
                                                 COLUMN-LABEL "Serial Number"
                                                 LABEL "Serial Number"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "The serial number|no|yes"
    FIELD   ship_cube                        AS  DECIMAL             
                                                 COLUMN-LABEL "Ship Cube"
                                                 LABEL "Ship Cube"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the ship_cube|no|yes"
    FIELD   ship_weight                      AS  DECIMAL             
                                                 COLUMN-LABEL "Ship Weight"
                                                 LABEL "Ship Weight"
                                                 FORMAT "->>,>>9.99"
                                                 INIT 0
                                                 HELP "Weight calculated or input at ship time.|no|yes"
    FIELD   stock_stat                       AS  CHARACTER           
                                                 COLUMN-LABEL "Stock Status"
                                                 LABEL "Stock Status"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "The status of this stock item.|no|yes"
    FIELD   tax                              AS  DECIMAL             
                                                 COLUMN-LABEL "Tax"
                                                 LABEL "Tax"
                                                 FORMAT "$ >>>,>>9.99"
                                                 INIT 0
                                                 HELP "Tax for this order line.|no|yes"
    FIELD   vendor_id                        AS  CHARACTER           
                                                 COLUMN-LABEL "Vendor ID"
                                                 LABEL "Vendor ID"
                                                 FORMAT "X(9)"
                                                 INIT ""
                                                 HELP "Vendor ID|no|yes"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse Number.|no|yes"
    FIELD   work_center                      AS  CHARACTER           
                                                 COLUMN-LABEL "Work Center"
                                                 LABEL "Work Center"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Work Center|no|yes"

                    .
TEMP-TABLE ttorddtlSrch:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsSearchItem
    FOR ttorddtlSrch,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


