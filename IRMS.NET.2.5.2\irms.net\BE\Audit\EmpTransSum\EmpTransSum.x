"ttTablePropsCREATE"
"ttTablePropsTblTableNamett_EmpSum"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKey"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamett_EmpSum"
"ttFieldPropsFldNametr_count"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat>,>>9"
"ttFieldPropsFldSideLabelNumber Of Trans."
"ttFieldPropsFldColLabelNumber Of Trans."
"ttFieldPropsFldHelpEnter the Number Of Trans."
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamett_EmpSum"
"ttFieldPropsFldNametr_date"
"ttFieldPropsFldDataTypeDate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelDate"
"ttFieldPropsFldColLabelDate"
"ttFieldPropsFldHelpEnter the Date"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamett_EmpSum"
"ttFieldPropsFldNametr_Name"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelTransaction Name"
"ttFieldPropsFldColLabelTransaction Name"
"ttFieldPropsFldHelpEnter the Transaction Name"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamett_EmpSum"
"ttFieldPropsFldNametr_Type"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(5)"
"ttFieldPropsFldSideLabelTransaction Type"
"ttFieldPropsFldColLabelTransaction Type"
"ttFieldPropsFldHelpEnter the Transaction Type"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameY:\BE_Area\src\blp\EmpTransSum_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextyes"
"ttOptionsmakePrevyes"
"ttOptionsmakeLastyes"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionno"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"