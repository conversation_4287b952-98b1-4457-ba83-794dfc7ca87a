/**
*** file name --- ctn_run.p               
**/
/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /irms/9.03/Custom/Overtons/remote/ctn_run.p 2     1/12/01 11:47a Dave $~n"
.
    define input parameter ch_co             as character no-undo .
    define input parameter ch_wh             as character no-undo .
    define input parameter i_wave            as integer   no-undo .
    define input parameter ord_id            as integer   no-undo .
    define input parameter ch_unuse          as character no-undo .
    define input parameter ch_queue          as character no-undo .
        
/** buffer defination **/
define buffer bf_ord for irms.ordhdr.
define buffer bf_wh  for irms.whmst . 

DEF VAR i_total_cartons AS INT NO-UNDO .

    if i_wave EQ ? OR i_wave = 0
    then do:
        find first bf_ord no-lock 
            where 
                bf_ord.id = ord_id 
            no-error
        .
        if available (bf_ord) 
        then do:
            assign i_wave = bf_ord.batch .
        end .
    end .

/*** Cartonization algorithm ***/
   RUN ctn_algo.p (i_wave, ch_co, ch_wh, 999, NO, NO, OUTPUT i_total_cartons).

/*** Overtons Form...  ***/
   RUN ctn_ovt.p ( i_wave, ch_co , ch_wh ) .

   RUN ctn_prnt.p ( i_wave, ch_co, ch_wh, ch_queue ) .
/*** END ****/

