/*
** glauber Thu 31aug95 14:47
** lprintor.p -- printer driver for LaserJet compatible
*/
/*
** Mod history:
**
** glauber 5sep97
** Added maxicode procedures.
** Assumes:
**   NeoMedia maxicode loaded into Progress via HLC
**   Maxicode font loaded in the printer with font id of {&MAXICODE_FONT_ID}
**   The vertical spacing required by the font is {&MAXICODE_FONT_DOTS}/300"
**
** Note:
**   (Define NO_MAXICODE as true to test if the library is missing.)
**   Since i'm using HLC for the maxicode anyway, i'm also using the
**      HLC version of the ANSI 128 barcode.
** 
** 24Sep98 07:36 glauber
** The new version of Neomedia's maxicode library (libencoder.a)
** has a different syntax from the older version which we first used
** to implement Maxicode at Dart. The older version is in the files in
** the /pdb/8.02/rf directory. The files in the /pdb/9.01 directory
** will not be backwards compatible. If we implement IRMS 9 at
** Dart, we should upgrade their Maxicode library too.
*/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/lprintor.p,v 1.9 2001/04/30 14:39:56 wayneb Exp $~n"
.


/* <<<<<<<<<< constants >>>>>>>>>> */

/*
** Define NO_MAXICODE as False
** to enable the HLC maxicode
**
** 23Sep98 19:18 glauber
** If you set NO_MAXICODE to false,
*/
&Scoped-Define NO_MAXICODE true /* false */ /* WRB20010409 - true */
&Scoped-Define MAXICODE_FONT_ID 300 /* WRB20010419 - 7 */
&Scoped-Define MAXICODE_FONT_DOTS 18

/* globals */
define shared variable apage as character no-undo .

/* other variables */
define variable barcode as handle no-undo .
define variable bartype as character no-undo initial "C" .


/* initialization */

&If {&NO_MAXICODE}
&Then
    run lj128_4g.p persistent set barcode .
&Else
    run ljdot.p persistent set barcode .  /* WRB20010410 - Changed lj128 to ljdot */
&EndIf

return .




/* cleanup */
procedure destroy:

    run destroy in barcode .
    delete procedure this-procedure .

end .


/* <<<<<<<<<< FROM HERE ON, STUFF THAT GOES TO PRINTER >>>>>>>>>> */

procedure reset:

    assign apage = apage + "~EE~E*t300R" .

end procedure . /*reset*/


procedure setform:

    /* 7 inches */
    assign apage = apage +
        "~E&l352A"
    .

end procedure . /*setform*/


procedure fixedlineprinter :

    assign
        apage = apage +
                "~E(10U~E(s0p16.67h8.5v0s0b0T"
    .

end procedure .

procedure fixedromanfont:
    define input parameter pointsize as decimal no-undo .
    define input parameter pitch as decimal no-undo .

    /* Works with pointsize 10.00 | 12.00 |
                pitch     12.0  | 10.0  | */

    assign apage = apage +
        "~E(8U~E(s0p" +
        string(pointsize) +
        "h" +
        string(pitch) +
        "v0s0b3T"
    .

end procedure . 


procedure romanfont:

    define input parameter pointsize as integer no-undo .

    assign apage = apage +
        "~E(8U~E(s1p" +
        string(pointsize) +
        "v0s0b4101T"
    .

end procedure . /*romanfont*/


procedure universfont:

    define input parameter pointsize as integer no-undo .

    assign apage = apage +
        "~E(8U~E(s1p" +
        string(pointsize) +
        "v0s0b4148T"
    .

end procedure . /*universfont*/


procedure typewriterfont:

    assign apage = apage +
        "~E(10U~E(s0p16.67h8.5v0s0b0T"
    .

end procedure . /*typewriterfont*/


procedure setfont:
    define input parameter pointsize as integer no-undo .

    run universfont( pointsize ) .
end procedure . /*setfont*/


procedure remarkfont:
    run setfont( 10 ) .
end procedure . /*remarkfont*/

procedure servicefont:
    run universfont( 16 ) . /*specifically Univers*/
end procedure . /*servicefont*/

procedure cluefont:
    run universfont( 24 ) . /*specifically Univers*/
end procedure . /*cluefont*/

procedure trackidfont:
    run universfont( 11 ) . /*specifically Univers*/
end procedure . /*trackidfont*/

procedure ups_barcode_font:
   run universfont( 12 ) .
end procedure .


procedure littlefont:
    run setfont( 8 ) .
end procedure . /*littlefont*/


procedure addressfont:
    run setfont( 11 ) .
end procedure . /*addressfont*/


procedure zipfont:
    run setfont( 14 ) .
end procedure . /*zipfont*/


/* THIS IS SPECIFIC TO OT LASERMATRIX PRINTERS */
procedure setfuser:
    define input parameter fusermode as integer no-undo .

    assign
        apage = apage + "~E~001~002OTCF" +
                string(fusermode, "9") + "~003"
    .
    
end procedure . /*setfuser*/



/* THIS IS SPECIFIC TO OT LASERMATRIX PRINTERS */
procedure setstop:

    define input parameter stopmode as integer no-undo .

    assign
        apage = apage +
            "~E~001~002OTCP" +
            string(stopmode, "9") + "~003"
    .

end procedure . /*setstop*/


/* THIS IS SPECIFIC TO THE OT LASERMATRIX */
procedure setvertcorr:

    define input parameter corrmode as integer no-undo .

    assign apage =
        apage + "~E~001~002OTCV" +
        string(corrmode, "9") + "~003"
    .

end procedure . /*setvertcorr*/


procedure initialize:

    run reset .
    run setfuser( 3 ) .
    run setstop( 2 ) .
    run setvertcorr( 1 ) .
    run setform .

    /* select font, just in case */
    run littlefont .
    
end procedure . /*initialize*/

procedure setbar:
   define input parameter d_height as decimal no-undo .
   define input parameter d_width as decimal no-undo .

   run 128_set_heigth in barcode( d_height ) .
   run 128_set_width  in barcode( d_width  ) .
END PROCEDURE .


procedure setbar_ups:
    
    run 128_set_heigth in barcode( 1 ) .
    run 128_set_width in barcode( 18 ) .

end procedure . /*setbar_ups*/

procedure setbar_carton:

    run 128_set_heigth in barcode(0.20).
    run 128_set_width in barcode(12) .

end procedure .



procedure setbar_carton_large:

    run 128_set_heigth in barcode(0.50).
    run 128_set_width in barcode(18) .

end procedure .


procedure setbar_order:

    run 128_set_heigth in barcode( 0.20 ) .
    run 128_set_width in barcode( 11 ) .

end procedure . /*setbar_order*/

/*for larger order barcode alpha shirt */
procedure setbar_order_large:

    run 128_set_heigth in barcode( 0.40 ) .
    run 128_set_width in barcode( 18.0 ) .

end procedure . /*setbar_order*/

procedure formfeed:

    assign apage = apage +
        "~E&l0H~n"
    .

end procedure . /*formfeed*/


procedure printgoto:

    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .

    define variable xdp as integer no-undo .
    define variable ydp as integer no-undo .

    assign
        xdp = xin * 300
        ydp = yin * 300
    .

    assign apage = apage +
        "~E*p" + string(ydp) + "Y" +
        "~E*p" + string(xdp) + "X"
    .
        

end procedure . /*printgoto*/



procedure printat:

    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .
    define input parameter it as character no-undo .

    if (it eq ?) then assign it = "" .
    run printgoto( xin, yin ) .
    assign apage = apage + it .

end procedure . /*printat*/


/*
** Simplified maxicode call.
** Assumes:
**   United States address.
**   The zip code contains digits only.
**
** 23Sep98 19:28 glauber:
** The new version of the maxicode encoder has a
** UPS-specific encoding, and the parameters have
** changed. So, i'm getting rid of the maxicodeat
** procedure, and putting everything in here at
** upsmaxicodeat, hoping that this won't break any
** existing code...
*/
procedure upsmaxicodeat:
    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .
    define input parameter ch_zip as character no-undo .
    define input parameter ch_ups_1z as character no-undo .
    /* new parameters */
    define input parameter i_package_number as integer no-undo .
    define input parameter i_package_count as integer no-undo .
    define input parameter d_weight as decimal no-undo .
    define input parameter i_julian_day as integer no-undo .
    define input parameter ch_irms_tracking as character no-undo .
    define input parameter ch_ship_city as character no-undo .
    define input parameter ch_ship_state as character no-undo .

    define variable i_country as integer no-undo .
    define variable i_service as integer no-undo .
    define variable ch_tracking as character no-undo .
    define variable ch_shipper as character no-undo .
    define variable ch_maxicode as character no-undo .
    define variable xdp as decimal no-undo .
    define variable ydp as decimal no-undo .
    define variable i_line as integer no-undo .
    define variable i_numlines as integer no-undo .



&If {&NO_MAXICODE}
&Then

    run setfont( input 8 ) .

    run printat( xin - 0.1, yin, "+" ) .
    run printat( xin + 1, yin, "+" ) .
    run printat( xin - 0.1, yin + 1, "+" ) .
    run printat( xin + 1, yin + 1, "+" ) .
    run printat( xin + 0.25, yin + 0.55, "Maxicode" ) .

        
&Else

    /* make sure the zip code has 9 digits */
    assign
        ch_zip = substring( ch_zip, 1, 9 )
        ch_zip = ch_zip + fill( "0", (9 - length(ch_zip)) )
    .

    /* first the easy ones */
    assign
        i_country = 840
    .

    /*
    ** Parse the ups "1Z" information
    ** 123456789012345678
    ** 1Ziiiiiisstttttttt
    ** i == shipper id
    ** s == service level
    ** t == tracking id including checkdigit
    */
    assign
        ch_shipper = substring( ch_ups_1z, 3, 6 )
        ch_tracking = "1Z" + substring( ch_ups_1z, 11, 8 )
    .
    assign
        i_service = 0
        i_service = integer(substring( ch_ups_1z, 9, 2 ))
        no-error
    .

    /* package number and package count... */
    if (
        (i_package_count gt 0) and
        (i_package_number gt 0) and
        (i_package_count lt 1000) and
        (i_package_number lt 1000) and
        (i_package_number le i_package_count)
       )
    then /*ok*/ .
    else do:
        assign
            i_package_count = 1
            i_package_number = 1
        .
    end .

    /* Julian Day (if set to zero, the C routine will set to TODAY) */
    if (
        (i_julian_day gt 0) and
        (i_julian_day le 356)
    )
    then /*ok*/ .
    else assign i_julian_day = 0 .

    /* IRMS carton id or order number */
    if (ch_irms_tracking gt ?)
    then /*ok*/ .
    else assign ch_irms_tracking = ch_ups_1z .

    /* Ship-to City */
    if (ch_ship_city eq ?)
    then assign ch_ship_city = "" .

    /* Ship-to State */
    if (length(ch_ship_state) eq 2)
    then /*ok*/ .
    else assign ch_ship_state = "" .

    
    run maxicode in barcode( input ch_zip,
                             input i_country,
                             input i_service,
                             input ch_tracking,
                             input ch_shipper,
                             input i_package_number,
                             input i_package_count,
                             input integer(d_weight),
                             input i_julian_day,
                             input ch_irms_tracking,
                             input ch_ship_city,
                             input ch_ship_state,
                             output ch_maxicode ) .

    message "CH_MAXICODE " ch_maxicode.

    assign
        xdp = xin * 300
        ydp = yin * 300
        i_line = 1
        i_numlines = num-entries( ch_maxicode, "~n" )
    .

    if (i_numlines gt 1)
    then do:

        assign apage = apage +
            /* select the maxicode font */
            "~E({&MAXICODE_FONT_ID}X"
        .

        do while i_line le i_numlines:
            assign
                apage = apage +
                "~E*p" + string(ydp) + "Y" +
                "~E*p" + string(xdp) + "X" +
                entry( i_line, ch_maxicode, "~n" )
                
                ydp = ydp + {&MAXICODE_FONT_DOTS}
                i_line = i_line + 1
            .
        end .
    end .
    else do:
        /* message will appear in appserver.log */
        message "Maxicode returned:" ch_maxicode "for carton" ch_ups_1z .
    end .
&EndIf
        
end procedure . /*upsmaxicodeat*/


procedure barat:

    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .
    define input parameter it as character no-undo .
    
    define variable barcmd as character no-undo .

    /*
    ** Avoid empty barcode, and also
    ** avoid ? (unknown value).
    */
    if length(it) gt 0
    then /*ok*/ .
    else return .

    run 128_bar in barcode( xin, yin, it, bartype, output barcmd ) .

    assign apage = apage + barcmd .

end procedure . /*barat*/


procedure title_page:
    define input parameter ordercount as integer no-undo .
    define input parameter labelcount as integer no-undo .
    define input parameter thebatch as integer no-undo .
    define input parameter tstring as character no-undo .

    define variable workstring as character no-undo .

    assign workstring = "BATCH " + string(thebatch) .

    run universfont( 20 ) .
    run printat( 0.75, 0.7, workstring ) .

    assign workstring = string(ordercount) + " orders, " +
                        string(labelcount) + " labels." .

    run printat( 0.75, 1.1, workstring ) .

    run printat( 0.75, 1.5, tstring ) .

    run littlefont .

    run formfeed .

end procedure . /*title_page*/


/*
** Jian 12feb99
** Draw a box with lefttop position , width and hight            
**
** Parameters:
** Xin: X position of lefttop in inches
** Yin: Y position of lefttop in inches
** len: Length of box in inches
** wid: width  of box in inches
*/

procedure draw_box :   
    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .
    define input parameter len as decimal no-undo . 
    define input parameter wid as decimal no-undo . 
    
    run line (xin , yin , len , 0.015) no-error . 
    
    run line (xin , yin , 0.015 , wid) no-error . 
    
    run line ((xin + len) , yin , 0.015 , (wid + 0.015)) no-error . 
    
    run line (xin , (yin + wid ) , (len + 0.015) , 0.015) no-error . 

end procedure .    /* end of draw_box */


/*
** Glauber/Jian 10feb99
** Print the UPS "service icon" (big white number in black box)
**
** Parameters:
** Xin: X position in inches
** Yin: Y position in inches
** msg: The message "1", "2", "3" , "1P" , "1 S" , "2A" or ""
*/
procedure ups_icon:
   
    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .
    define input parameter msg as character no-undo .

    run printgoto( input xin, input yin ) .

    IF msg = "" THEN 
       /* create the black box, 0.6 in square */
        assign
            apage = apage +
                "~E*c200A~E*c200B" +
                "~E*c0P~E*v0N~E*v1O~E*c1G~E*v1T"
                .
    
    
    run printgoto( input (xin + (40 / 300)), input (yin + (175 / 300)) ) .
    
    
    assign
        apage = apage + "~E(s1p40v0s2b16602T" + msg + "~E*c100G~E*v2T"
    .
    
end procedure . /*ups_icon*/
/*
** Jian 11feb99
** draw line with length and width at lefttop position 
**
** Parameters:
** Xin: X position in inches
** Yin: Y position in inches
** len: length of line in inches
** wid: width of line in inches
*/
procedure line :

    define input parameter xin as decimal no-undo .
    define input parameter yin as decimal no-undo .
    define input parameter len as decimal no-undo .
    define input parameter wid as decimal no-undo .

    run printgoto( input xin, input yin ) .

    assign
        apage = apage +
            "~E*c" + string (len * 300 ) + "A" +
            "~E*c" + string( wid * 300 ) + "B" +
            "~E*c0P"
    .

end procedure . /* end of line */

