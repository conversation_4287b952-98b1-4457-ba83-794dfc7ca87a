/*** 
** Item labels for Primary Pick Locations only .
** Zebra printer. File name is hard coded in lbl_prnt.w .
***/
                    

&SCOP DEBUG false   

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
        no-undo
            initial "@(#) $Header: /pdb/9.01/remote/RCS/zeb_ploc.p,v 1.4 2000-02-24 10:48:37-06 tanya Exp $~n" .                    

DEF INPUT PARAMETER ch_printer       AS CHAR NO-UNDO .
DEF INPUT PARAMETER ch_format        AS CHAR NO-UNDO .
DEF INPUT PARAMETER ch_value_ploc    AS CHAR NO-UNDO .
DEF INPUT PARAMETER company          AS CHAR NO-UNDO .
DEF INPUT PARAMETER warehouse        AS CHAR NO-UNDO .


DEF VAR ch_p_loc   AS CHAR NO-UNDO INIT ? .
DEF VAR ch_item1   AS CHAR NO-UNDO .
DEF VAR ch_desc1   AS CHAR NO-UNDO .
DEF VAR ch_p_loc1  AS CHAR NO-UNDO .
DEF VAR ch_l_type1 AS CHAR NO-UNDO .
DEF VAR ch_p_type1 AS CHAR NO-UNDO .
DEF VAR i_num_ent  AS INT  NO-UNDO        .
DEF VAR i_entry    AS INT  NO-UNDO INIT 1 .
DEF VAR ch_file    AS CHAR NO-UNDO        .
DEF VAR ch_print   AS CHAR NO-UNDO        .


MESSAGE "Starting Printing..." .

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .


&IF NOT {&DEBUG} &THEN
   
      
   ASSIGN
      i_num_ent = NUM-ENTRIES ( ch_value_ploc )
      .

   IF i_num_ent = 0 THEN
   DO:
      MESSAGE 'No primary location label list passed for printing...' .
   
      return .
   END.
   
&ELSE
   ASSIGN
      company    = '01'
      warehouse  = 'a5'
      ch_item1   = '.040 14 ht'
      ch_desc1   = 'Desccription'
      ch_p_loc1  = 'A001A'
      ch_l_type1 = 'Pallet'
      ch_p_type1 = 'Split'
      ch_printer   = 'zebra'
      ch_format = '3,6,10'
      i_num_ent = 1
      .
&ENDIF



/***
** Clean out temp file 
***/

OUTPUT TO VALUE ( ch_file ) .
OUTPUT CLOSE .



/***
**   Build print File 
***/

bin_loop:
DO WHILE i_num_ent >= i_entry :

   ASSIGN
      ch_p_loc1  = ENTRY ( i_entry , ch_value_ploc  )
      i_entry    = i_entry + 1 .    
                            

      FIND FIRST binmst 
         WHERE
            binmst.co_num  = company   AND
            binmst.wh_num  = warehouse AND
            binmst.bin_num = ch_p_loc1
         NO-LOCK NO-ERROR .
         
         
         IF AVAILABLE (binmst) THEN
         DO:

            ASSIGN
               ch_item1 = binmst.abs_num .
               
            CASE binmst.loc_type:
               WHEN 'T' THEN
                  ASSIGN
                     ch_l_type1 = 'Stage' .
                       
               WHEN 'S' THEN
                  ASSIGN
                     ch_l_type1 = 'Shelf' .
                     
               WHEN 'P' THEN
                  ASSIGN 
                     ch_l_type1 = 'Pallet' .
                     
               WHEN 'F' THEN
                  ASSIGN
                     ch_l_type1 = 'Flow Rack' .
                     
               WHEN 'B' THEN
                  ASSIGN
                     ch_l_type1 = 'Bulk' .
                     
               OTHERWISE
                  ASSIGN
                     ch_l_type1 = 'Unknown' .
                     
            END CASE . /* loc type */
            
            CASE binmst.prim_pick_type:
               WHEN 'S' THEN
                  ASSIGN 
                     ch_p_type1 = 'Split' .
                     
               WHEN 'F' THEN
                  ASSIGN
                     ch_p_type1 = 'Full' .
                     
               WHEN 'C' THEN
                  ASSIGN
                     ch_p_type1 = 'Counter' .
                     
               OTHERWISE
                  ASSIGN
                     ch_p_type1 = 'Normal' .
                    
            END CASE . /* prim pick type */
            
                  
            FIND FIRST item
               WHERE
                  item.co_num  = company   AND
                  item.wh_num  = warehouse AND
                  item.abs_num = ch_item1
               NO-LOCK NO-ERROR .
               
               IF AVAILABLE (item) THEN
                  ASSIGN
                     ch_desc1 = item.item_desc .
               ELSE
                  ASSIGN
                     ch_desc1 = " " .
                
         END . /* binmst found */
         ELSE
            NEXT bin_loop .



     RUN print_label ( ch_item1, ch_desc1, ch_p_loc1, ch_l_type1, ch_p_type1) .

END.

/***
**  Print the File
***/
message "About to print location labels:" ch_file .

if (opsys eq "UNIX")
then do:
    ASSIGN
    ch_print = "lp -d" + ch_printer + " " + ch_file .
    
    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .

    pause 5 no-message .

    OS-DELETE         VALUE ( ch_file    ) .
end .
else do: /*NT*/

    /********************** old code using spooler ********************
    
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        message "Copying" ch_file "to" ch_spool_dir .
        
        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .

    ******************************************************************/
    
       
    message "copying file  " ch_file "to printer " ch_printer .

    OS-COPY VALUE( ch_file) VALUE( ch_printer) .

    OS-DELETE  VALUE ( ch_file ) .   


end .

/* the end */
return .


PROCEDURE print_label: 
   
   DEF INPUT PARAMETER ch_item1   AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_desc1   AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_p_loc1  AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_l_type1 AS CHAR NO-UNDO .
   DEF INPUT PARAMETER ch_p_type1 AS CHAR NO-UNDO .
   
   DEF VAR             ch_value   AS CHAR NO-UNDO .
  
           
   ASSIGN
       ch_item1  = CAPS ( ch_item1 )
       ch_desc1  = CAPS ( ch_desc1 )
       ch_p_loc1 = CAPS ( ch_p_loc1 )
       . 
        
   RUN format_string ( ch_p_loc1, OUTPUT ch_value ) .  




    OUTPUT TO VALUE ( ch_file )  APPEND .                      
                                              
       PUT UNFORMATTED
          "^XA^CFD^FS"
          "^FO3,10"
          "^GB792,394,4^FS"
          "^FO3,263"
          "^GB792,0,4^FS"
          "^FO3,329"
          "^GB792,0,4^FS"
          "^FO267,329"
          "^GB0,70,4^FS"
          "^FO531,329"
          "^GB0,70,4^FS"
          "^FO531,329"
          "^GB0,70,4^FS"
                    
          "^FO16,43"
          "^A0N,20,25^FDItem:^FS"
          "^FO100,30"
          "^A0N,45,50^FD" + ch_item1 + "^FS"
          "^BY2.0"
          "^FO100,75"
          "^BCN,160,N,N,N^FD" + ch_item1 + "^FS"
                    
          "^FO16,270"
          "^A0N,20,25^FDDescription:^FS"
          "^FO26,304"
          "^A0N,30,35^FD" + ch_desc1 + "^FS"
                    
          "^FO16,340"
          "^A0N,20,25^FDLocation #:^FS"
          "^FO26,374"
          "^A0N,30,35^FD" + ch_value + "^FS"
                    
          "^FO283,340"
          "^A0N,20,25^FDLocation Type : ^FS"
          "^FO293,374"
          "^A0N,30,35^FD" + ch_l_type1 + "^FS"
                    
          "^FO547,340"
          "^A0N,20,25^FDPrim. Pick Loc. Type:^FS"
          "^FO557,374"
          "^A0N,30,35^FD" + ch_p_type1 + "^FS"
          "^XZ"
          .

END PROCEDURE.

PROCEDURE format_string :
/* -----------------------------------------------------------
  Purpose:    Format a location string ...  
  Parameters: Old Location (in) New formated Location (out) 
  Notes:       
-------------------------------------------------------------*/
   DEF INPUT  PARAMETER ch_p_loc  AS CHAR NO-UNDO .
   DEF OUTPUT PARAMETER ch_newloc AS CHAR NO-UNDO .

   DEF VAR ch_build    AS CHAR NO-UNDO .
   DEF VAR i_charpos   AS INT  NO-UNDO .
   DEF VAR i_numdashes AS INT  NO-UNDO .
   DEF VAR i_length    AS INT  NO-UNDO .
   DEF VAR i_count     AS INT  NO-UNDO .   
 
   ASSIGN
      i_numdashes = NUM-ENTRIES ( ch_format ) 
      ch_newloc   = ch_p_loc  
      .
         
   IF i_numdashes < 1 THEN
      RETURN .

   buildloop:
   REPEAT WHILE i_count < i_numdashes :

      ASSIGN
         i_count   = i_count + 1 
         i_length  = LENGTH ( ch_newloc )
         i_charpos = INT ( ENTRY (  i_count , ch_format ) )
         NO-ERROR .
         .

      IF ERROR-STATUS:ERROR THEN
      DO:
         MESSAGE "Problem with format string.  Please validate the format" +
            "string in system parameters."
             .
         RETURN .
      END .

      IF i_charpos > i_length THEN
         NEXT buildloop .

      ASSIGN
         ch_newloc = SUBSTRING ( ch_newloc, 1, i_charpos - 1 ) + '-' +
                     SUBSTRING ( ch_newloc, i_charpos )
                     .
   END.
      
  
END PROCEDURE.


