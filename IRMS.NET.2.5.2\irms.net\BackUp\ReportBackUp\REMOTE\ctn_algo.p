/*************************************************************************
***
**  Cartonizing Algorigthm
** 
**  Implements with a first fit algorithm.  
**
**  Created:
**     12-1-97 <PERSON> - Migration from ctn_prnn.p
**
**  Parameters:
**     company
**     warehouse
**     wave      (database batch)
**
**  Result:
**     Carton Master
**     Carton Detail
**     Carton ID in Pick table
**
**  External Procedures:
**     sort3.p -  Sorts three decimal variables from greatest to least.
**
**  Assumption:  
**     Self-ship items are always of quantity one.
***
**************************************************************************/     

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /irms/9.01/Custom/LTD/remote/ctn_algo.p 1     1/24/01 11:27a Dave $~n"
.       

&SCOP DEBUG             FALSE

/*** 
 ** Pre-Defined Global Constants
 ***/
   &SCOP          G_DEF_CARTON_WEIGHT       70 /* Default max weight for a carton which does not have a weight    */

/*****
 ** Temp Table Definitions
 ******/
  /***
   ** Stores items so not hitting db more than once for an item.
   ***/
   DEF TEMP-TABLE tt_item 
      FIELD ch_abs       AS CHAR /* Item Information... */
      FIELD d_height     AS DEC  /* Item Height         */
      FIELD d_width      AS DEC  /* Item Width          */
      FIELD d_length     AS DEC  /* Item Length         */
      FIELD l_self_ship  AS LOG  /* Self Ship Flag      */ 
      FIELD ch_uom       AS CHAR /* Unit of Measure     */
      FIELD ch_item_desc AS CHAR /* Item Description    */
      FIELD d_case_qty   AS DEC  /* Case Quantity       */
      FIELD d_pallet_qty AS DEC  /* Pallet Quantity     */
      FIELD d_weight     AS DEC  /* Weight              */
      INDEX ind_tt_item  AS PRIMARY UNIQUE ch_abs.

  /*** 
   ** Stores the picks for an order
   ***/
   DEF TEMP-TABLE order_line                               NO-UNDO 
      FIELD pickid     AS   INT                        /* Index into pick table */
      FIELD id         AS   INT 
      FIELD line       like irms.orddtl.line
      FIELD seq        like irms.orddtl.line_sequence
      FIELD item       like irms.orddtl.abs_num
      FIELD item_desc  like irms.item.item_desc  
      FIELD bin        like irms.orddtl.bin_num
      FIELD qty        like irms.orddtl.req_qty
      FIELD price      like irms.orddtl.charges
      FIELD pack_req   AS   LOG
      FIELD kit        AS   LOG
      FIELD dim        AS   DEC     EXTENT 3
      FIELD cube       AS   DEC
      FIELD zone       AS   CHAR  
      FIELD d_weight   AS   DEC 
      INDEX idlineseq  IS PRIMARY UNIQUE id line seq
      INDEX index_cube bin cube DESCENDING        .

  /***
   ** Stores all the active cartons for a warehouse
   ***/
   DEF TEMP-TABLE box_size NO-UNDO               /* Cartib Fitting table  */
      FIELD box_id  like carton_size.box_id
      FIELD dim1     AS   DEC                    /* dimensions are sorted */
      FIELD dim2     AS   DEC
      FIELD dim3     AS   DEC
      FIELD cube     AS   DEC
      FIELD d_weight AS   DEC
      INDEX i_large IS   PRIMARY cube     DESCENDING 
                                 d_weight DESCENDING
                                 dim1     DESCENDING 
                                 dim2     DESCENDING 
                                 dim3     DESCENDING  
      INDEX i_small cube d_weight dim1 dim2 dim3 .

  /***
   **  Stores the cartons detail to be writen to the database 
   **    - (similar to carton detail lines)
   ***/
   DEF TEMP-TABLE order_carton NO-UNDO   
      FIELD pickid        AS INT 
      FIELD box_num       AS INT
      FIELD bin_num       AS CHAR
      FIELD qty           AS INT
      FIELD abs_num       AS CHAR
      FIELD item_desc     AS CHAR
      FIELD price         AS DEC DECIMALS 2
      FIELD kit           AS LOG        
      FIELD d_weight      AS DEC 
      INDEX index_ord_ctn IS PRIMARY bin_num .

  /***
   ** Stores the carton header information to be written to the database 
   **    - (similar to the carton master table)
   ***/
   DEF TEMP-TABLE order_carton_size NO-UNDO
       FIELD box_num      AS INT                /* Id of box used w/ order_carton */
       FIELD box_size     AS CHAR               /* Box id from box_size           */
       FIELD d_max_dim1   AS DEC     INIT 0     /* Largest dim1 in box            */
       FIELD d_max_dim2   AS DEC     INIT 0     /* Largest dim2 in box            */
       FIELD d_max_dim3   AS DEC     INIT 0     /* Largest dim3 in box            */
       FIELD d_open_cube  AS DEC                /* Open cube in box               */
       FIELD d_used_cube  AS DEC     INIT 0     /* Cube used in box               */
       FIELD d_weight     AS DEC     INIT 0     /* Cube used in box               */
       FIELD d_max_weight AS DEC     INIT 0     /* Max Weight for box             */
       FIELD d_num_line   AS INT     INIT 0     /* Number of lines on carton      */
       FIELD zone         AS CHAR               /* Zone                           */
       FIELD ch_ctlzone   AS CHAR               /* Control Pick area              */    
       FIELD ch_item      AS CHAR               /* Item in box                    */
       FIELD l_same_item  AS LOG     INIT TRUE  /* Is same item in box?           */
       FIELD l_self_ship  AS LOG     INIT FALSE /* Is this a self ship item       */ 
       INDEX index_box_num  IS PRIMARY box_num 
       INDEX index_ctl_zone ch_ctlzone zone     .

  /***
   ** Pre-loaded all zones for the control_pick_area information
   ***/
   DEF TEMP-TABLE tt_zone NO-UNDO
      FIELD wh_zone            AS CHAR 
      FIELD control_pick_area  AS CHAR 
      INDEX ind_zone AS UNIQUE PRIMARY wh_zone
      .

/***
 ** Global Buffers and Variables
 ***/
   DEF BUFFER b_header FOR irms.ordhdr . 
   DEF BUFFER b_item   FOR tt_item     .
   DEF QUERY  q_header FOR b_header    .

/***
 ** Global Variables 
 ***/
&IF {&DEBUG} &THEN
   DEF VAR i_wave          AS INT  NO-UNDO . /* Wave/Batch being processed    */
   DEF VAR ch_co           AS CHAR NO-UNDO . /* Company                       */
   DEF VAR ch_wh           AS CHAR NO-UNDO . /* Warehouse                     */
   DEF VAR i_max_lines     AS INT  NO-UNDO . /* Max Lines on a carton         */
   DEF VAR l_pick_size_one AS LOG  NO-UNDO . /* Is pick sized defined as 1    */
   DEF VAR l_all_picks_ok  AS LOG  NO-UNDO . /* Have all picks been verified? */ 
   DEF VAR i_carton_count  AS INT  NO-UNDO . /* Total Cartons created         */

   ASSIGN
      i_wave          = 292
      ch_co           = '01'
      ch_wh           = 'A5'
      i_max_lines     = 15
      l_pick_size_one = NO
      .                     
       /*
   RUN ctn_cln.p ( ch_co, ch_wh , i_wave ) NO-ERROR .
       */
&ELSE
   DEF INPUT  PARAMETER i_wave          AS INT  NO-UNDO . /* Wave/Batch being processed    */
   DEF INPUT  PARAMETER ch_co           AS CHAR NO-UNDO . /* Company                       */
   DEF INPUT  PARAMETER ch_wh           AS CHAR NO-UNDO . /* Warehouse                     */
   DEF INPUT  PARAMETER i_max_lines     AS INT  NO-UNDO . /* Max Lines on a carton         */
   DEF INPUT  PARAMETER l_pick_size_one AS LOG  NO-UNDO . /* Is pick sized defined as 1    */
   DEF INPUT  PARAMETER l_all_picks_ok  AS LOG  NO-UNDO . /* Have all picks been verified? */ 
   DEF OUTPUT PARAMETER i_carton_count  AS INT  NO-UNDO . /* Total Cartons created         */
&ENDIF


   DEF VAR i_total_cartons AS INT  NO-UNDO . /* Total Cartons created         */

/*** End of global declarations ***/

RUN load_defaults.
/***
 ** Main-Block  
 **    For every order, cartonize it.
 ****/
OPEN QUERY q_header
   FOR EACH b_header 
      WHERE
         b_header.co_num  = ch_co    AND
         b_header.wh_num  = ch_wh    AND 
         b_header.batch   = i_wave   AND
         b_header.printed = NO     
      NO-LOCK .


printloop:
REPEAT TRANSACTION :

   GET NEXT q_header .
   
   IF NOT AVAILABLE( b_header ) THEN
   DO:
      CLOSE QUERY q_header .
      LEAVE printloop .
   END .

   IF ( b_header.order_status = "S" ) OR 
      ( b_header.assigned     = NO  ) THEN
      NEXT printloop . 

   IF NOT l_pick_size_one THEN 
   DO:
     /***
      ** Verify each pick will fit in a carton, 
      ** prior to cartinizing.  Saves hard steps later.  
      **  - Ignores full pallet quantities, may break shippable cartons
      **    User required to setup pallet_qty = case_qty to not break 
      **    cartons.
      ***/
      ASSIGN 
         l_all_picks_ok = TRUE .

      REPEAT WHILE ( l_all_picks_ok = TRUE ) :
         RUN verify_pick_in_box ( OUTPUT l_all_picks_ok ) .
      END.
   END.

   RUN get_pick_info    .

   RUN carton_rest      .

   RUN record_cartons   .

   ASSIGN
      i_carton_count = i_carton_count + i_total_cartons 
      .
      
   RUN clean_up_buffers .
END.

/**** End Main-Block ****/


/************************************************************/
PROCEDURE get_pick_info:
/***
 ** for each pick of the order line, add the line item to the 
 ** order_line information table. 
 ***/
   DEF BUFFER b_pick    FOR irms.pick   .
   DEF QUERY  q_pick    FOR b_pick      .
   DEF VAR    i_new_id  AS  INT         NO-UNDO INITIAL 0.
   
   OPEN QUERY q_pick
      FOR EACH b_pick NO-LOCK
         WHERE
            b_pick.co_num                        = ch_co                 AND
            b_pick.wh_num                        = ch_wh                 AND
            b_pick.order                         = b_header.order        AND
            b_pick.order_suffix                  = b_header.order_suffix AND
            b_pick.batch                         = i_wave                AND
            LENGTH ( TRIM ( b_pick.carton_id ) ) = 0                     AND
            b_pick.pick_status                   = "O"
         BY bin_num .
            
   pickloop:
   REPEAT:
      GET NEXT q_pick NO-LOCK .
      
      IF NOT AVAILABLE b_pick THEN
         LEAVE pickloop.
  
      CREATE order_line .

      ASSIGN
         order_line.pickid = b_pick.id
         order_line.id     = i_new_id
         order_line.line   = b_pick.line
         order_line.seq    = b_pick.line_sequence
         order_line.item   = b_pick.abs_num
         order_line.bin    = b_pick.bin_num
         order_line.qty    = IF (b_pick.pick_status = "O") THEN
                                b_pick.qty
                             ELSE 
                                0
         i_new_id          = i_new_id + 1 
         order_line.zone   = b_pick.wh_zone 
         .

      RUN find_item ( b_pick.abs_num ) NO-ERROR .

      IF AVAILABLE ( b_item ) THEN 
      DO:

         ASSIGN
            order_line.item_desc = b_item.ch_item_desc 
            order_line.dim[1]    = b_item.d_height
            order_line.dim[2]    = b_item.d_width
            order_line.dim[3]    = b_item.d_length
            order_line.d_weight  = b_item.d_weight 
            order_line.cube      = order_line.dim[1] *
                                   order_line.dim[2] *
                                   order_line.dim[3] * b_pick.qty 
            .

     /***
     **  Assumption:  Self-ship items are always of quantity one.
     ***/                                        
            RUN sort ( INPUT-OUTPUT order_line.dim[1] ,
                          INPUT-OUTPUT order_line.dim[2] ,
                          INPUT-OUTPUT order_line.dim[3] ) .

         IF b_item.l_self_ship                    OR
            ( b_item.d_case_qty = order_line.qty AND
              b_item.d_case_qty > 1                 ) THEN
         DO:
            ASSIGN
               order_line.pack_req  = FALSE
               .
            NEXT .
         END.
         ELSE 
            ASSIGN
               order_line.pack_req  = TRUE .


            /***
            ** See if item will fit in a box, else split 
            ***/
            FIND FIRST box_size 
               WHERE
                  box_size.cube >= order_line.dim[1] * order_line.dim[2] * 
                                   order_line.dim[3] AND
                  box_size.dim1 >= order_line.dim[1] AND
                  box_size.dim2 >= order_line.dim[2] AND
                  box_size.dim3 >= order_line.dim[3]  
               USE-INDEX i_large NO-ERROR.

            IF NOT AVAILABLE ( box_size ) THEN 
              /***
               ** will not fit in a box - self ship item 
               ***/               
               RUN no_box_for_item ( order_line.id , INPUT-OUTPUT i_new_id ) .
            ELSE 
              /***
               ** One will fit check cube of all items 
               ***/
               RUN fit_line_in_box ( order_line.id , INPUT-OUTPUT i_new_id ) .
               
      END .
      ELSE 
        /***
         ** No item master for line 
         ***/
         ASSIGN
            order_line.pack_req  = TRUE
            order_line.item_desc = "!!!UNKNOWN!!!" .

      IF order_line.qty = 0 THEN 
         ASSIGN 
            order_line.pack_req = TRUE .

   END . /* pickloop */
  
   FOR EACH order_line :
   END .

END PROCEDURE.


/**********************************************************/
PROCEDURE carton_rest:
/***
 ** 8/20/96  Peter - How we cartonize 
 **   1) Find largest line and put in box
 **   2) Find next largest 
 **   3) put in same box or create new box.
 **   4) Loop to 2 until completed.
 ***/      
   DEF VAR    i_first_page AS  INT        NO-UNDO .
   DEF VAR    l_ok         AS  LOG        NO-UNDO .
   DEF BUFFER b_line       FOR order_line         .
   DEF BUFFER b_oc         FOR order_carton       .
   DEF QUERY  q_line       FOR b_line             .
   DEF QUERY  b_box_size   FOR box_size           .   

   ASSIGN
      i_first_page = i_total_cartons .
      
   OPEN QUERY q_line    /* Step one:  find largest line and carton */   
      FOR EACH b_line
      USE-INDEX index_cube
          bY b_line.zone . 

   carton_loop:
   REPEAT:
      GET NEXT q_line.
      
      IF NOT AVAILABLE ( b_line ) THEN
         LEAVE carton_loop.

      IF NOT b_line.pack_req THEN  
      DO:  
        /***
         ** Line w/o carton - self ships always size 1  
         ***/
         CREATE b_oc .

         ASSIGN
            i_total_cartons = i_total_cartons + 1
            b_oc.pickid     = b_line.pickid
            b_oc.box_num    = i_total_cartons
            b_oc.bin_num    = b_line.bin
            b_oc.qty        = b_line.qty
            b_oc.abs_num    = b_line.item                            
            b_oc.price      = b_line.price 
            b_oc.item_desc  = b_line.item_desc 
            .      

      END.
      ELSE                      
      DO: /* Put in carton */       

         FOR EACH order_carton_size:
           /*** 
            ** See if line will fit in box 
            ***/
            RUN comp_carton_with_order_line ( b_line.id                 , 
                                              order_carton_size.box_num ,
                                              OUTPUT l_ok               ) .

            IF l_ok THEN
            DO:
               RUN add_to_carton ( b_line.id, order_carton_size.box_num ) .
            
               NEXT carton_loop.
            END.
         END.

         RUN new_carton ( b_line.id, INPUT-OUTPUT i_total_cartons ) .
      END.             
   END.

   RUN shrink_boxes.
   
END PROCEDURE .


/********************************************************/
PROCEDURE comp_carton_with_order_line:
/***
 **  - Compares a box and its contents with the line item to 
 **    see if they will fit in a box together.
 ***/  
   DEF INPUT  PARAMETER i_line_id AS INT     NO-UNDO .
   DEF INPUT  PARAMETER i_box_num AS INT     NO-UNDO .
   DEF OUTPUT PARAMETER l_ok      AS LOG NO-UNDO INITIAL FALSE.

   FIND FIRST order_line 
      WHERE
         order_line.id = i_line_id
      NO-LOCK.
   
   FIND FIRST tt_zone
      WHERE
         tt_zone.wh_zone = order_line.zone 
      NO-LOCK NO-ERROR .
      
   IF AVAILABLE ( tt_zone ) THEN     
     /***
      ** Find same contol pick zone 
      ***/
      FIND FIRST order_carton_size
         WHERE
            order_carton_size.box_num     = i_box_num                 AND
            order_carton_size.ch_ctlzone  = tt_zone.control_pick_area AND
            order_carton_size.d_weight   <= order_carton_size.d_max_weight - ( order_line.d_weight * order_line.qty ) 
         USE-INDEX index_ctl_zone 
         NO-LOCK NO-ERROR .
   ELSE 
      FIND FIRST order_carton_size   
        /***
         ** Default back to zone...
         ***/
         WHERE
            order_carton_size.box_num    = i_box_num       AND
            order_carton_size.zone       = order_line.zone AND
            order_carton_size.d_weight  <= order_carton_size.d_max_weight - ( order_line.d_weight * order_line.qty ) 
         USE-INDEX index_box_num 
         NO-LOCK NO-ERROR .


   IF NOT AVAILABLE ( order_carton_size ) THEN
      RETURN .
      
   IF order_carton_size.d_num_line > i_max_lines THEN
      RETURN.

   FIND FIRST box_size 
      WHERE
         box_size.cube >= order_carton_size.d_used_cube + order_line.cube              AND
         box_size.dim1 >= MAXIMUM ( order_carton_size.d_max_dim1 , order_line.dim[1] ) AND
         box_size.dim2 >= MAXIMUM ( order_carton_size.d_max_dim2 , order_line.dim[2] ) AND
         box_size.dim3 >= MAXIMUM ( order_carton_size.d_max_dim3 , order_line.dim[3] ) 
      USE-INDEX i_small NO-ERROR.

   IF AVAILABLE ( box_size ) THEN
      l_ok = TRUE  .
   ELSE
      l_ok = FALSE .
END.


/****************************************************************/
PROCEDURE add_to_carton:
/***
 ** Assign a pick to an order line
 ***/
   DEF INPUT PARAMETER i_line_id AS INT NO-UNDO . /* Line to add  */
   DEF INPUT PARAMETER i_box_num AS INT NO-UNDO . /* Box to add to */

   FIND FIRST order_line 
      WHERE
         order_line.id = i_line_id
      NO-LOCK.

   CREATE order_carton.
   
   ASSIGN
      order_carton.box_num   = i_box_num
      order_carton.pickid    = order_line.pickid
      order_carton.bin_num   = order_line.bin
      order_carton.qty       = order_line.qty
      order_carton.abs_num   = order_line.item
      order_carton.item_desc = order_line.item_desc
      order_carton.price     = order_line.price
      .

  /***  
   ** Update available cube, max dimension size, and weight
   ***/
   FIND FIRST order_carton_size
      WHERE
         order_carton_size.box_num = i_box_num
      USE-INDEX index_box_num .

   ASSIGN
      order_carton_size.d_max_dim1  = MAX ( order_carton_size.d_max_dim1, order_line.dim[1] ) 
      order_carton_size.d_max_dim2  = MAX ( order_carton_size.d_max_dim2, order_line.dim[2] ) 
      order_carton_size.d_max_dim3  = MAX ( order_carton_size.d_max_dim3, order_line.dim[3] ) 
      order_carton_size.d_open_cube = order_carton_size.d_open_cube - order_line.cube 
      order_carton_size.d_used_cube = order_carton_size.d_used_cube + order_line.cube
      order_carton_size.d_weight    = order_carton_size.d_weight    + order_line.d_weight * order_line.qty 
      order_carton_size.d_num_line  = order_carton_size.d_num_line  + 1
      order_carton_size.l_same_item = IF ( TRIM ( order_carton.abs_num ) EQ TRIM ( order_carton_size.ch_item ) ) AND 
                                           order_carton_size.l_same_item THEN
                                         YES
                                      ELSE
                                         NO
      order_carton_size.ch_item     = order_carton.abs_num
      .
END.


/********************************************************/
PROCEDURE new_carton:
/*** 
 ** Create a new carton for a line 
 ***/
   DEF INPUT        PARAMETER i_line_id AS INT NO-UNDO .
   DEF INPUT-OUTPUT PARAMETER i_box_num AS INT NO-UNDO .

   DEF BUFFER b_orline for order_line .

   FIND FIRST b_orline 
        WHERE 
           b_orline.id = i_line_id   
      NO-LOCK NO-ERROR 
      .

   FIND FIRST box_size 
      USE-INDEX i_large 
      NO-ERROR .
   
   IF NOT AVAILABLE ( box_size ) THEN
   DO:
      DISPLAY "No box sizes are defined." .
      RETURN.
   END.

   FIND FIRST tt_zone
      WHERE
         tt_zone.wh_zone = b_orline.zone 
      NO-LOCK NO-ERROR .

  /***
   ** create the largest box. 
   ***/
   CREATE order_carton_size . 
   
   ASSIGN 
      i_box_num                      = i_box_num + 1 
      order_carton_size.box_num      = i_box_num
      order_carton_size.box_size     = box_size.box_id 
      order_carton_size.d_open_cube  = box_size.cube 
      order_carton_size.zone         = b_orline.zone
      order_carton_size.d_max_weight = box_size.d_weight
      .

   IF AVAILABLE ( tt_zone ) THEN
      ASSIGN
         order_carton_size.ch_ctlzone = tt_zone.control_pick_area
         .

   RUN add_to_carton ( i_line_id, i_box_num ) .
END.


/****************************************************************/      
PROCEDURE shrink_boxes:
/***
 ** Since the order_carton_size table stores max dimensions for an
 ** item, and the used cube, we may compare the information against the
 ** box information stored in the box_size table
 ***/
   FOR EACH order_carton_size:
      FIND FIRST box_size 
         WHERE
            box_size.cube >= order_carton_size.d_used_cube AND
            box_size.dim1 >= order_carton_size.d_max_dim1  AND
            box_size.dim2 >= order_carton_size.d_max_dim2  AND
            box_size.dim3 >= order_carton_size.d_max_dim3  
         USE-INDEX i_small NO-ERROR.

      IF AVAILABLE ( box_size ) THEN
         ASSIGN
            order_carton_size.box_size    = box_size.box_id 
            order_carton_size.d_open_cube = box_size.cube - order_carton_size.d_used_cube 
            .
   END.
END.


/**********************************************************/
PROCEDURE find_item :
/*** 
 ** Uses temp-table tt_item and item table to quickly reference 
 ** all the vital item information 
 ***/
   DEF INPUT PARAMETER ch_find AS CHAR NO-UNDO .
   
   DEF BUFFER b_finditem FOR irms.item .
   
   FIND FIRST b_item
      WHERE
         b_item.ch_abs = ch_find 
      NO-ERROR .
   
   IF AVAILABLE ( b_item ) THEN
      RETURN .
   
   FIND FIRST b_finditem 
      WHERE
         b_finditem.co_num  = ch_co AND
         b_finditem.wh_num  = ch_wh AND
         b_finditem.abs_num = ch_find 
      NO-LOCK NO-ERROR .
   
   IF NOT AVAILABLE ( b_finditem ) THEN
   DO:  
     /***
      **  Return nothing... 
      ***/
      RELEASE b_item .
      RETURN .
   END.
   
   CREATE b_item .
   
   ASSIGN
      b_item.ch_abs       = b_finditem.abs_num 
      b_item.d_width      = b_finditem.width
      b_item.d_height     = b_finditem.height 
      b_item.d_length     = b_finditem.length
      b_item.l_self_ship  = b_finditem.self_ship
      b_item.ch_item_desc = b_finditem.item_desc
      b_item.ch_uom       = b_finditem.uom
      b_item.d_case_qty   = b_finditem.case_qty 
      b_item.d_pallet_qty = b_finditem.pallet_qty 
      b_item.d_weight     = b_finditem.weight
      .

   IF d_width = ? THEN
      ASSIGN
         d_width = .01
         .
   IF d_height = ? THEN
      ASSIGN
         d_height = .01
         .
   IF d_length = ? THEN
      ASSIGN
         d_length = .01 
         .

   RUN sort ( INPUT-OUTPUT b_item.d_width  ,
                 INPUT-OUTPUT b_item.d_height ,
                 INPUT-OUTPUT b_item.d_length ) .

END.



/**********************************************************/
PROCEDURE load_defaults:
/***
 **  Loads in all one time data 
 ***/

  /***
   **   Load all warehouse zones...
   **     - Control Pick Area is the key to the future of cartoning...
   ***/
   FOR EACH irms.wh_zone
      WHERE
         co_num = ch_co  AND
         wh_num = ch_wh 
      NO-LOCK :
   
      CREATE tt_zone .
   
      ASSIGN
         tt_zone.wh_zone           = wh_zone.wh_zone 
         tt_zone.control_pick_area = irms.wh_zone.control_pick_area 
         .
   END.
   
  /*** 
   ** Load all enabled cartons 
   ***/
   FOR EACH carton_size 
      WHERE
         carton_size.row_status = YES    AND
         carton_size.co_num     = ch_co  AND
         carton_size.wh_num     = ch_wh
      NO-LOCK:  /* set up the carton sizes */
   
      CREATE box_size .
    
      ASSIGN
         box_size.box_id   = carton_size.box_id
         box_size.cube     = carton_size.cube
         box_size.dim1     = carton_size.length
         box_size.dim2     = carton_size.width
         box_size.dim3     = carton_size.height
         box_size.d_weight = carton_size.weight 
         .  

      IF box_size.d_weight = 0 THEN
         ASSIGN
            box_size.d_weight = {&G_DEF_CARTON_WEIGHT} .
            
      RUN sort ( input-output box_size.dim1,
                    input-output box_size.dim2,
                    input-output box_size.dim3 ) .
      RELEASE box_size .
   END .
   
   FIND FIRST box_size  
      NO-ERROR .
      
   IF NOT AVAILABLE ( box_size ) THEN
   DO: /*
      MESSAGE 'ERROR *** No box sizes setup for company and warehouse.' . 
      */
      QUIT. 
   END.
END.


/******************************************************************/
PROCEDURE no_box_for_item:
/***
 ** Take a line and splits it into several self ship items for
 ** no box will hold the item
 ***/
   DEF INPUT        PARAMETER    i_id     AS INT NO-UNDO .
   DEF INPUT-OUTPUT PARAMETER    i_new_id AS INT NO-UNDO .
   DEF BUFFER           b_order_line FOR      order_line.

   FIND FIRST b_order_line 
      WHERE
         b_order_line.id = i_id
      NO-ERROR .

   IF NOT AVAILABLE ( b_order_line ) THEN
      RETURN .

   REPEAT WHILE b_order_line.qty > 1:
      CREATE order_line .

      ASSIGN
         order_line.pickid    = b_order_line.pickid
         order_line.id        = i_new_id
         order_line.line      = b_order_line.line
         order_line.seq       = b_order_line.seq
         order_line.item      = b_order_line.item
         order_line.bin       = b_order_line.bin
         order_line.qty       = 1
         order_line.pack_req  = FALSE
         order_line.item_desc = b_order_line.item_desc 
         order_line.dim[1]    = b_order_line.dim[1]
         order_line.dim[2]    = b_order_line.dim[2]
         order_line.dim[3]    = b_order_line.dim[3]
         order_line.cube      = order_line.dim[1] *
                                order_line.dim[2] *
                                order_line.dim[3] 
         i_new_id             = i_new_id + 1        
         b_order_line.qty     = b_order_line.qty - 1
         order_line.d_weight  = b_order_line.d_weight
         .
   END.
   
   ASSIGN
      b_order_line.pack_req = FALSE
      b_order_line.cube     = b_order_line.dim[1] *
                              b_order_line.dim[2] *
                              b_order_line.dim[3] .
END.


/***********************************************************/
PROCEDURE clean_up_buffers:

   ASSIGN
      i_total_cartons = 0 .
      
   FOR EACH order_line:
      DELETE order_line .
   END .
   
   FOR EACH order_carton:
      DELETE order_carton .
   END .
   
   FOR EACH order_carton_size:
      DELETE order_carton_size .
   END .
   
END PROCEDURE .


/*******************************************************************/
PROCEDURE fit_line_in_box:
/***
 ** Fit a line into a box.  if not one box, then split in as many boxes
 ** as necessary
 ***/
   DEF INPUT        PARAMETER i_id     AS INT NO-UNDO .
   DEF INPUT-OUTPUT PARAMETER i_new_id AS INT NO-UNDO .

   DEF VAR    d_count      AS  DEC        NO-UNDO .
   DEF BUFFER b_order_line FOR order_line .
   
   FIND FIRST b_order_line 
      WHERE
         b_order_line.id = i_id
      NO-ERROR .

   IF NOT AVAILABLE ( b_order_line ) THEN
      RETURN .

   ASSIGN 
      d_count = b_order_line.qty.

   findboxloop:   /* Find quantity that will fit in largest box */
   REPEAT:     
      FIND FIRST box_size 
         WHERE
            box_size.cube     >= b_order_line.dim[1] * b_order_line.dim[2] * 
                                 b_order_line.dim[3] * d_count AND
            box_size.dim1     >= b_order_line.dim[1] AND
            box_size.dim2     >= b_order_line.dim[2] AND
            box_size.dim3     >= b_order_line.dim[3] 
         USE-INDEX i_large NO-ERROR.
      
      IF AVAILABLE box_size THEN
         LEAVE findboxloop.
      
      d_count = d_count - 1.
   END.
   
   IF d_count = b_order_line.qty THEN
      RETURN.

  /***
   ** While more orderline than can fit in a box, split 
   ***/
   REPEAT WHILE d_count <= b_order_line.qty:
      CREATE order_line .

      ASSIGN
         order_line.pickid    = b_order_line.pickid
         order_line.id        = i_new_id
         order_line.line      = b_order_line.line
         order_line.seq       = b_order_line.seq
         order_line.item      = b_order_line.item
         order_line.bin       = b_order_line.bin
         order_line.qty       = d_count
         order_line.pack_req  = TRUE
         order_line.item_desc = b_order_line.item_desc 
         order_line.dim[1]    = b_order_line.dim[1]
         order_line.dim[2]    = b_order_line.dim[2]
         order_line.dim[3]    = b_order_line.dim[3]
         order_line.cube      = order_line.dim[1] *
                                order_line.dim[2] *
                                order_line.dim[3] * 
                                order_line.qty
         i_new_id             = i_new_id + 1        
         b_order_line.qty     = b_order_line.qty - d_count   
         b_order_line.cube    = b_order_line.dim[1] *
                                b_order_line.dim[2] *
                                b_order_line.dim[3] * 
                                b_order_line.qty    
         order_line.d_weight  = b_order_line.d_weight 
         .
   END. 

END PROCEDURE . /* fit_line_in_box */


/*******************************************************************/
PROCEDURE record_cartons:
/***
 ** Procedure creates cartonmst table records in the database
 ***/
   DEF BUFFER b_ocs    FOR order_carton_size .
   DEF BUFFER b_oc     FOR order_carton      . 
   DEF BUFFER b_cnew   FOR irms.cartonmst    .

   DEF VAR    i_loop       AS INT  NO-UNDO   INIT 1 .
   DEF VAR    i_carton_num AS INT  NO-UNDO          .
   DEF VAR    d_d1         AS DEC  NO-UNDO   INIT ? .
   DEF VAR    d_d2         AS DEC  NO-UNDO   INIT ? .
   DEF VAR    d_d3         AS DEC  NO-UNDO   INIT ? .
   DEF VAR    ch_carton_id AS CHAR NO-UNDO          .

   REPEAT WHILE i_loop <= i_total_cartons:

      FIND FIRST b_ocs
         WHERE
            i_loop = b_ocs.box_num
         NO-ERROR.
    
      IF AVAILABLE ( b_ocs ) THEN 
      DO:
         FIND FIRST box_size 
            WHERE
               box_size.box_id = b_ocs.box_size
            NO-ERROR .
       
         IF AVAILABLE ( box_size ) THEN
            ASSIGN
               d_d1 = box_size.dim1 
               d_d2 = box_size.dim2 
               d_d3 = box_size.dim3 
               .
      END.
      ELSE   
      DO:   
        /***
         ** Self Ship, load item dimensions... 
         ***/
         FIND FIRST b_oc
            WHERE 
               b_oc.box_num = i_loop
            NO-ERROR .

         IF AVAILABLE ( b_oc ) THEN   
         DO:
            RUN find_item ( b_oc.abs_num ) .
       
            IF AVAILABLE ( b_item ) THEN
               ASSIGN
                  d_d1 = b_item.d_height 
                  d_d2 = b_item.d_width
                  d_d3 = b_item.d_length 
                  .
         END.
      END.

      findcartonloop:
      REPEAT:
         ch_carton_id = 'C' + STRING ( NEXT-VALUE ( cartonmst_carton_id ), '999999999' ) .
         
         FIND FIRST cartonmst
            WHERE
               cartonmst.co_num    = ch_co        AND
               cartonmst.wh_num    = ch_wh        AND
               cartonmst.carton_id = ch_carton_id
            NO-LOCK NO-ERROR .
            
         IF NOT AVAILABLE ( cartonmst ) THEN
            LEAVE findcartonloop . 
      END.

      CREATE b_cnew .

      ASSIGN
         b_cnew.co_num       = b_header.co_num
         b_cnew.wh_num       = b_header.wh_num
         b_cnew.carton_id    = ch_carton_id
         b_cnew.order        = b_header.order
         b_cnew.order_suffix = b_header.order_suffix
         b_cnew.cust_code    = b_header.cust_code
         b_cnew.carrier_id   = TRIM( b_header.carrier ) + "/" + TRIM( b_header.service )
         b_cnew.full         = TRUE
         b_cnew.box_id       = IF AVAILABLE ( b_ocs ) THEN
                                  b_ocs.box_size
                               ELSE
                                  ''
         b_cnew.batch        = i_wave
         b_cnew.height       = d_d1
         b_cnew.width        = d_d2 
         b_cnew.weight       = IF AVAILABLE ( b_ocs ) THEN
                                  b_ocs.d_weight
                               ELSE
                                  0.00
         b_cnew.length       = d_d3
         b_cnew.row_status   = 'N'       
         b_cnew.X_of_Y       = STRING ( i_loop ) + " of " + STRING ( i_total_cartons )
         i_carton_num        = b_cnew.carton_num    
         .

      RELEASE b_cnew .

      RUN update_detail ( i_carton_num, i_loop, ch_carton_id  ) .

      ASSIGN
         i_loop = i_loop + 1 
         .
   END .
END .


/**********************************************************/
PROCEDURE update_pick:
    DEF INPUT  PARAMETER i_pickid     AS INT  NO-UNDO .   
    DEF INPUT  PARAMETER ch_carton_id AS CHAR NO-UNDO .
    
    DEF BUFFER               b_pick   FOR irms.pick .

    FIND FIRST b_pick 
       WHERE 
          b_pick.id           = i_pickid              AND
          b_pick.order        = b_header.order        AND
          b_pick.order_suffix = b_header.order_suffix AND
          b_pick.batch        = i_wave                AND
          b_pick.co_num       = b_header.co_num       AND
          b_pick.wh_num       = b_header.wh_num 
       USE-INDEX id EXCLUSIVE-LOCK NO-ERROR.

    IF NOT AVAILABLE ( b_pick ) THEN
    DO: 
       FIND FIRST b_pick
          WHERE
             b_pick.ID = i_pickid
          USE-INDEX id
          NO-LOCK.
       /*
       IF ( b_pick.batch NE i_wave ) THEN
          MESSAGE 'MESSAGE: WV1 -' b_pick.batch '::' i_wave VIEW-AS ALERT-BOX INFO.
       */
       RETURN ERROR.
    END.

    ASSIGN 
       b_pick.carton_id = ch_carton_id .

    RELEASE b_pick NO-ERROR .     
END PROCEDURE . /*update_pick*/


/**********************************************************/
PROCEDURE update_detail:
  /***
   ** kitstat:
   **          0 == NOT kit
   **          1 == kit header
   **          2 == kit member
   ***/
   DEF INPUT PARAMETER i_carton_num AS INT  NO-UNDO . /* CartonMst.id number        */
   DEF INPUT PARAMETER i_box_num    AS INT  NO-UNDO . /* order carton detail number */
   DEF INPUT PARAMETER ch_carton_id AS CHAR NO-UNDO . /* Carton 'C' Number          */

   DEF BUFFER b_oc  FOR order_carton .
   DEF BUFFER b_cdt FOR cartondtl    . 

   FOR EACH b_oc
      WHERE
         box_num = i_box_num
         .        

      RUN find_item ( b_oc.abs_num ) .
         
      DO: 
        /***
         ** Create and populate carton detail 
         ***/
         CREATE b_cdt .

         ASSIGN
            b_cdt.carton_num = i_carton_num 
            b_cdt.abs_num    = b_oc.abs_num
            b_cdt.qty        = b_oc.qty
            b_cdt.pick_id    = b_oc.pickid
            .

         IF AVAILABLE ( b_item ) THEN
            ASSIGN
               b_cdt.uom      = b_item.ch_uom
               b_cdt.case_qty = b_item.d_case_qty 
               .
         ELSE 
           /***
            ** Use defaults 
            ***/
            ASSIGN                
               b_cdt.uom      = "EA"
               b_cdt.case_qty = 1                 
               .

        /***
         ** Update cartonid in pick 
         ***/
         IF ( b_oc.pickid > 0 ) THEN 
            RUN update_pick ( b_oc.pickid, ch_carton_id ) NO-ERROR.
         
         IF ERROR-STATUS:ERROR THEN
         DO: /*
            MESSAGE 'unable to open pick' VIEW-AS ALERT-BOX  .
             */
         END.
      END.
   END.
   
END PROCEDURE . /* update_detail */


/**********************************************************/
PROCEDURE verify_pick_in_box:
   DEF OUTPUT PARAMETER l_change AS LOG NO-UNDO INIT FALSE.

   DEF BUFFER b_pick    FOR irms.pick   .
   DEF QUERY  q_pick    FOR b_pick      .
   DEF VAR    i_new_id  AS  INT         NO-UNDO INITIAL 0 .
   DEF VAR    d_count   AS  DEC         NO-UNDO           .
   
   OPEN QUERY q_pick
      FOR EACH b_pick NO-LOCK
         WHERE
            b_pick.co_num       = ch_co                 AND
            b_pick.wh_num       = ch_wh                 AND
            b_pick.order        = b_header.order        AND
            b_pick.order_suffix = b_header.order_suffix AND
            b_pick.batch        = i_wave                AND
            b_pick.pick_status  = 'O'            
         BY b_pick.id .

   verifypickloop:
   REPEAT:
      GET NEXT q_pick NO-LOCK .
      
      IF NOT AVAILABLE b_pick THEN
         LEAVE verifypickloop.

      RUN find_item ( b_pick.abs_num )  .

      IF AVAILABLE ( b_item ) THEN 
      DO:
         IF b_pick.qty = b_item.d_pallet_qty AND
            b_pick.qty > 1                   THEN
            NEXT . /* Don't mess with full pallets */
            
         IF b_pick.qty = b_item.d_case_qty AND
            b_pick.qty > 1                 THEN
         DO:
            NEXT . /* Don't mess with full cases (either) ... */
         END.
         
         IF b_pick.qty = 1 THEN
            NEXT .
            
        /***
         ** More than a pallet on one pick, split it 
         **   - should not happen.  Order Manager should prevent it.
         ***/
         IF b_pick.qty          > b_item.d_pallet_qty AND
            b_item.d_pallet_qty > 1                   THEN
         DO:
            RUN split_pick ( b_pick.id, d_pallet_qty  ) .

            ASSIGN
               l_change = TRUE .

            NEXT .
         END.
            
         ASSIGN 
            d_count = b_pick.qty .

        /***
         ** Find quantity that will fit in largest box 
         ***/
         findboxloop:   
         REPEAT:     
            FIND FIRST box_size 
               WHERE
                  box_size.cube     >= b_item.d_width  * b_item.d_height * 
                                       b_item.d_length * d_count         AND
                  box_size.dim1     >= b_item.d_width                    AND
                  box_size.dim2     >= b_item.d_height                   AND
                  box_size.dim3     >= b_item.d_length                   AND
                  box_size.d_weight >= d_count * b_item.d_weight 
               USE-INDEX i_large NO-ERROR.
      
            IF AVAILABLE box_size THEN
               LEAVE findboxloop.

            ASSIGN
               d_count = d_count - 1 .
             
            IF d_count = 1 THEN   /* Never break more than to a single item */
               LEAVE findboxloop .
         END.
   
         IF d_count = b_pick.qty THEN
            NEXT verifypickloop.

        /***
         ** While more orderline than can fit in a box, split 
         ***/
         IF d_count > 0 THEN
            RUN split_pick ( b_pick.id, d_count ) .
         
         ASSIGN
            l_change = TRUE .
      END .
      ELSE
         NEXT .

   END . /* pickloop */
END PROCEDURE . /* verity pick in box */


/**********************************************************/
PROCEDURE split_pick:
   DEF INPUT PARAMETER i_pick_id AS INT NO-UNDO .
   DEF INPUT PARAMETER d_qty     AS DEC NO-UNDO .
   
   DEF BUFFER b_old_pick FOR irms.pick .
   DEF BUFFER b_new_pick FOR irms.pick .
   
   split_pick_loop:
   REPEAT TRANSACTION ON ERROR UNDO, RETURN ERROR :
   
      FIND FIRST b_old_pick 
         WHERE
            b_old_pick.id = i_pick_id
         EXCLUSIVE NO-ERROR.
   
      IF NOT AVAILABLE ( b_old_pick ) THEN
         RETURN ERROR .
      
      IF b_old_pick.qty <= d_qty AND
         d_qty          >  0     THEN
         LEAVE split_pick_loop .
      
      CREATE b_new_pick .
      
      ASSIGN
         b_new_pick.co_num         = b_old_pick.co_num
         b_new_pick.wh_num         = b_old_pick.wh_num
         b_new_pick.batch          = b_old_pick.batch
         b_new_pick.order          = b_old_pick.order
         b_new_pick.order_suffix   = b_old_pick.order_suffix
         b_new_pick.line           = b_old_pick.line
         b_new_pick.line_sequence  = b_old_pick.line_sequence
         b_new_pick.carton_id      = b_old_pick.carton_id
         b_new_pick.abs_num        = b_old_pick.abs_num
         b_new_pick.serial_num     = b_old_pick.serial_num
         b_new_pick.lot            = b_old_pick.lot
         b_new_pick.pick_sequence  = b_old_pick.pick_sequence
         b_new_pick.wh_zone        = b_old_pick.wh_zone
         b_new_pick.aisle          = b_old_pick.aisle
         b_new_pick.bin_num        = b_old_pick.bin_num
         b_new_pick.qty            = b_old_pick.qty - d_qty
         b_new_pick.orig_qty       = b_old_pick.orig_qty
         b_new_pick.date_time      = b_old_pick.date_time
         b_new_pick.reserved       = b_old_pick.reserved
         b_new_pick.printed        = b_old_pick.printed
         b_new_pick.pallet_num     = b_old_pick.pallet_num
         b_new_pick.pallet_id      = b_old_pick.pallet_id
         b_new_pick.stock_stat     = b_old_pick.stock_stat
         b_new_pick.pick_status    = b_old_pick.pick_status
         .
      
      ASSIGN
         i_pick_id      = b_new_pick.id 
         b_old_pick.qty = d_qty 
         .
   END . /* transaction */
END PROCEDURE . /* split_pick */

PROCEDURE sort:

    DEFINE INPUT-OUTPUT PARAMETER num1 AS DEC NO-UNDO .
    DEFINE INPUT-OUTPUT PARAMETER num2 AS DEC NO-UNDO .
    DEFINE INPUT-OUTPUT PARAMETER num3 AS DEC NO-UNDO .

    DEF VAR thefirst  AS DEC NO-UNDO .
    DEF VAR thesecond AS DEC NO-UNDO .
    DEF VAR thethird  AS DEC NO-UNDO .

 IF (num1 le num2) THEN
    DO:
       IF (num2 le num3) THEN
       DO:
          RETURN .
       END .
       ELSE
       DO:
           IF (num1 le num3) THEN
           DO:
               ASSIGN 
                  thefirst  = num1
                  thesecond = num3
                  thethird  = num2
                  .
           END .
           ELSE 
           DO:
               ASSIGN
                   thefirst  = num3
                   thesecond = num1
                   thethird  = num2
                   .
           END .
       END .
    END .
    ELSE
    DO: /* num1 gt num2 */
    
        IF (num2 ge num3) THEN
        DO:
            /* they are in reverseorder */
            ASSIGN
                thefirst  = num3
                thesecond = num2
                thethird  = num1
            .
        END .
        ELSE
        DO: /* num1 gt num2, num3 gt num2 */
            IF (num1 ge num3) THEN
            DO:
                ASSIGN 
                    thefirst  = num2
                    thesecond = num3
                    thethird  = num1
                .
            END .
            ELSE
            DO:
                ASSIGN
                    thefirst  = num2
                    thesecond = num1
                    thethird  = num3
                .
            END .
        END .
    
    END .
    
    ASSIGN
        num1 = thefirst
        num2 = thesecond
        num3 = thethird
        .

END PROCEDURE
