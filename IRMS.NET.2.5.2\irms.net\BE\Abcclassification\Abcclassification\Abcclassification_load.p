/**=================================================================**
* Y:\BE_Area\src\be\Abcclassification\Abcclassification\Abcclassification_load.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/10/06, 16:46 PM
**=================================================================**/


/* Business Entity Definintions */
{Abcclassification/Abcclassification/Abcclassification_ds.i}
{Abcclassification/Abcclassification/Abcclassification_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT        PARAM ipcGUID AS  CHAR .
    DEF       OUTPUT PARAM DATASET FOR dsAbcclassification .


    DEF VAR hDataSet   AS HANDLE   NO-UNDO. 
    DEF VAR cTable     AS CHAR     NO-UNDO. 


    hDataSet = DYNAMIC-FUNCTION('getProperty' IN THIS-PROCEDURE,'DataSetHandle').
    IF hDataSet:NUM-RELATIONS > 0 THEN
       cTable = hDataSet:GET-RELATION(1):PARENT-BUFFER:NAME.
    ELSE
       cTable = hDataSet:GET-BUFFER-HANDLE(1):NAME.


    CREATE ds_Filter.
    ASSIGN
       ds_Filter.TableName  = cTable
       ds_Filter.FieldName  = 'GUID'
       ds_Filter.Operand    = '='
       ds_Filter.FieldValue = ipcGUID
       .


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


