/**=================================================================**
* Y:\irms.net.2.5.1\irms.net\BE\backdoorlogin\BackdoorLogin_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 03/25/08, 08:19 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qttBackdoor FOR ttBackdoor SCROLLING . 
QUERY qttBackdoor:QUERY-PREPARE("FOR EACH ttBackdoor").
QUERY qttBackdoor:QUERY-OPEN.


DEF  QUERY qttBackdoor_BEFORE FOR ttBackdoor_BEFORE SCROLLING . 
QUERY qttBackdoor_BEFORE:QUERY-PREPARE("FOR EACH ttBackdoor_BEFORE").
QUERY qttBackdoor_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "srcBackdoor" */
DEFINE BUFFER whmst_1 FOR irms.whmst.
DEFINE BUFFER cmpmst_1 FOR irms.cmpmst.
DEFINE QUERY qSrcsrcBackdoor
    FOR whmst_1,
        cmpmst_1
        SCROLLING.
DEFINE DATA-SOURCE srcBackdoor
    FOR QUERY qSrcsrcBackdoor
        whmst_1 KEYS (co_num,wh_num),
        cmpmst_1 KEYS (co_num)        .
DATA-SOURCE srcBackdoor:PREFER-DATASET = no.
DATA-SOURCE srcBackdoor:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.07.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttBackdoor                   AS  HANDLE              
    FIELD   hQry_ttBackdoor                  AS  HANDLE              
    FIELD   hQry_ttBackdoor_BEFORE           AS  HANDLE              
    FIELD   ttBackdoor_DataSourceHdl         AS  HANDLE              
    FIELD   ttBackdoor_BatchSize             AS  INTEGER             
                                                 INIT 0
    FIELD   ttBackdoor_Fill                  AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBackdoor_CanRead               AS  LOGICAL             
                                                 INIT yes
    FIELD   ttBackdoor_CanCreate             AS  LOGICAL             
                                                 INIT no
    FIELD   ttBackdoor_CanUpdate             AS  LOGICAL             
                                                 INIT no
    FIELD   ttBackdoor_CanDelete             AS  LOGICAL             
                                                 INIT no
    FIELD   ttBackdoor_Src_Names             AS  CHARACTER           
                                                 INIT ""
    FIELD   ttBackdoor_Src_Hdls              AS  CHARACTER           
                                                 INIT ""
    FIELD   ttBackdoor_CurrentSource         AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   ttBackdoor_UniqueKey             AS  CHARACTER           
                                                 INIT ""
    FIELD   ttBackdoor_srcBackdoor_Map       AS  CHARACTER           
                                                 INIT ""
    FIELD   ttBackdoor_srcBackdoor_CF        AS  CHARACTER           
                                                 INIT "co_num"
    FIELD   ttBackdoor_srcBackdoor_NoP       AS  CHARACTER           
                                                 INIT ""
    FIELD   srcBackdoor_hdl                  AS  HANDLE              
    FIELD   srcBackdoor_UseQuery             AS  LOGICAL             
                                                 INIT yes
    FIELD   srcBackdoor_PostTable            AS  CHARACTER           
                                                 INIT "whmst_1"
    FIELD   srcBackdoor_qhdl                 AS  HANDLE              
    FIELD   srcBackdoor_whmst_1_W            AS  CHARACTER           
                                                 INIT ""
    FIELD   srcBackdoor_whmst_1_S            AS  CHARACTER           
                                                 INIT ""
    FIELD   srcBackdoor_cmpmst_1_W           AS  CHARACTER           
                                                 INIT "cmpmst_1.co_num = whmst_1.co_num"
    FIELD   srcBackdoor_cmpmst_1_S           AS  CHARACTER           
                                                 INIT ""
    FIELD   srcBackdoor_Buffs                AS  CHARACTER           
                                                 INIT "whmst_1,cmpmst_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "whmst,ttBackdoor,cmpmst,ttBackdoor"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "ttBackdoor,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsBackdoorLogin:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_ttBackdoor                   = QUERY qttBackdoor:HANDLE
       htt_ttBackdoor                    = TEMP-TABLE ttBackdoor:HANDLE
       hQry_ttBackdoor_BEFORE            = QUERY qttBackdoor_BEFORE:HANDLE
       ttBackdoor_src_Names              = 'srcBackdoor,Default'
       ttBackdoor_src_Hdls               =         STRING(DATA-SOURCE srcBackdoor:HANDLE)
                                           + ',' + STRING(DATA-SOURCE srcBackdoor:HANDLE)
       ttBackdoor_srcBackdoor_Map        =         'co_num,whmst_1.co_num'
                                           + ',' + 'wh_desc,whmst_1.wh_desc'
                                           + ',' + 'wh_num,whmst_1.wh_num'
                                           + ',' + 'co_desc,cmpmst_1.co_name'
       srcBackdoor_hdl                   = DATA-SOURCE srcBackdoor:HANDLE
       srcBackdoor_qhdl                  = QUERY qSrcsrcBackdoor:HANDLE
       TopLevelTables                    = 'ttBackdoor'
       DataSetOneTransaction             = yes
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/backdoorlogin_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackdoorLogin .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackdoorLogin BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackdoorLogin .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackdoorLogin BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttBackdoor_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackdoorLogin .
     RUN ttBackdoor_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackdoorLogin BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttBackdoor_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackdoorLogin .
     RUN ttBackdoor_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackdoorLogin BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_ttBackdoor_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackdoorLogin .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('ttBackdoor') NO-ERROR .
     RUN ttBackdoor_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackdoorLogin BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_ttBackdoor_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsBackdoorLogin .
     RUN AfterRowFill  IN THIS-PROCEDURE ('ttBackdoor') NO-ERROR .
     RUN ttBackdoor_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsBackdoorLogin BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


