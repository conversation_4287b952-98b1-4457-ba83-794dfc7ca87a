/**=================================================================**
* Y:\BE_Area\src\be\ActionCodes\Search\Search_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:32 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE returnaction NO-UNDO
    BEFORE-TABLE returnaction_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   code                             AS  CHARACTER           
                                                 COLUMN-LABEL "Action Code"
                                                 LABEL "Action Code"
                                                 FORMAT "X(2)"
                                                 INIT ""
                                                 HELP "Enter the Action Code"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Company"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "Custom Fields"
                                                 LABEL "Custom Fields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Custom Fields"
    FIELD   description                      AS  CHARACTER           
                                                 COLUMN-LABEL "Description"
                                                 LABEL "Description"
                                                 FORMAT "X(30)"
                                                 INIT ""
                                                 HELP "Enter the Description"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID"
    FIELD   test                             AS  CHARACTER           
                                                 COLUMN-LABEL "test"
                                                 LABEL "test"
                                                 FORMAT "X(1)"
                                                 INIT ""
                                                 HELP "Enter the test"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse"

                    .
TEMP-TABLE returnaction:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsSearch
    FOR returnaction,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


