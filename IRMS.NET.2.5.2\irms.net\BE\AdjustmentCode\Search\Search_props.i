/**=================================================================**
* Y:\BE_Area\src\be\AdjustmentCode\Search\Search_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:36 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qinv_adj FOR inv_adj SCROLLING . 
QUERY qinv_adj:QUERY-PREPARE("FOR EACH inv_adj").
QUERY qinv_adj:QUERY-OPEN.


DEF  QUERY qinv_adj_BEFORE FOR inv_adj_BEFORE SCROLLING . 
QUERY qinv_adj_BEFORE:QUERY-PREPARE("FOR EACH inv_adj_BEFORE").
QUERY qinv_adj_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "inv_adj" */
DEFINE BUFFER inv_adj_1 FOR irms.inv_adj.
DEFINE QUERY qSrcinv_adj
    FOR inv_adj_1
        SCROLLING.
DEFINE DATA-SOURCE inv_adj
    FOR QUERY qSrcinv_adj
        inv_adj_1 KEYS (co_num,wh_num,adj_code)        .
DATA-SOURCE inv_adj:PREFER-DATASET = no.
DATA-SOURCE inv_adj:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_inv_adj                      AS  HANDLE              
    FIELD   hQry_inv_adj                     AS  HANDLE              
    FIELD   hQry_inv_adj_BEFORE              AS  HANDLE              
    FIELD   inv_adj_DataSourceHdl            AS  HANDLE              
    FIELD   inv_adj_BatchSize                AS  INTEGER             
                                                 INIT 50
    FIELD   inv_adj_Fill                     AS  LOGICAL             
                                                 INIT yes
    FIELD   inv_adj_CanRead                  AS  LOGICAL             
                                                 INIT yes
    FIELD   inv_adj_CanCreate                AS  LOGICAL             
                                                 INIT no
    FIELD   inv_adj_CanUpdate                AS  LOGICAL             
                                                 INIT no
    FIELD   inv_adj_CanDelete                AS  LOGICAL             
                                                 INIT no
    FIELD   inv_adj_Src_Names                AS  CHARACTER           
                                                 INIT ""
    FIELD   inv_adj_Src_Hdls                 AS  CHARACTER           
                                                 INIT ""
    FIELD   inv_adj_CurrentSource            AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   inv_adj_UniqueKey                AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   inv_adj_inv_adj_Map              AS  CHARACTER           
                                                 INIT ""
    FIELD   inv_adj_inv_adj_CF               AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   inv_adj_inv_adj_NoP              AS  CHARACTER           
                                                 INIT ""
    FIELD   inv_adj_hdl                      AS  HANDLE              
    FIELD   inv_adj_UseQuery                 AS  LOGICAL             
                                                 INIT yes
    FIELD   inv_adj_PostTable                AS  CHARACTER           
                                                 INIT "inv_adj_1"
    FIELD   inv_adj_qhdl                     AS  HANDLE              
    FIELD   inv_adj_inv_adj_1_W              AS  CHARACTER           
                                                 INIT ""
    FIELD   inv_adj_inv_adj_1_S              AS  CHARACTER           
                                                 INIT ""
    FIELD   inv_adj_Buffs                    AS  CHARACTER           
                                                 INIT "inv_adj_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "inv_adj,inv_adj"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "inv_adj,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsSearch:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_inv_adj                      = QUERY qinv_adj:HANDLE
       htt_inv_adj                       = TEMP-TABLE inv_adj:HANDLE
       hQry_inv_adj_BEFORE               = QUERY qinv_adj_BEFORE:HANDLE
       inv_adj_src_Names                 = 'inv_adj,Default'
       inv_adj_src_Hdls                  =         STRING(DATA-SOURCE inv_adj:HANDLE)
                                           + ',' + STRING(DATA-SOURCE inv_adj:HANDLE)
       inv_adj_inv_adj_Map               =         'adj_code,inv_adj_1.adj_code'
                                           + ',' + 'adj_desc,inv_adj_1.adj_desc'
                                           + ',' + 'co_num,inv_adj_1.co_num'
                                           + ',' + 'enter_loc,inv_adj_1.enter_loc'
                                           + ',' + 'GUID,inv_adj_1.GUID'
                                           + ',' + 'host_origin,inv_adj_1.host_origin'
                                           + ',' + 'status_1,inv_adj_1.status_1'
                                           + ',' + 'status_2,inv_adj_1.status_2'
                                           + ',' + 'tran_types,inv_adj_1.tran_types'
                                           + ',' + 'valid_status_1,inv_adj_1.valid_status_1'
                                           + ',' + 'valid_status_2,inv_adj_1.valid_status_2'
                                           + ',' + 'wh_num,inv_adj_1.wh_num'
       inv_adj_hdl                       = DATA-SOURCE inv_adj:HANDLE
       inv_adj_qhdl                      = QUERY qSrcinv_adj:HANDLE
       TopLevelTables                    = 'inv_adj'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_inv_adj_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN inv_adj_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_inv_adj_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN inv_adj_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_inv_adj_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('inv_adj') NO-ERROR .
     RUN inv_adj_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_inv_adj_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN AfterRowFill  IN THIS-PROCEDURE ('inv_adj') NO-ERROR .
     RUN inv_adj_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


