"ttTablePropsCREATE"
"ttTablePropsTblTableNamettrtdetSrch"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateno"
"ttTablePropscanUpdateno"
"ttTablePropscanDeleteno"
"ttTablePropsUniqueKeyGUID"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameabs_num"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelAbsolute Item Number"
"ttFieldPropsFldColLabelItem Number"
"ttFieldPropsFldHelpEnter the company-wide unique code for this item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameact_quantity"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>,>>9"
"ttFieldPropsFldSideLabelActual Quantity"
"ttFieldPropsFldColLabelActual Quantity"
"ttFieldPropsFldHelpQuantity received"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameasn_flag"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelasn_flag"
"ttFieldPropsFldColLabelASN FLag?"
"ttFieldPropsFldHelpIs this an EDI transaction?"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamebo_count"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelB/O Count"
"ttFieldPropsFldColLabelB/O Count"
"ttFieldPropsFldHelpThe number of back orders for this item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamebo_quantity"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>,>>9"
"ttFieldPropsFldSideLabelB/O Quantity"
"ttFieldPropsFldColLabelB/O Quantity"
"ttFieldPropsFldHelpQuantity."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamecase_qty"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>,>>9"
"ttFieldPropsFldSideLabelOuter Pack"
"ttFieldPropsFldColLabelOuter Pack"
"ttFieldPropsFldHelpOuter pack quantity for this RT line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamecomments"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelComments"
"ttFieldPropsFldColLabelComments"
"ttFieldPropsFldHelpComments for this RT line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the company number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustom Fields"
"ttFieldPropsFldColLabelCustom Fields"
"ttFieldPropsFldHelpEnter the Custom Fields"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamedelivery"
"ttFieldPropsFldDataTypedate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelDelivery"
"ttFieldPropsFldColLabelDelivery"
"ttFieldPropsFldHelpEstimated delivery date."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameexp_quantity"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>,>>9"
"ttFieldPropsFldSideLabelExpected Quantity"
"ttFieldPropsFldColLabelExpected Quantity"
"ttFieldPropsFldHelpQuantity expected"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameGUID1"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99999999.999999999"
"ttFieldPropsFldSideLabelGUID1"
"ttFieldPropsFldColLabelGUID1"
"ttFieldPropsFldHelpEnter the GUID1"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameGUID2"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID2"
"ttFieldPropsFldColLabelGUID2"
"ttFieldPropsFldHelpEnter the GUID2"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamehost_origin"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(25)"
"ttFieldPropsFldSideLabelHost Origin"
"ttFieldPropsFldColLabelHost Origin"
"ttFieldPropsFldHelpEnter the Host Origin"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameitem_cost"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat$>>>,>>9.99"
"ttFieldPropsFldSideLabelCost"
"ttFieldPropsFldColLabelCost"
"ttFieldPropsFldHelpEnter the Cost"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameitem_desc"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelItem Description"
"ttFieldPropsFldColLabelItem Description"
"ttFieldPropsFldHelpDescription for this item."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameitem_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelAlternate Item Number"
"ttFieldPropsFldColLabelAlternate Item Number"
"ttFieldPropsFldHelpEnter this item's catalog number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameline_indicator"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelline_indicator"
"ttFieldPropsFldColLabelline_indicator"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameline_num"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>>>>9"
"ttFieldPropsFldSideLabelLine Number"
"ttFieldPropsFldColLabelLine Number"
"ttFieldPropsFldHelpEnter the Line Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameline_sequence"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelline_sequence"
"ttFieldPropsFldColLabelLine Sequence"
"ttFieldPropsFldHelpEnter the line_sequence"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamelot"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelLot"
"ttFieldPropsFldColLabelLot"
"ttFieldPropsFldHelpLot Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameordered_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelQuantity Ordered"
"ttFieldPropsFldColLabelQuantity Ordered"
"ttFieldPropsFldHelpQuantity ordered."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepacker"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(14)"
"ttFieldPropsFldSideLabelpacker"
"ttFieldPropsFldColLabelPacker"
"ttFieldPropsFldHelpEnter the packer"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepacking_list"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelpacking_list"
"ttFieldPropsFldColLabelPacking List RT?"
"ttFieldPropsFldHelpEnter the packing_list"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepercent_fill"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat->>,>>9.99"
"ttFieldPropsFldSideLabelPercent Fill"
"ttFieldPropsFldColLabelPercent Fill"
"ttFieldPropsFldHelpEnter the Percent Fill"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepool"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelPool"
"ttFieldPropsFldColLabelPool"
"ttFieldPropsFldHelpEnter the Pool"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepo_line"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>9"
"ttFieldPropsFldSideLabelpo_line"
"ttFieldPropsFldColLabelPO Line"
"ttFieldPropsFldHelpEnter the po_line"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepo_number"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelPO Number"
"ttFieldPropsFldColLabelPO Number"
"ttFieldPropsFldHelpEnter the PO Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepo_suffix"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelPO Suffix"
"ttFieldPropsFldColLabelPO Suffix"
"ttFieldPropsFldHelpEnter the PO Suffix"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamepo_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatXX"
"ttFieldPropsFldSideLabelPO Type"
"ttFieldPropsFldColLabelPO Type"
"ttFieldPropsFldHelpPurchase Order Type."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameprod_desc"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(30)"
"ttFieldPropsFldSideLabelprod_desc"
"ttFieldPropsFldColLabelProduct Description"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameqty_unavail"
"ttFieldPropsFldDataTypeDecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>>.99"
"ttFieldPropsFldSideLabelqty_unavail"
"ttFieldPropsFldColLabelUnavailable Qty"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamequantity"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>,>>9"
"ttFieldPropsFldSideLabelQuantity Left"
"ttFieldPropsFldColLabelQuantity Left"
"ttFieldPropsFldHelpQuantity."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamerd_po_type"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelrd_po_type"
"ttFieldPropsFldColLabelrd_po_type"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamereceiver_num"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelreceiver_num"
"ttFieldPropsFldColLabelreceiver_num"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamereturn_fl"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelreturn_fl"
"ttFieldPropsFldColLabelreturn_fl"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameret_line"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit1"
"ttFieldPropsFldFormat>>>9"
"ttFieldPropsFldSideLabelLine"
"ttFieldPropsFldColLabelLine"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameret_line_sequence"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>9"
"ttFieldPropsFldSideLabelret_line_sequence"
"ttFieldPropsFldColLabelret_line_sequence"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamerow_status"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitO"
"ttFieldPropsFldFormatx"
"ttFieldPropsFldSideLabelStatus"
"ttFieldPropsFldColLabelStatus"
"ttFieldPropsFldHelpThe status for this R/T line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamertn_order"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(10)"
"ttFieldPropsFldSideLabelOrder"
"ttFieldPropsFldColLabelOrder"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamertn_order_suffix"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelOrder Suffix"
"ttFieldPropsFldColLabelOrder Suffix"
"ttFieldPropsFldHelpR&D Field"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectno"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamert_id"
"ttFieldPropsFldDataTypeInteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>>>>9"
"ttFieldPropsFldSideLabelRT Id"
"ttFieldPropsFldColLabelRT ID"
"ttFieldPropsFldHelpDO NOT EDIT THIS FIELD -- RT id assigned by IRMS"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamespecial_handling"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelSpecial Handling"
"ttFieldPropsFldColLabelSpecial Handling"
"ttFieldPropsFldHelp<B>ack-order, <C>ross-docking, <T>ie."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNameuom"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(4)"
"ttFieldPropsFldSideLabelUnit of Measure"
"ttFieldPropsFldColLabelUOM"
"ttFieldPropsFldHelpUnit of Measure for this RT line."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamevendor_id"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(9)"
"ttFieldPropsFldSideLabelVendor ID"
"ttFieldPropsFldColLabelVendor ID"
"ttFieldPropsFldHelpVendor ID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamevend_item"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelVendor Item"
"ttFieldPropsFldColLabelVendor Item"
"ttFieldPropsFldHelpVendor-specific item number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamettrtdetSrch"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse Number."
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNamesrcttrtdetsh"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTablertdet_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNamesrcttorddtlsh"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTableorddtl_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcttrtdetsh"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.rtdet"
"ttDataSourceJoinscBufNamertdet_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsrt_id,line_num"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcttorddtlsh"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.orddtl"
"ttDataSourceJoinscBufNameorddtl_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsid,line,line_sequence"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamesrcttorddtlsh"
"ttDataSourceJoinscDSTable"
"ttDataSourceJoinscDBTableirms.item"
"ttDataSourceJoinscBufNameitem_1"
"ttDataSourceJoinscDBWhereitem_1.co_num = orddtl_1.co_num and item_1.wh_num = orddtl_1.wh_num and 
item_1.abs_num = orddtl_1.abs_num"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num,abs_num"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNameY:\BE_Area\src\blp\SearchARRetQty_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstyes"
"ttOptionsmakeNextyes"
"ttOptionsmakePrevyes"
"ttOptionsmakeLastyes"
"ttOptionsmakepostno"
"ttOptionsmakeLoadno"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionyes"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTablettrtdetSrch"
"ttAttachSourcecSrcNamesrcttrtdetsh"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTablettorddtlSrch"
"ttAttachSourcecSrcNamesrcttorddtlsh"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsabs_num,rtdet_1.abs_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsact_quantity,rtdet_1.act_quantity"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsasn_flag,rtdet_1.asn_flag"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsbo_count,rtdet_1.bo_count"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsbo_quantity,rtdet_1.bo_quantity"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldscase_qty,rtdet_1.case_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldscomments,rtdet_1.comments"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsco_num,rtdet_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsdelivery,rtdet_1.delivery"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsexp_quantity,rtdet_1.exp_quantity"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsGUID,rtdet_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldshost_origin,rtdet_1.host_origin"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsitem_cost,rtdet_1.item_cost"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsitem_desc,rtdet_1.item_desc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsitem_num,rtdet_1.item_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsline_indicator,rtdet_1.line_indicator"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsline_num,rtdet_1.line_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsline_sequence,rtdet_1.line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldslot,rtdet_1.lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsordered_qty,rtdet_1.ordered_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspacker,rtdet_1.packer"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspacking_list,rtdet_1.packing_list"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspercent_fill,rtdet_1.percent_fill"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspool,rtdet_1.pool"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspo_line,rtdet_1.po_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspo_number,rtdet_1.po_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspo_suffix,rtdet_1.po_suffix"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldspo_type,rtdet_1.po_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsprod_desc,rtdet_1.prod_desc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsqty_unavail,rtdet_1.qty_unavail"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsquantity,rtdet_1.quantity"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsrd_po_type,rtdet_1.rd_po_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsreceiver_num,rtdet_1.receiver_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsreturn_fl,rtdet_1.return_fl"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsret_line,rtdet_1.ret_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsret_line_sequence,rtdet_1.ret_line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsrow_status,rtdet_1.row_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsrtn_order,rtdet_1.rtn_order"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsrtn_order_suffix,rtdet_1.rtn_order_suffix"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsrt_id,rtdet_1.rt_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsspecial_handling,rtdet_1.special_handling"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsuom,rtdet_1.uom"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsvendor_id,rtdet_1.vendor_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldsvend_item,rtdet_1.vend_item"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettrtdetSrch"
"ttAttachDtlcSrcNamesrcttrtdetsh"
"ttAttachDtlMappedFieldswh_num,rtdet_1.wh_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsabs_num,orddtl_1.abs_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsact_qty,orddtl_1.act_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsassigned,orddtl_1.assigned"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsbin_num,orddtl_1.bin_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldscharges,orddtl_1.charges"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldscomment,orddtl_1.comment"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsco_num,orddtl_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsdiscount,orddtl_1.discount"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsDiscountAmt,orddtl_1.DiscountAmt"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsdrop_cube,orddtl_1.drop_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsdrop_weight,orddtl_1.drop_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsExtdPrice,orddtl_1.ExtdPrice"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsfiller_flag,orddtl_1.filler_flag"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsfl_zone,orddtl_1.fl_zone"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsgift_wrap,orddtl_1.gift_wrap"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsGUID,orddtl_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldshost_origin,orddtl_1.host_origin"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsid,orddtl_1.id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsitem_desc,item_1.item_desc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline,orddtl_1.line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline_alt_number,orddtl_1.line_alt_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline_sequence,orddtl_1.line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsline_status,orddtl_1.line_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldslot,orddtl_1.lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_employee,orddtl_1.msds_employee"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_packed,orddtl_1.msds_packed"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_required,orddtl_1.msds_required"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsmsds_sheet,orddtl_1.msds_sheet"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsordered_qty,orddtl_1.ordered_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorder_alt_num,orddtl_1.order_alt_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorder_alt_suf,orddtl_1.order_alt_suf"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorig_cube,orddtl_1.orig_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorig_req_qty,orddtl_1.orig_req_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsorig_weight,orddtl_1.orig_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspackage_code,orddtl_1.package_code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspick_line,orddtl_1.pick_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspool,orddtl_1.pool"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_line,orddtl_1.po_line"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_line_sequence,orddtl_1.po_line_sequence"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_number,orddtl_1.po_number"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldspo_suffix,orddtl_1.po_suffix"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsreq_emp,orddtl_1.req_emp"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsreq_qty,orddtl_1.req_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsret_qty,orddtl_1.ret_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsrt_num,orddtl_1.rt_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldssame_lot,orddtl_1.same_lot"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsserial_num,orddtl_1.serial_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsship_cube,orddtl_1.ship_cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsship_weight,orddtl_1.ship_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsstock_stat,orddtl_1.stock_stat"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldstax,orddtl_1.tax"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldsvendor_id,orddtl_1.vendor_id"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldswh_num,orddtl_1.wh_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablettorddtlSrch"
"ttAttachDtlcSrcNamesrcttorddtlsh"
"ttAttachDtlMappedFieldswork_center,orddtl_1.work_center"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"