/**
*** File zebra.p 
***
*** John August 23, 1999
***
*** Description:printer driver for Zebra printer  
***
*** Amendments: WRB12152000 - Added in Routines to Handle Code 39 bar codes
***             and UPCA bar codes
**/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/rf/RCS/zebra.p,v 1.5 2000/05/23 15:52:21 jyy8301 Exp $~n~n"
.   
/* <<<<<<<<<< GLOBAL PARAMETER  >>>>>>>>>> */
define shared variable apage as character no-undo .
define shared variable lines as integer  no-undo .

procedure line :
    
    define input parameter x_at  as integer no-undo .
    define input parameter y_at  as integer no-undo .
    define input parameter len   as integer no-undo .
    define input parameter thick as integer no-undo .
    define input parameter dir   as integer no-undo .

    define variable wid as integer no-undo .
    case dir :
    when 0 then 
        assign wid = 0 .
    when 1 then 
        assign  wid = len 
                len = 0 .
    when 2 then 
        assign  
            x_at = x_at - len
            wid = 0 . 
    when 3 then 
        assign  
            y_at = y_at - len
            wid = len 
            len = 0 .
    end case .

    assign 
        apage = apage + 
            "^FO" + string(x_at) + "," + string(y_at) + /* start point */ 
            "^GB" +                                     /* Box symbol  */ 
                string(len) + "," +                     /* width  of box */
                string(wid) + "," +                     /* height of box */
                string(thick) +                         /* thick of line */ 
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
procedure box  :
    
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter wid  as integer no-undo .
    define input parameter hei  as integer no-undo .
    define input parameter thick as integer no-undo .
    
    assign 
        apage = apage + 
            "^FO" + string(x_at) + "," + string(y_at) + /* start point */ 
            "^GB" +                                     /* Box symbol  */ 
            string(wid) + "," +                     /* width  of box */
            string(hei) + "," +                     /* height of box */
            string(thick) +                         /* thick of line */ 
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
procedure prn_str1 :
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter fonts as character no-undo .
    define input parameter dir  as character no-undo .
    define input parameter size as decimal no-undo .
    define input parameter str  as character no-undo .  
    
    if str EQ ?
    then assign str = "" .

    /*** font    from A-Z and 1-9    ***/
    /*** size 10-1500 dots ********/
    /*** dir can be :
            "N"         Normal 
            "R"         Rotated 90 degrees 
            "I"         Inverted 180 degrees
            "B"         Read from Bottom up, 270 degrees
    ***/
    assign 
        apage = apage + 
            "^FO" + string(x_at)  + "," + string(y_at) +   /* start point */
            "^FW" + string(dir)   + "," +                  /* direction   */
            "^A"  + string("F") + "," +                  /* select font */
            "^FD" + string(str)   +                        /* string      */
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
procedure prn_label :
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter dir  as character no-undo .
    define input parameter size as decimal no-undo .
    define input parameter str  as character no-undo .  

    if str EQ ?
    then assign str = "" .
    
    assign 
        size = (size * 203) / 72 .
        apage = apage + 
            "^FO" + string(x_at)  + "," + string(y_at) +   /* start point */
            "^A0,"  +                                      /* select font */
            string(dir)           + "," +                  /* direction   */
            string(size) + "," + string(integer(size * 0.10 )) + 
            "^FD" + string(str)   +                        /* string      */
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
procedure rev_str :  
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter dir  as character no-undo .
    define input parameter size as decimal no-undo .
    define input parameter str  as character no-undo .  

    if str EQ ?
    then assign str = "" .
    
    assign 
        size = (size * 203) / 72 .
        apage = apage + 
            "^FO" + string(x_at)  + "," + string(y_at - 10 ) + 
            "^GB" + string(size * length(str) * 0.52) +  ",0," + 
                    string(size) +  "^FS~n" +  
            "^FO" + string(x_at)  + "," + string(y_at) +   /* start point */
            "^FR^A0,"  +                                   /* select font */
            string(dir)           + "," +                  /* direction   */
            string(size) + "," + string(integer(size * 0.10 )) + 
            "^FD" + string(str)   +                        /* string      */
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
procedure prn_str :
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter fonts as character no-undo .
    define input parameter dir  as character no-undo .
    define input parameter size as decimal no-undo .
    define input parameter str  as character no-undo .  

    /*** font    from A-Z and 1-9    ***/
    /*** size points size  ********/
    /*** dir should be :
            "N"         Normal 
            "R"         Rotated 90 degrees 
            "I"         Inverted 180 degrees
            "B"         Read from Bottom up, 270 degrees
    ***/
    if str EQ ?
    then assign str = "" .

    assign 
        size = (size * 203) / 72 .
        apage = apage + 
            "^FO" + string(x_at)  + "," + string(y_at) +   /* start point */
            "^FW" + string(dir)   + "," +                  /* direction   */
            "^A"  + string(fonts) + "," +                  /* select font */
            string(dir)           + "," +                  /* direction   */
            string(size ) + "," + string(integer(size * 0.75 )) + 
            "^FD" + string(str)   +                        /* string      */
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
procedure head :
    /*** head and set top left asposition 0,0 ******/
    assign 
        apage = apage + "^XA^CFD^LH0,0^FS~n" .
end .
procedure tail :
    assign 
        apage = apage + "^XZ~n" .
end .
procedure bar_128 :   
    
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter hei  as integer no-undo .
    define input parameter dir  as character no-undo .
    define input parameter wid  as decimal no-undo .
    define input parameter bar  as character no-undo .

    if bar EQ ?
    then assign bar = "" .
    assign 
        apage = apage + 
            "^BY" + string(wid) +  
            "^FO" + string(x_at) + "," + string(y_at) +  /* start point */
            "^BC" + string(dir)  + "," +                 /* Barcode direction */
                    string(hei) + ",N,N,N" +             /* height and pars.*/
            "^FD"   + string(bar) +                      /* barcode */ 
            "^FS~n"
    .
    assign lines = lines + 1 .
end .
/* WRB12152000 - Start */
procedure bar_code39:
    define input parameter x_at as integer no-undo.
    define input parameter y_at as integer no-undo.
    define input parameter hei as integer no-undo.
    define input parameter dir as character no-undo.
    define input parameter wid as decimal no-undo.
    define input parameter bar as character no-undo.
    
    if bar = ? then
        assign bar = "".
    apage = apage + 
            "^BY" + string(wid) + 
            "^FO" + string(x_at) + "," + string(y_at) +
            "^B3" + string(dir) + ",N," + 
                    string(hei) + ",N,N" +
            "^FD" + string(bar) +
            "^FS~n"
    .
  
    assign lines = lines + 1 .
    
end .
procedure bar_upca:
    define input parameter x_at as integer no-undo.
    define input parameter y_at as integer no-undo.
    define input parameter hei as integer no-undo.
    define input parameter dir as character no-undo.
    define input parameter wid as decimal no-undo.
    define input parameter bar as character no-undo.
    
    if bar = ? then
        assign bar = "".
    apage = apage + 
            "^BY" + string(wid) +
            "^FO" + string(x_at) + "," + string(y_at) + 
            "^BU" + string(dir) + "," + string(hei) + ",Y,N,Y" + 
            "^FD" + string(bar) + 
            "^FS~n".        
    assign lines = lines + 1.        
end.
/* WRB12152000 - End */
procedure reverse  :      
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter ups_num as character no-undo .

    if ups_num EQ ?
    then assign ups_num = "" .

    assign 
        apage = apage + 
            "^FO" + string(x_at) + "," + string(y_at) +
            "^GB" + string(110) + ",0," + string(110) + "^FS~n" +  
            "^FO" + string(x_at) + "," + string(y_at) + 
            "^FR^AD," + "110" + "^FD" + string(ups_num) + 
            "^FS~n" 
    .
end .
procedure draw_ups :      
    
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter len  as integer no-undo .
    define input parameter dir  as integer no-undo .
    define input parameter service as character no-undo .
    define input parameter z_num as character no-undo .
    
    define variable ups_num as character format "x(1)" no-undo .
    define variable ups_lbl as character format "x(30)" no-undo .

    case service :
        when "GD" then do:
            assign 
                ups_num = " " 
                ups_lbl = "UPS GROUNDTRAC "
            .
        end .
        when "ND" then do:
            assign 
                ups_num = "1" 
                ups_lbl = "UPS NEXT DAY AIR"
            .
        end .
        when "2D" then do:
            assign 
                ups_num = "2" 
                ups_lbl = "UPS 2ND DAY AIR" 
            .
        end .
        when "3D" then do:
            assign 
                ups_num = "3" 
                ups_lbl = "UPS 3 DAY SELECT"
            .
        end .
        otherwise do:      
            assign 
                ups_num = " " 
                ups_lbl = "UPS GROUNDTRAC "
            .
        end .
    end case .

    run line  (x_at , y_at      , len - 142 , 16, dir) .
    run line  (x_at , y_at + 142, len - 142 ,  2, dir) .
    run line  (x_at , y_at + 406, len       ,  2, dir) .

    /**** draw ups icon ****/
    assign 
        apage = apage + 
            "^FO" + string(x_at + len - 142) + "," + string(y_at) +
            "^GB" + string(142) + ",0," + string(142) + "^FS~n" +  
            "^FO" + string(x_at + len - 112 ) + "," + string(y_at + 20) + 
            "^FR^AD," + "142" + "^FD" + string(ups_num) + 
            "^FS~n" 
    .
    run prn_str  (x_at + 15 ,y_at + 20 , "0", "N", 30 , ups_lbl) .
    run prn_str  (x_at + 50 ,y_at + 90 , "0", "N", 20 , "TRACKING NUMBER") .
    run prn_str  (x_at + 180,y_at + 150, "0", "N", 15  , z_num) .

    assign lines = lines + 1 .
    /***** Print barcode ******/    
    run bar_128 (x_at + 100 , y_at + 180 , 203 , "N" , 3 , z_num ) .
    
end .
procedure maxicode :      
    
    define input parameter x_at as integer no-undo .
    define input parameter y_at as integer no-undo .
    define input parameter code as character no-undo .

    if code EQ ?
    then assign code = "" .

    /**** draw maxicode ****/
    assign 
        apage = apage + 
            "^FO"  + string(x_at) + "," + string(y_at) + "~n" + 
            "^CVN~n" + 
            "^BD2^FH" + 
            "^FD" + code +   
            "^FS~n" 
    .
end .
procedure destroy:
    delete procedure this-procedure .
end procedure . /*destroy*/
