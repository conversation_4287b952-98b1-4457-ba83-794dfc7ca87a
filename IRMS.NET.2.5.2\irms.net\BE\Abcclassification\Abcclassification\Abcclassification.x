"ttTablePropsCREATE"
"ttTablePropsTblTableNameABC"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateyes"
"ttTablePropscanUpdateyes"
"ttTablePropscanDeleteyes"
"ttTablePropsUniqueKeyGUID"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameA_count_interval"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit45"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelA Interval"
"ttFieldPropsFldColLabelA Interval"
"ttFieldPropsFldHelpEnter the A Interval"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamea_count_loc"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelA Count Locations"
"ttFieldPropsFldColLabelA Count Locations"
"ttFieldPropsFldHelpEnter the A Count Locations"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameA_count_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit5"
"ttFieldPropsFldFormat>>%"
"ttFieldPropsFldSideLabelA Percent By Count"
"ttFieldPropsFldColLabelA Percent By Count"
"ttFieldPropsFldHelpEnter the A Percent By Count"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameA_dollar_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit70"
"ttFieldPropsFldFormat>>%"
"ttFieldPropsFldSideLabelA Percent By Dollar"
"ttFieldPropsFldColLabelA Percent By Dollar"
"ttFieldPropsFldHelpEnter the A Percent By Dollar"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameB_count_interval"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit90"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelB Interval"
"ttFieldPropsFldColLabelB Interval"
"ttFieldPropsFldHelpEnter the B Interval"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameb_count_loc"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelB Count Loc"
"ttFieldPropsFldColLabelB Count Loc"
"ttFieldPropsFldHelpEnter the B Count Loc"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameB_count_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit10"
"ttFieldPropsFldFormat>>9%"
"ttFieldPropsFldSideLabelB Percent By Count"
"ttFieldPropsFldColLabelB Percent By Count"
"ttFieldPropsFldHelpEnter the B Percent By Count"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameB_dollar_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit20"
"ttFieldPropsFldFormat>>9%"
"ttFieldPropsFldSideLabelB Percent By Dollar"
"ttFieldPropsFldColLabelB Percent By Dollar"
"ttFieldPropsFldHelpEnter the B Percent By Dollar"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamecount_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelCount Type"
"ttFieldPropsFldColLabelCount Type"
"ttFieldPropsFldHelpEnter the Count Type"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the Company"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustom Fields"
"ttFieldPropsFldColLabelCustom Fields"
"ttFieldPropsFldHelpEnter the Custom Fields"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameC_count_interval"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit180"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelC Interval"
"ttFieldPropsFldColLabelC Interval"
"ttFieldPropsFldHelpEnter the C Interval"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamec_count_loc"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelC Count Loc"
"ttFieldPropsFldColLabelC Count Loc"
"ttFieldPropsFldHelpEnter the C Count Loc"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameC_count_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit65"
"ttFieldPropsFldFormat>>9%"
"ttFieldPropsFldSideLabelC Percent By Count"
"ttFieldPropsFldColLabelC Percent By Count"
"ttFieldPropsFldHelpEnter the C Percent By Count"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameC_dollar_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit10"
"ttFieldPropsFldFormat>>9%"
"ttFieldPropsFldSideLabelC Percent By Dollar"
"ttFieldPropsFldColLabelC Percent By Dollar"
"ttFieldPropsFldHelpEnter the C Percent By Dollar"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameO_count_interval"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit360"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelOther Interval"
"ttFieldPropsFldColLabelOther Interval"
"ttFieldPropsFldHelpEnter the Other Interval"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameo_count_loc"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelOther Count Loc"
"ttFieldPropsFldColLabelOther Count Loc"
"ttFieldPropsFldHelpEnter the Other Count Loc"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameO_count_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit20"
"ttFieldPropsFldFormat>>%"
"ttFieldPropsFldSideLabelOther Percent By Count"
"ttFieldPropsFldColLabelOther Percent By Count"
"ttFieldPropsFldHelpEnter the Other Percent By Count"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNameO_dollar_percent"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>%"
"ttFieldPropsFldSideLabelOther Percent By Dollar"
"ttFieldPropsFldColLabelOther Percent By Dollar"
"ttFieldPropsFldHelpEnter the Other Percent By Dollar"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamerecalc_interval"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit1"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelRecalculation Interval"
"ttFieldPropsFldColLabelRecalculation Interval"
"ttFieldPropsFldHelpEnter the Recalculation Interval"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamerecalc_last"
"ttFieldPropsFldDataTypedate"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormat99/99/9999"
"ttFieldPropsFldSideLabelLast Recalculation"
"ttFieldPropsFldColLabelLast Recalculation"
"ttFieldPropsFldHelpEnter the Last Recalculation"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamerecalc_timeframe"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitM"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelRecalculation Time-frame"
"ttFieldPropsFldColLabelRecalculation Time-frame"
"ttFieldPropsFldHelpEnter the Recalculation Time-frame"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamerecalc_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelRecalc Type"
"ttFieldPropsFldColLabelRecalc Type"
"ttFieldPropsFldHelpEnter the Recalc Type"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamevalforinclude"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelvalforinclude"
"ttFieldPropsFldColLabelvalforinclude"
"ttFieldPropsFldHelpEnter the valforinclude"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamevalforreport"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelvalforreport"
"ttFieldPropsFldColLabelvalforreport"
"ttFieldPropsFldHelpEnter the valforreport"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamevalforupdate"
"ttFieldPropsFldDataTypeCharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(10)"
"ttFieldPropsFldSideLabelvalforupdate"
"ttFieldPropsFldColLabelvalforupdate"
"ttFieldPropsFldHelpEnter the valforupdate"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameABC"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse"
"ttDataSourceCREATE"
"ttDataSourceDSrcNameABC"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTableABC_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNameABC"
"ttDataSourceJoinscDSTableABC"
"ttDataSourceJoinscDBTableirms.ABC"
"ttDataSourceJoinscBufNameABC_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num,recalc_last"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNamey:\BE_Area\src\blp\ABCClass_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstno"
"ttOptionsmakeNextno"
"ttOptionsmakePrevno"
"ttOptionsmakeLastno"
"ttOptionsmakepostyes"
"ttOptionsmakeLoadyes"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionno"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTableABC"
"ttAttachSourcecSrcNameABC"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsA_count_interval,ABC_1.A_count_interval"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsa_count_loc,ABC_1.a_count_loc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsA_count_percent,ABC_1.A_count_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsA_dollar_percent,ABC_1.A_dollar_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsB_count_interval,ABC_1.B_count_interval"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsb_count_loc,ABC_1.b_count_loc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsB_count_percent,ABC_1.B_count_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsB_dollar_percent,ABC_1.B_dollar_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldscount_type,ABC_1.count_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsco_num,ABC_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsC_count_interval,ABC_1.C_count_interval"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsc_count_loc,ABC_1.c_count_loc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsC_count_percent,ABC_1.C_count_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsC_dollar_percent,ABC_1.C_dollar_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsGUID,ABC_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsO_count_interval,ABC_1.O_count_interval"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldso_count_loc,ABC_1.o_count_loc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsO_count_percent,ABC_1.O_count_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsO_dollar_percent,ABC_1.O_dollar_percent"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsrecalc_interval,ABC_1.recalc_interval"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsrecalc_last,ABC_1.recalc_last"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsrecalc_timeframe,ABC_1.recalc_timeframe"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldsrecalc_type,ABC_1.recalc_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableABC"
"ttAttachDtlcSrcNameABC"
"ttAttachDtlMappedFieldswh_num,ABC_1.wh_num"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"