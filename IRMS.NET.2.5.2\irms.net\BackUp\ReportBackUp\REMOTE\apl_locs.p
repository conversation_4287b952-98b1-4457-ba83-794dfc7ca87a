/*** 
** Location labels for Apollo printer .
** File name is hard coded in lbl_prnt.w .
***/

/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: /pdb/9.01/remote/RCS/apl_locs.p,v 1.9 2000/02/24 20:07:30 tanya Exp $"
.

DEF INPUT PARAMETER i_arrow       AS INT   NO-UNDO .
DEF INPUT PARAMETER ch_format     AS CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_value_locs AS CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_printer      AS CHAR  NO-UNDO .
DEF INPUT PARAMETER i_type        AS INT   NO-UNDO .


DEF VAR i_num_ent AS INT  NO-UNDO        .
DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
DEF VAR ch_loc    AS CHAR NO-UNDO        .
DEF VAR ch_print  AS CHAR NO-UNDO        .
DEF VAR ch_file   AS CHAR NO-UNDO        .


MESSAGE "Starting Printing..." .


ASSIGN
   i_num_ent = NUM-ENTRIES ( ch_value_locs )
   .

IF i_num_ent = 0 THEN
DO:
   MESSAGE 'No location label list passed for printing...' .
   
   RETURN .
END.
      
IF ( ch_format = ? ) OR ERROR-STATUS:ERROR THEN
   ASSIGN
      ch_format = ''  . 


IF ch_printer = ? THEN
DO:
   MESSAGE 'No UNIX printer queue specified...' .
   RETURN .
END.

/***
**   Build print File 
***/

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LOC", ".txt", output ch_file ) .

DO WHILE i_num_ent >= i_entry :
 
   ASSIGN
      ch_loc  = ENTRY ( i_entry , ch_value_locs ) .
      i_entry = i_entry + 1 .                          
   MESSAGE 'Generating ' ch_loc  'Label...'.   
      
   RUN print_label ( ch_loc , ch_file ) .
END.

/***
**  Print the File
***/
message "About to print location labels:" ch_file .

if (opsys eq "UNIX")
then do:
    ASSIGN
        ch_print = "lp -d" + ch_printer + " " + ch_file .
    
    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .

    pause 5 no-message .

    OS-DELETE         VALUE ( ch_file    ) .
end .
else do: /*NT*/

    /************************* old code using spooler ***************** 
       
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_printer
        .

        message "Copying" ch_file "to" ch_spool_dir .
        
        os-copy value(ch_file) value(ch_spool_dir) .
        os-delete value(ch_file) .
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .
    ***********************************************************/
    
    
    message "copying file  " ch_file "to printer " ch_printer .

    OS-COPY VALUE( ch_file) VALUE( ch_printer) .

    OS-DELETE  VALUE ( ch_file ) .   

   
end .

/* the end */
return .



/* <<<<<<<<<<<<<<<<<<<<<< PROCEDURES >>>>>>>>>>>>>>>>>>>>> */

PROCEDURE print_label:                         
    DEF INPUT PARAMETER ch_loc   AS CHAR NO-UNDO .
    DEF INPUT PARAMETER ch_file  AS CHAR NO-UNDO .

    DEF VAR             ch_value AS CHAR NO-UNDO .

    ASSIGN
       ch_loc = CAPS ( ch_loc ) 
       .
       
    RUN format_string ( ch_loc, OUTPUT ch_value ) .

    OUTPUT TO VALUE ( ch_file )  APPEND. 
    CASE i_type :
       WHEN 1 THEN   /* 1x4 Location Label... */
       DO:          
          PUT UNFORMATTED
             "m i~n"
             "J~n"
             "Sl1;0,0.05,0.9,0.95,4.0~n"
             "B 2.40,0.20,0,code128,0.65,0.01;" + ch_loc + "~n"
             "T 0.0,0.75,0,5,0.65,h.20;" + ch_value + "~n"
             "A 1~n".
           
        END .
              
       WHEN 2 THEN       
       DO:   /* 1x4  Location label printed on 2x4 label size */
          PUT UNFORMATTED
             "m i~n"
             "J~n"
             "Sl1;0,0.05,2.00,2.10,4.0~n"
             "G 2.10,0,270;  L:0.9,0.02~n"
             "B 2.40,0.20,0,code128,0.65,0.01;" + ch_loc + "~n"
             "T 0.0,0.75,0,5,0.65,h.20;" + ch_value + "~n"
             "A 1~n".
       END.       
       
       WHEN 3 THEN
       DO:   /* LABEL SIZE 2X4 */

          PUT UNFORMATTED 
             "m i~n"                           
             "J~n"                             
             "H 5.2,5,T,R0~n"                  
             "Sl1;0,0.05,2.00,2.10,4.0~n"      
             "G 0.00,1.00,0;  L:3.7,0.02~n".               
          
          CASE i_arrow:   /* ARROW UP*/
              WHEN 1 THEN
              DO:
                 PUT UNFORMATTED
                    "G 3.00,1.00,90; L:1.0,0.02~n"      
                    "G 3.40,0.20,0;  L:0.01,0.01~n"     
                    "G 3.39,0.21,0;  L:0.03,0.01~n"     
                    "G 3.38,0.22,0;  L:0.05,0.01~n"     
                    "G 3.37,0.23,0;  L:0.07,0.01~n"     
                    "G 3.36,0.24,0;  L:0.09,0.01~n"     
                    "G 3.35,0.25,0;  L:0.11,0.01~n"     
                    "G 3.34,0.26,0;  L:0.13,0.01~n"     
                    "G 3.33,0.27,0;  L:0.15,0.01~n"     
                    "G 3.32,0.28,0;  L:0.17,0.01~n"     
                    "G 3.31,0.29,0;  L:0.19,0.01~n"     
                    "G 3.30,0.30,0;  L:0.21,0.01~n"     
                    "G 3.29,0.31,0;  L:0.23,0.01~n"     
                    "G 3.28,0.32,0;  L:0.25,0.01~n"     
                    "G 3.27,0.33,0;  L:0.27,0.01~n"     
                    "G 3.26,0.34,0;  L:0.29,0.01~n"     
                    "G 3.25,0.35,0;  L:0.31,0.01~n"     
                    "G 3.24,0.36,0;  L:0.33,0.01~n"     
                    "G 3.23,0.37,0;  L:0.35,0.01~n"     
                    "G 3.22,0.38,0;  L:0.37,0.01~n"     
                    "G 3.21,0.39,0;  L:0.39,0.01~n"     
                    "G 3.20,0.40,0;  L:0.41,0.01~n"     
                    "G 3.19,0.41,0;  L:0.43,0.01~n"     
                    "G 3.18,0.42,0;  L:0.45,0.01~n"     
                    "G 3.17,0.43,0;  L:0.47,0.01~n"     
                    "G 3.16,0.44,0;  L:0.49,0.01~n"     
                    "G 3.15,0.45,0;  L:0.51,0.01~n"     
                    "G 3.265,0.65,0; L:0.28,0.40~n"  .    
              END.
               
               
               WHEN 2 THEN   /* ARROW DOWN */
               DO:
                  PUT UNFORMATTED
                     "G 3.00,1.00,90; L:1.0,0.02~n"      
                     "G 3.265,0.35,0; L:0.28,0.40~n"     
                     "G 3.40,0.80,0;  L:0.01,0.01~n"     
                     "G 3.39,0.79,0;  L:0.03,0.01~n"     
                     "G 3.38,0.78,0;  L:0.05,0.01~n"     
                     "G 3.37,0.77,0;  L:0.07,0.01~n"     
                     "G 3.36,0.76,0;  L:0.09,0.01~n"     
                     "G 3.35,0.75,0;  L:0.11,0.01~n"     
                     "G 3.34,0.74,0;  L:0.13,0.01~n"     
                     "G 3.33,0.73,0;  L:0.15,0.01~n"     
                     "G 3.32,0.72,0;  L:0.17,0.01~n"     
                     "G 3.31,0.71,0;  L:0.19,0.01~n"     
                     "G 3.30,0.70,0;  L:0.21,0.01~n"     
                     "G 3.29,0.69,0;  L:0.23,0.01~n"     
                     "G 3.28,0.68,0;  L:0.25,0.01~n"     
                     "G 3.27,0.67,0;  L:0.27,0.01~n"     
                     "G 3.26,0.66,0;  L:0.29,0.01~n"     
                     "G 3.25,0.65,0;  L:0.31,0.01~n"     
                     "G 3.24,0.64,0;  L:0.33,0.01~n"     
                     "G 3.23,0.63,0;  L:0.35,0.01~n"    
                     "G 3.22,0.62,0;  L:0.37,0.01~n"   
                     "G 3.21,0.61,0;  L:0.39,0.01~n"     
                     "G 3.20,0.60,0;  L:0.41,0.01~n"     
                     "G 3.19,0.59,0;  L:0.43,0.01~n"     
                     "G 3.18,0.58,0;  L:0.45,0.01~n"     
                     "G 3.17,0.57,0;  L:0.47,0.01~n"     
                     "G 3.16,0.56,0;  L:0.49,0.01~n"     
                     "G 3.15,0.55,0;  L:0.51,0.01~n" .           
               END.
            END CASE .
      
            PUT UNFORMATTED       /*PRINT LOCATION AND BAR CODE */
             
               "T 0.0,0.15,0,5,0.17,h.11;LOCATION:~n"              
               "T 0.0,0.8,0,5,0.65,h.30;" + ch_value + "~n"        
               "B 0.20,1.10,0,code128,0.65,0.023;" + ch_loc + "~n" 
               "A 1~n" . 
               
          END.
      WHEN 4 THEN       /* LABEL SIZE 4X6 */
      DO:
         PUT UNFORMATTED 
            "m i~n"                                         
            "J~n"                                           
            "H 5.2,5,T,R0~n"                                
            "O R~n"                                         
            "S l1;.0,.0,6,6.10,4.0~n" 
            "G 2.20,0.05,270;  L:5.95,0.02~n".                         
           
             
             CASE i_arrow :
               WHEN 1 THEN     /* ARROW UP */

                  PUT UNFORMATTED

                     "G 2.20,4.30,0;    L:1.75,0.02~n"

                     "G 3.78,5.15,270;  L:0.01,0.0125~n"              
                     "G 3.77,5.14,270;  L:0.03,0.0125~n"              
                     "G 3.76,5.13,270;  L:0.05,0.0125~n"              
                     "G 3.75,5.12,270;  L:0.07,0.0125~n"              
                     "G 3.74,5.11,270;  L:0.09,0.0125~n"              
                     "G 3.73,5.10,270;  L:0.11,0.0125~n"              
                     "G 3.72,5.09,270;  L:0.13,0.0125~n"              
                     "G 3.71,5.08,270;  L:0.15,0.0125~n"              
                     "G 3.70,5.07,270;  L:0.17,0.0125~n"              
                     "G 3.69,5.06,270;  L:0.19,0.0125~n"              
                     "G 3.68,5.05,270;  L:0.21,0.0125~n"              
                     "G 3.67,5.04,270;  L:0.23,0.0125~n"              
                     "G 3.66,5.03,270;  L:0.25,0.0125~n"              
                     "G 3.65,5.02,270;  L:0.27,0.0125~n"              
                     "G 3.64,5.01,270;  L:0.29,0.0125~n"              
                     "G 3.63,5.00,270;  L:0.31,0.0125~n"              
                     "G 3.62,4.99,270;  L:0.33,0.0125~n"              
                     "G 3.61,4.98,270;  L:0.35,0.0125~n"              
                     "G 3.60,4.97,270;  L:0.37,0.0125~n"              
                     "G 3.59,4.96,270;  L:0.39,0.0125~n"              
                     "G 3.58,4.95,270;  L:0.41,0.0125~n"              
                     "G 3.57,4.94,270;  L:0.43,0.0125~n"              
                     "G 3.56,4.93,270;  L:0.45,0.0125~n"              
                     "G 3.55,4.92,270;  L:0.47,0.0125~n" 
                     "G 3.54,4.91,270;  L:0.49,0.0125~n" 
                     "G 3.53,4.90,270;  L:0.51,0.0125~n" 
                     "G 3.52,4.89,270;  L:0.53,0.0125~n"              
                     "G 3.51,4.88,270;  L:0.55,0.0125~n" 
                     "G 3.50,4.87,270;  L:0.57,0.0125~n"              
                     "G 3.49,4.86,270;  L:0.59,0.0125~n"              
                     "G 3.48,4.85,270;  L:0.61,0.0125~n"              
                     "G 3.47,4.84,270;  L:0.63,0.0125~n"              
                     "G 3.46,4.83,270;  L:0.65,0.0125~n" 
                     "G 3.45,4.82,270;  L:0.67,0.0125~n"              
                     "G 3.44,4.81,270;  L:0.69,0.0125~n"              
                     "G 3.43,4.80,270;  L:0.71,0.0125~n" 
                     "G 3.42,4.79,270;  L:0.73,0.0125~n"              
                     "G 3.41,4.78,270;  L:0.75,0.0125~n"              
                     "G 3.40,4.77,270;  L:0.77,0.0125~n" 
                     "G 3.39,4.76,270;  L:0.79,0.0125~n"
                     "G 3.38,4.75,270;  L:0.81,0.0125~n"              
                     "G 3.37,4.74,270;  L:0.83,0.0125~n"              
                     "G 3.36,4.73,270;  L:0.85,0.0125~n"              
                     "G 3.35,4.72,270;  L:0.87,0.0125~n"              
                     "G 3.34,4.71,270;  L:0.89,0.0125~n"              
                     
                     "G 3.495,4.865,270; L:0.58,0.0125~n"
                     "G 2.97,4.925,270; L:0.46,0.74~n" .               
                              


              WHEN 2 THEN      /*ARROW DOWN*/

                  PUT UNFORMATTED

                     "G 2.20,4.30,0;    L:1.75,0.02~n"              
                     "G 2.60,5.15,270;  L:0.01,0.0125~n"               
                     "G 2.61,5.14,270;  L:0.03,0.0125~n"               
                     "G 2.62,5.13,270;  L:0.05,0.0125~n"               
                     "G 2.63,5.12,270;  L:0.07,0.0125~n"               
                     "G 2.64,5.11,270;  L:0.09,0.0125~n"               
                     "G 2.65,5.10,270;  L:0.11,0.0125~n"               
                     "G 2.66,5.09,270;  L:0.13,0.0125~n"               
                     "G 2.67,5.08,270;  L:0.15,0.0125~n"               
                     "G 2.68,5.07,270;  L:0.17,0.0125~n"               
                     "G 2.69,5.06,270;  L:0.19,0.0125~n"               
                     "G 2.70,5.05,270;  L:0.21,0.0125~n"               
                     "G 2.71,5.04,270;  L:0.23,0.0125~n"               
                     "G 2.72,5.03,270;  L:0.25,0.0125~n"               
                     "G 2.73,5.02,270;  L:0.27,0.0125~n"               
                     "G 2.74,5.01,270;  L:0.29,0.0125~n"               
                     "G 2.75,5.00,270;  L:0.31,0.0125~n"               
                     "G 2.76,4.99,270;  L:0.33,0.0125~n"               
                     "G 2.77,4.98,270;  L:0.35,0.0125~n"               
                     "G 2.78,4.97,270;  L:0.37,0.0125~n"               
                     "G 2.79,4.96,270;  L:0.39,0.0125~n"               
                     "G 2.80,4.95,270;  L:0.41,0.0125~n"              
                     "G 2.81,4.94,270;  L:0.43,0.0125~n"               
                     "G 2.82,4.93,270;  L:0.45,0.0125~n"               
                     "G 2.83,4.92,270;  L:0.47,0.0125~n"               
                     "G 2.84,4.91,270;  L:0.49,0.0125~n"               
                     "G 2.85,4.90,270;  L:0.51,0.0125~n"               
                     "G 2.86,4.89,270;  L:0.53,0.0125~n"               
                     "G 2.87,4.88,270;  L:0.55,0.0125~n"               
                     "G 2.88,4.87,270;  L:0.57,0.0125~n"               
                     "G 2.89,4.86,270;  L:0.59,0.0125~n"               
                     "G 2.90,4.85,270;  L:0.61,0.0125~n"               
                     "G 2.91,4.84,270;  L:0.63,0.0125~n"               
                     "G 2.92,4.83,270;  L:0.65,0.0125~n"               
                     "G 2.93,4.82,270;  L:0.67,0.0125~n"               
                     "G 2.94,4.81,270;  L:0.69,0.0125~n"               
                     "G 2.95,4.80,270;  L:0.71,0.0125~n"               
                     "G 2.96,4.79,270;  L:0.73,0.0125~n"               
                     "G 2.97,4.78,270;  L:0.75,0.0125~n"               
                     "G 2.98,4.77,270;  L:0.77,0.0125~n"               
                     "G 2.99,4.76,270;  L:0.79,0.0125~n"               
                     "G 3.00,4.75,270;  L:0.81,0.0125~n"               
                     "G 3.01,4.74,270;  L:0.83,0.0125~n"               
                     "G 3.02,4.73,270;  L:0.85,0.0125~n"              
                     "G 3.03,4.72,270;  L:0.87,0.0125~n"               
                     "G 3.04,4.71,270;  L:0.89,0.0125~n" 
                                   
                     "G 2.835,4.925,270; L:0.46,0.0125~n"              
                     "G 3.41,4.925,270; L:0.46,0.74~n" .
              END CASE .
          
            /* PRINT LOCATION AND BAR CODE */
                  
          PUT UNFORMATTED
             "T 3.70,0.25,270,5,0.35,h.18;LOCATION:~n"           
             "T 2.60,0.30,270,5,1.00,h.40;" + ch_value + "~n"    
             "B 2.0,0.40,270,code128,1.7,0.04;" + ch_loc + "~n"  
             "A 1~n" .
      END.   
   END CASE .
    
END PROCEDURE.


PROCEDURE format_string :
/* -----------------------------------------------------------
  Purpose:    Format a location string ...
  Parameters: Old Location (in) New formated Location (out)
  Notes:
-------------------------------------------------------------*/
 DEF INPUT  PARAMETER ch_loc    AS CHAR NO-UNDO.
 DEF OUTPUT PARAMETER ch_newloc AS CHAR NO-UNDO .
     
 DEF VAR ch_build    AS CHAR NO-UNDO .
 DEF VAR i_charpos   AS INT  NO-UNDO .
 DEF VAR i_numdashes AS INT  NO-UNDO .
 DEF VAR i_length    AS INT  NO-UNDO .
 DEF VAR i_count     AS INT  NO-UNDO .
                         
   ASSIGN
      i_numdashes = NUM-ENTRIES ( ch_format )
      ch_newloc   = ch_loc          
      .
      
   IF i_numdashes < 1 THEN
      RETURN .
               
   buildloop:
   REPEAT WHILE i_count < i_numdashes :
                     
      ASSIGN
         i_count   = i_count + 1
         i_length  = LENGTH ( ch_newloc )
         i_charpos = INT ( ENTRY (  i_count , ch_format ) )
         NO-ERROR .
    
         IF ERROR-STATUS:ERROR THEN
         DO:
            MESSAGE "Problem with format string.  Please validate the format" +              "string in system parameters."
            .
            RETURN .
         END .
                                         
         IF i_charpos > i_length THEN
            NEXT buildloop .
                                                        
         ASSIGN
            ch_newloc = SUBSTRING ( ch_newloc, 1, i_charpos - 1 ) + '-' +
            SUBSTRING ( ch_newloc, i_charpos )
            .
      END.
                                             
END PROCEDURE.
                                                                  
