/**=================================================================**
* S:\IRMS.NET.2.5.2\irms.net\BE\BatchProcess\BatchBuild\BatchBuild_Post.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 08/19/09, 16:30 PM
**=================================================================**/


/* Business Entity Definintions */
{BatchProcess/BatchBuild/BatchBuild_ds.i}
{BatchProcess/BatchBuild/BatchBuild_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Context .
    DEF INPUT-OUTPUT PARAM DATASET FOR dsBatchBuild .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN DO:
        CREATE ds_Control.
        ASSIGN ds_Control.PropName = 'COMMAND'
               ds_Control.PropValue = 'POST'.
    END.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


