/***    Assign carton ID for the order when the order was dropped ***/
/**
***   File  : ctn_ntw.p 
***
***   Desc  : Do cartonization for the order .      
***
***   Author: <PERSON>  
***
***   DAte  : July 28, 2000   7:45
***
***   INPUT : order_ID                                  
***
***   OUTPUT: None
***/
/* <<<<<<<<<< SOURCE CONTROL ID >>>>>>>>>> */
define variable SCCS_ID
    as character
    no-undo
    initial "@(#) $Header: $~n"
.

    define input parameter ord_id            as integer   no-undo .
    
    /** *** buffer defination **/
    define buffer bf_ord for irms.ordhdr.
    define buffer bf_pic for irms.pick  .
    define buffer bf_ctn for irms.cartonmst .
    define buffer bf_dtl for irms.cartondtl .

    /** *** Loacl variable **/
    define variable my_carton as character no-undo .

    /** *** Main block **/
    /* check for the order */
    if ( ord_id EQ ? ) OR (ord_id EQ 0)
    then do:
        message "Invalid Order ID. " .
        return .
    end .
    find first bf_ord no-lock 
        where 
            bf_ord.id = ord_id 
        no-error
    .
    if NOT available (bf_ord)
    then do:
        message "The order is not exist in database." .
        return .
    end .
    if bf_ord.order_status EQ "S" 
    then do:
        message "The order has been shipped ." .
        return .
    end .

    /** get carton ID ***/
    run get_ctn_id (output my_carton ) .

    /** Create Carton ****/
    run create_ctn (input my_carton) .

    /*** Update pick ****/ 
    run update_pick (input my_carton ) .

/* end Job */
/**<<<<<<< PROCEDURE >>>>>>>**/
PROCEDURE get_ctn_id :
    define output parameter the_ctn as character no-undo . 

    define buffer my_ord for irms.ordhdr    .
    define buffer my_ctn for irms.cartonmst .

    define variable my_co_num  as character no-undo . 
    define variable my_wh_num  as character no-undo . 
    find first my_ord no-lock 
        where 
            my_ord.id = ord_id 
        no-error
    .
    repeat:
        assign 
            the_ctn = "C" + 
                    string(next-value(cartonmst_carton_id), "999999999") 
        .
        find first my_ctn no-lock 
            where 
                my_ctn.co_num = my_ord.co_num and 
                my_ctn.wh_num = my_ord.wh_num and 
                my_ctn.carton_id = the_ctn 
            no-error
        .
        if NOT available (my_ctn) 
        then do:
            leave .
        end .
    end .
END PROCEDURE .  /** get_ctn_id ***/

PROCEDURE create_ctn :
    define input parameter the_ctn as character no-undo . 

    define buffer my_ord for irms.ordhdr    .
    define buffer my_ctn for irms.cartonmst .

    find first my_ord no-lock 
        where 
            my_ord.id = ord_id 
        no-error
    .
    find first my_ctn no-lock 
        where 
            my_ctn.co_num = my_ord.co_num and 
            my_ctn.wh_num = my_ord.wh_num and 
            my_ctn.carton_id = the_ctn 
        no-error
    .
    if available (my_ctn) 
    then do:
        return .
    end .
    create my_ctn no-error .

    assign 
        my_ctn.co_num       = my_ord.co_num 
        my_ctn.wh_num       = my_ord.wh_num 
        my_ctn.carton_id    = the_ctn 
        my_ctn.order        = my_ord.order
        my_ctn.order_suffix = my_ord.order_suffix
        my_ctn.batch        = my_ord.batch 
        my_ctn.cust_code    = my_ord.cust_code 
        my_ctn.carrier_id   = my_ord.carrier + "/" + my_ord.service
    .
    release my_ctn no-error .

END PROCEDURE .  /** create_ctn ***/

PROCEDURE update_pick :
    define input parameter the_ctn as character no-undo . 

    define buffer my_ord for irms.ordhdr    .
    define buffer my_ctn for irms.cartonmst .
    define buffer my_pic for irms.pick .     
    define buffer my_dtl for irms.cartondtl .  

    find first my_ord no-lock 
        where 
            my_ord.id = ord_id 
        no-error
    .
    find first my_ctn no-lock 
        where 
            my_ctn.co_num = my_ord.co_num and 
            my_ctn.wh_num = my_ord.wh_num and 
            my_ctn.carton_id = the_ctn 
        no-error
    .
    for each my_pic 
        where 
            my_pic.co_num = my_ord.co_num and 
            my_pic.wh_num = my_ord.wh_num and 
            my_pic.order  = my_ord.order  and 
            my_pic.order_suffix = my_ord.order_suffix 
    :
        if NOT can-find (first my_dtl 
            where 
                my_dtl.carton_num = my_ctn.carton_num and 
                my_dtl.pick_id    = my_pic.id )
        then do:
            create my_dtl  no-error .
            assign 
                my_dtl.carton_num = my_ctn.carton_num 
                my_dtl.abs_num    = my_pic.abs_num 
                my_dtl.qty        = my_pic.qty 
                my_dtl.lot        = my_pic.lot 
                my_dtl.bin_num    = my_pic.bin_num 
                my_dtl.pick_id    = my_pic.id 
            .
        end .
        assign my_pic.carton_id = my_ctn.carton_id  .
    end .
END PROCEDURE .  /** update_pick **/
