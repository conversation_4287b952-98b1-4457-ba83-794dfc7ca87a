/*** 
** Prints Item Labels on Zebra  printer
** File name is hard coded in lbl_prnt.w .
***/

DEF VAR SCCS_ID  AS CHAR NO-UNDO INIT "@(#) $Header: /pdb/9.01/remote/RCS/zeb_recv.p,v 1.3 2000-02-24 10:50:15-06 tanya Exp $~n" .
                     

&SCOP DEBUG FALSE 

DEF INPUT PARAMETER ch_abs_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_descr         AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_po_num        AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_rec_date      AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_emp_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_lot           AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_loc           AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_ser_num       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_lbl_size      AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER ch_printer       AS  CHAR  NO-UNDO .
DEF INPUT PARAMETER i_qty            AS  INT   NO-UNDO .


DEF VAR ch_type   AS CHAR NO-UNDO INIT ? .
DEF VAR ch_list   AS CHAR NO-UNDO INIT ? .
DEF VAR ch_que    AS CHAR NO-UNDO INIT ? .
DEF VAR ch_arrow  AS CHAR NO-UNDO INIT ? .
DEF VAR ch_format AS CHAR NO-UNDO INIT ? . 
DEF VAR i_type    AS INT  NO-UNDO INIT ? .
DEF VAR i_arrow   AS INT  NO-UNDO INIT ? .
DEF VAR i_num_ent AS INT  NO-UNDO        .
DEF VAR i_entry   AS INT  NO-UNDO INIT 1 .
DEF VAR ch_print  AS CHAR NO-UNDO        .
DEF VAR ch_file   AS CHAR NO-UNDO        .

MESSAGE "Starting Printing..." .

/* Get temp file to print out ... */
RUN adecomm/_tmpfile.p ( "LBL", ".txt", output ch_file ) .


/***
**   Build print File 
*/

DO WHILE i_qty >= i_entry :

   ASSIGN
      i_entry = i_entry + 1 .                          

   MESSAGE 'Generating ' ch_abs_num  'Label...'.
      
   RUN print_label.
END.


/***
**  Print the File
***/
message "About to print item labels:" ch_file .

if (opsys eq "UNIX")  
then do:
    ASSIGN
        ch_print = "lp -c -d " + ch_printer + " " + ch_file .

    message "Running command:" ch_print .

    OS-COMMAND SILENT VALUE ( ch_print ) .
    
    pause 5 no-message.
    
    OS-DELETE  VALUE ( ch_file    ) .
end .

else do: /*NT*/

 /*************************** old code using spooler *********************
 
    define variable ch_spool_dir as character no-undo .

    assign
        ch_spool_dir = os-getenv("IRMS_SPOOLER")
    .

    if (ch_spool_dir gt "")
    then do:
        assign
            ch_spool_dir = ch_spool_dir + "/" + ch_que
        .

        message "Copying" ch_file "to" ch_spool_dir .
    
        os-copy value(ch_file) value(ch_spool_dir) .
        
        os-delete value(ch_file) .
            
    end .
    else do:
        message "IRMS_SPOOLER variable is not defined." .
        return error .
    end .

   *******************************************************************/
   
      
    message "copying file  " ch_file "to printer " ch_printer .

    OS-COPY VALUE( ch_file) VALUE( ch_printer) .

    OS-DELETE  VALUE ( ch_file ) .   

end .
     
 /* the end */
  return .    

PROCEDURE print_label:                         
   DEF VAR  ch_value AS CHAR NO-UNDO .


   OUTPUT TO VALUE ( ch_file )  APPEND. 
    CASE ch_lbl_size :

       WHEN "2x4" THEN   
       DO:
  
         PUT UNFORMATTED  
               "^XA^CFD^FS~n"
               
               "^FO3,10~n"
               "^GB792,394,4^FS~n"
               "^FO3,197~n"
               "^GB792,0,4^FS~n"
               "^FO3,263~n"
               "^GB792,0,4^FS~n"
               "^FO3,329~n"
               "^GB792,0,4^FS~n"
               "^FO287,263~n"
               "^GB0,66,4^FS~n"
               "^FO399,329~n"
               "^GB0,70,4^FS~n"
               "^FO615,263~n"
               "^GB0,67,4^FS~n"
               
               "^FO16,23~n"
               "^A0N,20,25^FDItem Number:^FS~n"
               "^FO190,20~n"
               "^A0N,30,35^FD" + ch_abs_num + "^FS~n"
               "^BY2.0~n"
               "^FO95,55~n"
               "^BCN,130,N,N,N^FD" + ch_abs_num + "^FS~n"
               "^FO16,204~n"
               "^A0N,20,25^FDDescription:^FS~n"
               "^FO26,238~n"
               "^A0N,30,35^FD" + ch_descr + "^FS~n"
               "^FO16,274~n"
               "^A0N,20,25^FDReceived  #:^FS~n"
               "^FO27,304~n"
               "^A0N,30,35^FD" + ch_po_num + "^FS~n"
               "^FO300,274~n"
               "^A0N,20,25^FDReceived Date:^FS~n"
               "^FO311,304~n"
               "^A0N,30,35^FD" + ch_rec_date + "^FS~n"
               "^FO628,274~n"
               "^A0N,20,25^FDRecieved By:^FS~n"
               "^FO638,304~n"
               "^A0N,30,35^FD" + ch_emp_num + "^FS~n"
               "^FO16,340~n"
               "^A0N,20,25^FDLot #:^FS~n"
               "^FO26,374~n"
               "^A0N,25,25^FD" + ch_lot + "^FS~n"
               "^FO415,340~n"
               "^A0N,20,25^FDSerial #:^FS~n"
               "^FO425,374~n"
               "^A0N,25,25^FD" + ch_ser_num + "^FS~n"
               "^XZ".

            
       END .
       
       WHEN "2x3" THEN
       DO:
       
       END .
       
       WHEN "1x4" THEN
       DO:

       END .
       WHEN "4x6" THEN
       DO:

       END .
       
    END CASE .

END PROCEDURE.

