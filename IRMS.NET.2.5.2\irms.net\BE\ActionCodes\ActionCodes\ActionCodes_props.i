/**=================================================================**
* Y:\BE_Area\src\be\ActionCodes\ActionCodes\ActionCodes_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:31 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qreturnaction FOR returnaction SCROLLING . 
QUERY qreturnaction:QUERY-PREPARE("FOR EACH returnaction").
QUERY qreturnaction:QUERY-OPEN.


DEF  QUERY qreturnaction_BEFORE FOR returnaction_BEFORE SCROLLING . 
QUERY qreturnaction_BEFORE:QUERY-PREPARE("FOR EACH returnaction_BEFORE").
QUERY qreturnaction_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "returnaction" */
DEFINE BUFFER return_act_1 FOR irms.return_action.
DEFINE QUERY qSrcreturnaction
    FOR return_act_1
        SCROLLING.
DEFINE DATA-SOURCE returnaction
    FOR QUERY qSrcreturnaction
        return_act_1 KEYS (co_num,wh_num,code)        .
DATA-SOURCE returnaction:PREFER-DATASET = no.
DATA-SOURCE returnaction:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_returnaction                 AS  HANDLE              
    FIELD   hQry_returnaction                AS  HANDLE              
    FIELD   hQry_returnaction_BEFORE         AS  HANDLE              
    FIELD   returnaction_DataSourceHdl       AS  HANDLE              
    FIELD   returnaction_BatchSize           AS  INTEGER             
                                                 INIT 50
    FIELD   returnaction_Fill                AS  LOGICAL             
                                                 INIT yes
    FIELD   returnaction_CanRead             AS  LOGICAL             
                                                 INIT yes
    FIELD   returnaction_CanCreate           AS  LOGICAL             
                                                 INIT yes
    FIELD   returnaction_CanUpdate           AS  LOGICAL             
                                                 INIT yes
    FIELD   returnaction_CanDelete           AS  LOGICAL             
                                                 INIT yes
    FIELD   returnaction_Src_Names           AS  CHARACTER           
                                                 INIT ""
    FIELD   returnaction_Src_Hdls            AS  CHARACTER           
                                                 INIT ""
    FIELD   returnaction_CurrentSource       AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   returnaction_UniqueKey           AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   returnaction_returnaction_Map    AS  CHARACTER           
                                                 INIT ""
    FIELD   returnaction_returnaction_CF     AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   returnaction_returnaction_NoP    AS  CHARACTER           
                                                 INIT ""
    FIELD   returnaction_hdl                 AS  HANDLE              
    FIELD   returnaction_UseQuery            AS  LOGICAL             
                                                 INIT yes
    FIELD   returnaction_PostTable           AS  CHARACTER           
                                                 INIT "return_act_1"
    FIELD   returnaction_qhdl                AS  HANDLE              
    FIELD   returnaction_return_act_1_W      AS  CHARACTER           
                                                 INIT ""
    FIELD   returnaction_return_act_1_S      AS  CHARACTER           
                                                 INIT ""
    FIELD   returnaction_Buffs               AS  CHARACTER           
                                                 INIT "return_act_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "return_action,returnaction"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "returnaction,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsActionCodes:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_returnaction                 = QUERY qreturnaction:HANDLE
       htt_returnaction                  = TEMP-TABLE returnaction:HANDLE
       hQry_returnaction_BEFORE          = QUERY qreturnaction_BEFORE:HANDLE
       returnaction_src_Names            = 'returnaction,Default'
       returnaction_src_Hdls             =         STRING(DATA-SOURCE returnaction:HANDLE)
                                           + ',' + STRING(DATA-SOURCE returnaction:HANDLE)
       returnaction_returnaction_Map     =         'code,return_act_1.code'
                                           + ',' + 'co_num,return_act_1.co_num'
                                           + ',' + 'description,return_act_1.description'
                                           + ',' + 'GUID,return_act_1.GUID'
                                           + ',' + 'wh_num,return_act_1.wh_num'
       returnaction_hdl                  = DATA-SOURCE returnaction:HANDLE
       returnaction_qhdl                 = QUERY qSrcreturnaction:HANDLE
       TopLevelTables                    = 'returnaction'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

    RUN LoadSuper ("blp/ActionCodes_blp.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsActionCodes .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsActionCodes BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsActionCodes .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsActionCodes BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_returnaction_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsActionCodes .
     RUN returnaction_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsActionCodes BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_returnaction_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsActionCodes .
     RUN returnaction_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsActionCodes BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_returnaction_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsActionCodes .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('returnaction') NO-ERROR .
     RUN returnaction_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsActionCodes BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_returnaction_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsActionCodes .
     RUN AfterRowFill  IN THIS-PROCEDURE ('returnaction') NO-ERROR .
     RUN returnaction_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsActionCodes BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


