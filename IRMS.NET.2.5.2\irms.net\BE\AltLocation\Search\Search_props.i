/**=================================================================**
* Y:\BE_Area\src\be\AltLocation\Search\Search_props.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:37 PM
**=================================================================**/


/********************************************************
* QUERIES ON TEMP-TABLES 
********************************************************/
DEF QUERY qttExtValues FOR ttExtValues SCROLLING.
QUERY qttExtValues:QUERY-PREPARE("FOR EACH ttExtValues").
QUERY qttExtValues:QUERY-OPEN.


DEF QUERY qttExtValues_BEFORE FOR ttExtValues_BEFORE SCROLLING.
QUERY qttExtValues_BEFORE:QUERY-PREPARE("FOR EACH ttExtValues_BEFORE").
QUERY qttExtValues_BEFORE:QUERY-OPEN.


DEF  QUERY qAltLocSearch FOR AltLocSearch SCROLLING . 
QUERY qAltLocSearch:QUERY-PREPARE("FOR EACH AltLocSearch").
QUERY qAltLocSearch:QUERY-OPEN.


DEF  QUERY qAltLocSearch_BEFORE FOR AltLocSearch_BEFORE SCROLLING . 
QUERY qAltLocSearch_BEFORE:QUERY-PREPARE("FOR EACH AltLocSearch_BEFORE").
QUERY qAltLocSearch_BEFORE:QUERY-OPEN.


DEF  QUERY qds_Filter  FOR      ds_Filter SCROLLING .
QUERY qds_Filter:QUERY-PREPARE("FOR EACH ds_Filter").
QUERY qds_Filter:QUERY-OPEN.


DEF  QUERY qds_Sort    FOR      ds_Sort   SCROLLING .
QUERY qds_Sort:QUERY-PREPARE("FOR EACH ds_Sort").
QUERY qds_Sort:QUERY-OPEN.


DEF  QUERY qds_Error   FOR      ds_Error  SCROLLING .
QUERY qds_Error:QUERY-PREPARE("FOR EACH ds_Error").
QUERY qds_Error:QUERY-OPEN.


DEF  QUERY qds_Control FOR      ds_Control  SCROLLING .
QUERY qds_Control:QUERY-PREPARE("FOR EACH ds_Control").
QUERY qds_Control:QUERY-OPEN.


DEF  QUERY qds_SchemaAttr FOR   ds_SchemaAttr  SCROLLING .
QUERY qds_SchemaAttr:QUERY-PREPARE("FOR EACH ds_SchemaAttr").
QUERY qds_SchemaAttr:QUERY-OPEN.


DEF QUERY qds_ExtFields FOR ds_ExtFields SCROLLING.
QUERY qds_ExtFields:QUERY-PREPARE("FOR EACH ds_ExtFields").
QUERY qds_ExtFields:QUERY-OPEN.


/********************************************************
* Data Sources 
********************************************************/

/* DATA-SOURCE: "AltLocSearch" */
DEFINE BUFFER binmst_1 FOR irms.binmst.
DEFINE QUERY qSrcAltLocSearch
    FOR binmst_1
        SCROLLING.
DEFINE DATA-SOURCE AltLocSearch
    FOR QUERY qSrcAltLocSearch
        binmst_1 KEYS (co_num,wh_num,bin_num)        .
DATA-SOURCE AltLocSearch:PREFER-DATASET = no.
DATA-SOURCE AltLocSearch:MERGE-BY-FIELD = yes.


/********************************************************
* PROPERTIES TEMP-TABLE DEFINITIONS
********************************************************/
DEF TEMP-TABLE BE_Props NO-UNDO
    FIELD   ContextID                        AS  CHARACTER           
                                                 FORMAT "x(30)"
                                                 INIT ""
    FIELD   Version                          AS  CHARACTER           
                                                 FORMAT "x(10)"
                                                 INIT "1.03.01"
    FIELD   DataSetOneTransaction            AS  LOGICAL             
                                                 INIT YES
    FIELD   DataSetHandle                    AS  HANDLE              
    FIELD   ds_Context                       AS  HANDLE              
    FIELD   ds_Schema                        AS  HANDLE              
    FIELD   dsContextHandle                  AS  HANDLE              
    FIELD   TrackingChanges                  AS  LOGICAL             
                                                 INIT NO
    FIELD   hQry_Filter                      AS  HANDLE              
    FIELD   hQry_Sort                        AS  HANDLE              
    FIELD   hQry_Error                       AS  HANDLE              
    FIELD   hQry_Control                     AS  HANDLE              
    FIELD   hQry_SchemaAttr                  AS  HANDLE              
    FIELD   hQry_ExtFields                   AS  HANDLE              
    FIELD   hQry_ttExtValues                 AS  HANDLE              
    FIELD   hQry_ttExtValues_BEFORE          AS  HANDLE              
    FIELD   DataRelation                     AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_ttExtValues                  AS  HANDLE              
    FIELD   htt_ttExtValues_BEFORE           AS  HANDLE              
    FIELD   DataRelationNames                AS  CHARACTER           
                                                 INIT ""
    FIELD   htt_AltLocSearch                 AS  HANDLE              
    FIELD   hQry_AltLocSearch                AS  HANDLE              
    FIELD   hQry_AltLocSearch_BEFORE         AS  HANDLE              
    FIELD   AltLocSearch_DataSourceHdl       AS  HANDLE              
    FIELD   AltLocSearch_BatchSize           AS  INTEGER             
                                                 INIT 50
    FIELD   AltLocSearch_Fill                AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocSearch_CanRead             AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocSearch_CanCreate           AS  LOGICAL             
                                                 INIT no
    FIELD   AltLocSearch_CanUpdate           AS  LOGICAL             
                                                 INIT no
    FIELD   AltLocSearch_CanDelete           AS  LOGICAL             
                                                 INIT no
    FIELD   AltLocSearch_Src_Names           AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocSearch_Src_Hdls            AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocSearch_CurrentSource       AS  CHARACTER           
                                                 INIT "DEFAULT"
    FIELD   AltLocSearch_UniqueKey           AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   AltLocSearch_AltLocSearch_Map    AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocSearch_AltLocSearch_CF     AS  CHARACTER           
                                                 INIT "GUID"
    FIELD   AltLocSearch_AltLocSearch_NoP    AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocSearch_hdl                 AS  HANDLE              
    FIELD   AltLocSearch_UseQuery            AS  LOGICAL             
                                                 INIT yes
    FIELD   AltLocSearch_PostTable           AS  CHARACTER           
                                                 INIT "binmst_1"
    FIELD   AltLocSearch_qhdl                AS  HANDLE              
    FIELD   AltLocSearch_binmst_1_W          AS  CHARACTER           
                                                 INIT "binmst_1.loc_type = 'M'"
    FIELD   AltLocSearch_binmst_1_S          AS  CHARACTER           
                                                 INIT ""
    FIELD   AltLocSearch_Buffs               AS  CHARACTER           
                                                 INIT "binmst_1"
    FIELD   DB_2_TT                          AS  CHARACTER           
                                                 INIT "binmst,AltLocSearch"
    FIELD   TempTableNames                   AS  CHARACTER           
                                                 INIT "AltLocSearch,ttExtValues"
    FIELD   TopLevelTables                   AS  CHARACTER           
                                                 INIT "x(40)"
    .

   CREATE BE_Props.

   ASSIGN
       THIS-PROCEDURE:ADM-DATA           = STRING(TEMP-TABLE BE_Props:DEFAULT-BUFFER-HANDLE)
       DataSetHandle                     = DATASET dsSearch:HANDLE
       ds_Context                        = DATASET ds_Context:HANDLE
       ds_Schema                         = DATASET ds_Schema:HANDLE
       dsContextHandle                   = DATASET ds_Context:HANDLE
       hQry_Filter                       = QUERY qds_Filter:HANDLE
       hQry_Sort                         = QUERY qds_Sort:HANDLE
       hQry_Error                        = QUERY qds_Error:HANDLE
       hQry_Control                      = QUERY qds_Control:HANDLE
       hQry_SchemaAttr                   = QUERY qds_SchemaAttr:HANDLE
       hQry_ExtFields                    = QUERY qds_ExtFields:HANDLE
       hQry_ttExtValues                  = QUERY qttExtValues:HANDLE
       hQry_ttExtValues_BEFORE           = QUERY qttExtValues_BEFORE:HANDLE
       hQry_AltLocSearch                 = QUERY qAltLocSearch:HANDLE
       htt_AltLocSearch                  = TEMP-TABLE AltLocSearch:HANDLE
       hQry_AltLocSearch_BEFORE          = QUERY qAltLocSearch_BEFORE:HANDLE
       AltLocSearch_src_Names            = 'AltLocSearch,Default'
       AltLocSearch_src_Hdls             =         STRING(DATA-SOURCE AltLocSearch:HANDLE)
                                           + ',' + STRING(DATA-SOURCE AltLocSearch:HANDLE)
       AltLocSearch_AltLocSearch_Map     =         'abc,binmst_1.abc'
                                           + ',' + 'abs_num,binmst_1.abs_num'
                                           + ',' + 'aisle,binmst_1.aisle'
                                           + ',' + 'bin_full,binmst_1.bin_full'
                                           + ',' + 'bin_hits,binmst_1.bin_hits'
                                           + ',' + 'bin_num,binmst_1.bin_num'
                                           + ',' + 'check_qty,binmst_1.check_qty'
                                           + ',' + 'co_num,binmst_1.co_num'
                                           + ',' + 'cube,binmst_1.cube'
                                           + ',' + 'depth,binmst_1.depth'
                                           + ',' + 'GUID,binmst_1.GUID'
                                           + ',' + 'height,binmst_1.height'
                                           + ',' + 'host_origin,binmst_1.host_origin'
                                           + ',' + 'loc_type,binmst_1.loc_type'
                                           + ',' + 'max_lvl,binmst_1.max_lvl'
                                           + ',' + 'max_pal,binmst_1.max_pal'
                                           + ',' + 'max_weight,binmst_1.max_weight'
                                           + ',' + 'min_lvl,binmst_1.min_lvl'
                                           + ',' + 'physical,binmst_1.physical'
                                           + ',' + 'prim_pick,binmst_1.prim_pick'
                                           + ',' + 'prim_pick_type,binmst_1.prim_pick_type'
                                           + ',' + 'rep_qty,binmst_1.rep_qty'
                                           + ',' + 'rep_unit,binmst_1.rep_unit'
                                           + ',' + 'row_status,binmst_1.row_status'
                                           + ',' + 'stack_height,binmst_1.stack_height'
                                           + ',' + 'wh_num,binmst_1.wh_num'
                                           + ',' + 'wh_zone,binmst_1.wh_zone'
                                           + ',' + 'width,binmst_1.width'
                                           + ',' + 'wood_flag,binmst_1.wood_flag'
       AltLocSearch_hdl                  = DATA-SOURCE AltLocSearch:HANDLE
       AltLocSearch_qhdl                 = QUERY qSrcAltLocSearch:HANDLE
       TopLevelTables                    = 'AltLocSearch'
       .


/********************************************************
* Pre-Loaded Logic 
********************************************************/
    RUN LoadSuper ("bussentity/be_super.p") .

/********************************************************
* Procedures... 
********************************************************/

PROCEDURE LoadSuper :
    DEF INPUT PARAMETER ipcSuper    AS  CHAR    NO-UNDO.

    DEF VAR hProc   AS  HANDLE  NO-UNDO.
    DEF VAR cProc   AS  CHAR    NO-UNDO.

    DEF VAR ripcsuper   AS  CHAR    NO-UNDO.

    DEF VAR i_numentries  AS  INT    NO-UNDO.

    assign i_numentries = num-entries(ipcsuper,".").

    assign ripcsuper = entry(i_numentries - 1,ipcsuper,".") + ".r".

    cProc = SEARCH(ripcSuper).
    IF cProc = ? THEN
    cProc = SEARCH(ipcSuper).
    IF cProc = ? THEN
        RETURN "ERROR".

    hProc = SESSION:FIRST-PROCEDURE.
    DO WHILE VALID-HANDLE(hProc)
         AND hProc:FILE-NAME <> cProc:
        hProc = hProc:NEXT-SIBLING.
    END.

    IF NOT VALID-HANDLE(hProc) THEN
        RUN VALUE(ipcSuper) PERSISTENT SET hProc .

    TARGET-PROCEDURE:ADD-SUPER-PROCEDURE(hProc,SEARCH-TARGET).

END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN DataSet_BeforeFill IN THIS-PROCEDURE 
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_DataSet_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN DataSet_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AltLocSearch_BeforeFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN AltLocSearch_BeforeFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AltLocSearch_AfterFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN AltLocSearch_AfterFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---=------------------------------------------------------- */

PROCEDURE callback_AltLocSearch_BeforeRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN BeforeRowFill  IN THIS-PROCEDURE ('AltLocSearch') NO-ERROR .
     RUN AltLocSearch_BeforeRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */

PROCEDURE callback_AltLocSearch_AfterRowFill  :
     DEFINE INPUT PARAM DATASET FOR dsSearch .
     RUN AfterRowFill  IN THIS-PROCEDURE ('AltLocSearch') NO-ERROR .
     RUN AltLocSearch_AfterRowFill  IN THIS-PROCEDURE
        (DATASET ds_Context BY-REFERENCE, 
         DATASET dsSearch BY-REFERENCE) 
         NO-ERROR .
END PROCEDURE.
/* ---------------------------------------------------------- */



/**************************** END OF FILE ****************************/


