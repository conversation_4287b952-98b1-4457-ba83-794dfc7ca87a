/**=================================================================**
* S:\IRMS.NET.2.6.0\irms.net\BE\ActionTable\ActionTable_First.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 09/19/08, 10:35 PM
**=================================================================**/


/* Business Entity Definintions */
{BE/ActionTable/ActionTable_ds.i}
{BE/ActionTable/ActionTable_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF       OUTPUT PARAM DATASET FOR dsActionTable .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN
        CREATE ds_Control.
    ASSIGN ds_Control.PropName = 'COMMAND'
           ds_Control.PropValue = 'FILL'.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


