"ttTablePropsCREATE"
"ttTablePropsTblTableNameAltLocation"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateyes"
"ttTablePropscanUpdateyes"
"ttTablePropscanDeleteyes"
"ttTablePropsUniqueKeyGUID"
"ttTablePropsorder0"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameabc"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelLocation Class"
"ttFieldPropsFldColLabelLocation Class"
"ttFieldPropsFldHelpEnter the Location Class"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameabs_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0.0"
"ttFieldPropsFldFormatx(24)"
"ttFieldPropsFldSideLabelItem Number"
"ttFieldPropsFldColLabelItem Number"
"ttFieldPropsFldHelpEnter the Item Number"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameaisle"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit1"
"ttFieldPropsFldFormat>>>9"
"ttFieldPropsFldSideLabelAisle"
"ttFieldPropsFldColLabelAisle"
"ttFieldPropsFldHelpEnter the Aisle"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamebin_full"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelbin_full"
"ttFieldPropsFldColLabelbin_full"
"ttFieldPropsFldHelpEnter the bin_full"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamebin_hits"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelBin Hits"
"ttFieldPropsFldColLabelBin Hits"
"ttFieldPropsFldHelpEnter the Bin Hits"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamebin_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(20)"
"ttFieldPropsFldSideLabelLocation Id"
"ttFieldPropsFldColLabelLocation Id"
"ttFieldPropsFldHelpEnter the Location Id"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamecheck_qty"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInityes"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelCheck Quantity"
"ttFieldPropsFldColLabelCheck Quantity"
"ttFieldPropsFldHelpEnter the Check Quantity"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the Company"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamecube"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>>>,>>>,>>9.99"
"ttFieldPropsFldSideLabelCube"
"ttFieldPropsFldColLabelCube"
"ttFieldPropsFldHelpEnter the Cube"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustom Fields"
"ttFieldPropsFldColLabelCustom Fields"
"ttFieldPropsFldHelpEnter the Custom Fields"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamedepth"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>9"
"ttFieldPropsFldSideLabelDepth"
"ttFieldPropsFldColLabelDepth"
"ttFieldPropsFldHelpEnter the Depth"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat999999999.999999999"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameheight"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>9"
"ttFieldPropsFldSideLabelHeight"
"ttFieldPropsFldColLabelHeight"
"ttFieldPropsFldHelpEnter the Height"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamehost_origin"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(25)"
"ttFieldPropsFldSideLabelHost Origin"
"ttFieldPropsFldColLabelHost Origin"
"ttFieldPropsFldHelpEnter the Host Origin"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameloc_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitP"
"ttFieldPropsFldFormatx"
"ttFieldPropsFldSideLabelLocation Type"
"ttFieldPropsFldColLabelLocation Type"
"ttFieldPropsFldHelpEnter the Location Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamemax_lvl"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0.0"
"ttFieldPropsFldFormat>>,>>9.99"
"ttFieldPropsFldSideLabelMaximum Level"
"ttFieldPropsFldColLabelmax lvl"
"ttFieldPropsFldHelpEnter the Maximum Level"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamemax_pal"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit1"
"ttFieldPropsFldFormat>>9"
"ttFieldPropsFldSideLabelMaximum Pallets"
"ttFieldPropsFldColLabelmax pal"
"ttFieldPropsFldHelpEnter the Maximum Pallets"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamemax_weight"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelMaximum Weight"
"ttFieldPropsFldColLabelmax weight"
"ttFieldPropsFldHelpEnter the Maximum Weight"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamemin_lvl"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0.0"
"ttFieldPropsFldFormat>>,>>9.99"
"ttFieldPropsFldSideLabelMinimum Level"
"ttFieldPropsFldColLabelmin lvl"
"ttFieldPropsFldHelpEnter the Minimum Level"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamepallet_footprint"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelPallet Footprint"
"ttFieldPropsFldColLabelPallet Footprint"
"ttFieldPropsFldHelpEnter the Pallet Footprint"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamephysical"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelPhysical"
"ttFieldPropsFldColLabelPhysical"
"ttFieldPropsFldHelpEnter the Physical"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameprim_pick"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelPrimary Pick"
"ttFieldPropsFldColLabelPrimary Pick"
"ttFieldPropsFldHelpEnter the Primary Pick"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNameprim_pick_type"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitS"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelP.P. Type"
"ttFieldPropsFldColLabelP.P. Type"
"ttFieldPropsFldHelpEnter the P.P. Type"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamerep_qty"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0.0"
"ttFieldPropsFldFormat>>,>>9.99"
"ttFieldPropsFldSideLabelRep Quantity"
"ttFieldPropsFldColLabelRep Quantity"
"ttFieldPropsFldHelpEnter the Rep Quantity"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamerep_unit"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitC"
"ttFieldPropsFldFormatX"
"ttFieldPropsFldSideLabelReplenish. Unit"
"ttFieldPropsFldColLabelReplenish. Unit"
"ttFieldPropsFldHelpEnter the Replenish. Unit"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamerow_status"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInityes"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelActive"
"ttFieldPropsFldColLabelActive"
"ttFieldPropsFldHelpEnter the Active"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamestack_height"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>>,>>9"
"ttFieldPropsFldSideLabelStacking Height"
"ttFieldPropsFldColLabelStacking Height"
"ttFieldPropsFldHelpEnter the Stacking Height"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamewh_zone"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatXX"
"ttFieldPropsFldSideLabelWarehouse Zone"
"ttFieldPropsFldColLabelWarehouse Zone"
"ttFieldPropsFldHelpEnter the Warehouse Zone"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamewidth"
"ttFieldPropsFldDataTypeinteger"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat>,>>9"
"ttFieldPropsFldSideLabelWidth"
"ttFieldPropsFldColLabelWidth"
"ttFieldPropsFldHelpEnter the Width"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNameAltLocation"
"ttFieldPropsFldNamewood_flag"
"ttFieldPropsFldDataTypelogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitno"
"ttFieldPropsFldFormatyes/no"
"ttFieldPropsFldSideLabelWood"
"ttFieldPropsFldColLabelWood"
"ttFieldPropsFldHelpEnter the Wood"
"ttFieldPropsFldMandatoryno"
"ttFieldPropsFldColumnSelectyes"
"ttDataSourceCREATE"
"ttDataSourceDSrcNameAltLocation"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTablebinmst_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNameAltLocation"
"ttDataSourceJoinscDSTableAltLocation"
"ttDataSourceJoinscDBTableirms.binmst"
"ttDataSourceJoinscBufNamebinmst_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num,bin_num"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNamey:\BE_Area\src\blp\AltLocation_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstno"
"ttOptionsmakeNextno"
"ttOptionsmakePrevno"
"ttOptionsmakeLastno"
"ttOptionsmakepostyes"
"ttOptionsmakeLoadyes"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionno"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTableAltLocation"
"ttAttachSourcecSrcNameAltLocation"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsabc,binmst_1.abc"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsabs_num,binmst_1.abs_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsaisle,binmst_1.aisle"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsbin_full,binmst_1.bin_full"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsbin_hits,binmst_1.bin_hits"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsbin_num,binmst_1.bin_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldscheck_qty,binmst_1.check_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsco_num,binmst_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldscube,binmst_1.cube"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsdepth,binmst_1.depth"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsGUID,binmst_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsheight,binmst_1.height"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldshost_origin,binmst_1.host_origin"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsloc_type,binmst_1.loc_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsmax_lvl,binmst_1.max_lvl"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsmax_pal,binmst_1.max_pal"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsmax_weight,binmst_1.max_weight"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsmin_lvl,binmst_1.min_lvl"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldspallet_footprint,binmst_1.pallet_footprint"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsphysical,binmst_1.physical"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsprim_pick,binmst_1.prim_pick"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsprim_pick_type,binmst_1.prim_pick_type"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsrep_qty,binmst_1.rep_qty"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsrep_unit,binmst_1.rep_unit"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsrow_status,binmst_1.row_status"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldsstack_height,binmst_1.stack_height"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldswh_num,binmst_1.wh_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldswh_zone,binmst_1.wh_zone"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldswidth,binmst_1.width"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTableAltLocation"
"ttAttachDtlcSrcNameAltLocation"
"ttAttachDtlMappedFieldswood_flag,binmst_1.wood_flag"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"