/**=================================================================**
* Y:\irms.net.1.3.1\irms.net\BE\Audit\Audit\Audit_ds.i : Bussiness Entity Definitions
*--------------------------------------------------------------------*
* Generated : 02/26/14, 15:56 PM
**=================================================================**/


/********************************************************
* DATASET TEMP-TABLE DEFINITIONS 
********************************************************/

DEF TEMP-TABLE ttExtValues NO-UNDO
    BEFORE-TABLE ttExtValues_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   FieldGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Field GUID"
                                                 LABEL "Field GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   TableGUID                        AS  DECIMAL             
                                                 COLUMN-LABEL "Table GUID"
                                                 LABEL "Table GUID"
                                                 FORMAT "999999999.999999999"
    FIELD   CustomValue                      AS  CHARACTER           
                                                 COLUMN-LABEL "Value"
                                                 FORMAT "x(60)"
                                                 INIT ""
    .
TEMP-TABLE ttExtValues:TRACKING-CHANGES = YES.

DEF TEMP-TABLE AuditLog NO-UNDO
    BEFORE-TABLE AuditLog_BEFORE

    FIELD   DB_ROWID                         AS  ROWID               
    FIELD   abs_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "abs_num"
                                                 LABEL "abs_num"
                                                 FORMAT "X(24)"
                                                 INIT ""
                                                 HELP "Enter the abs_num|no|yes"
    FIELD   action_code                      AS  CHARACTER           
                                                 COLUMN-LABEL "Action Code"
                                                 LABEL "Action Code"
                                                 FORMAT "x(8)"
                                                 INIT ""
                                                 HELP "Enter the Action Code|no|yes"
    FIELD   adj_code                         AS  CHARACTER           
                                                 COLUMN-LABEL "adj code"
                                                 LABEL "Adjustment Code"
                                                 FORMAT "x(6)"
                                                 INIT ""
                                                 HELP "Enter the Adjustment Code|no|yes"
    FIELD   batch                            AS  INTEGER             
                                                 COLUMN-LABEL "Wave"
                                                 LABEL "Wave"
                                                 FORMAT ">,>>>,>>9"
                                                 HELP "Enter the Wave|no|yes"
    FIELD   bin_from                         AS  CHARACTER           
                                                 COLUMN-LABEL "From Location"
                                                 LABEL "From Location"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the From Location|no|yes"
    FIELD   bin_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Location"
                                                 LABEL "Location"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Location|no|yes"
    FIELD   bin_to                           AS  CHARACTER           
                                                 COLUMN-LABEL "To Location"
                                                 LABEL "To Location"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the To Location|no|yes"
    FIELD   cancelled                        AS  LOGICAL             
                                                 COLUMN-LABEL "Cancelled"
                                                 LABEL "Cancelled"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the Cancelled|no|yes"
    FIELD   cancelled_at                     AS  CHARACTER           
                                                 COLUMN-LABEL "Cancelled At"
                                                 LABEL "Cancelled At"
                                                 FORMAT "9999-99-99 99:99"
                                                 INIT ""
                                                 HELP "Enter the Cancelled At|no|yes"
    FIELD   cancelled_by                     AS  CHARACTER           
                                                 COLUMN-LABEL "Cancelled By"
                                                 LABEL "Cancelled By"
                                                 FORMAT "x(6)"
                                                 INIT ""
                                                 HELP "Enter the Cancelled By|no|yes"
    FIELD   cargo_control                    AS  CHARACTER           
                                                 COLUMN-LABEL "Cargo Control"
                                                 LABEL "Cargo Control"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Cargo Control|no|yes"
    FIELD   carton_id                        AS  CHARACTER           
                                                 COLUMN-LABEL "Carton ID"
                                                 LABEL "Carton ID"
                                                 FORMAT "x(17)"
                                                 INIT ""
                                                 HELP "Enter the Carton ID|no|yes"
    FIELD   case_qty                         AS  INTEGER             
                                                 COLUMN-LABEL "Case Quantity"
                                                 LABEL "Case Quantity"
                                                 FORMAT ">>,>>9"
                                                 HELP "Enter the Case Quantity|no|yes"
    FIELD   cc_string                        AS  CHARACTER           
                                                 COLUMN-LABEL "Level of Cycle Count"
                                                 LABEL "Level of Cycle Count"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Level of Cycle Count|no|yes"
    FIELD   cc_type                          AS  CHARACTER           
                                                 COLUMN-LABEL "Type of Cycle Count"
                                                 LABEL "Type of Cycle Count"
                                                 FORMAT "X(8)"
                                                 INIT ""
                                                 HELP "Enter the Type of Cycle Count|no|yes"
    FIELD   comments                         AS  CHARACTER           
                                                 COLUMN-LABEL "comments"
                                                 LABEL "comments"
                                                 FORMAT "x(50)"
                                                 INIT ""
                                                 HELP "Enter the comments|no|yes"
    FIELD   co_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Company"
                                                 LABEL "Company"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Company|no|yes"
    FIELD   CustomFields                     AS  LOGICAL             
                                                 COLUMN-LABEL "Custom Fields"
                                                 LABEL "Custom Fields"
                                                 FORMAT "Yes/No"
                                                 INIT No
                                                 HELP "Enter the Custom Fields|no|yes"
    FIELD   date_time                        AS  CHARACTER           
                                                 COLUMN-LABEL "dt"
                                                 LABEL "Date and Time"
                                                 FORMAT "9999-99-99 99:99"
                                                 INIT ""
                                                 HELP "Enter the Date and Time|no|yes"
    FIELD   dept_num                         AS  INTEGER             
                                                 COLUMN-LABEL "Department Number"
                                                 LABEL "Department Number"
                                                 FORMAT ">>>>>9"
                                                 HELP "Enter the Department Number|no|yes"
    FIELD   doc_id                           AS  CHARACTER           
                                                 COLUMN-LABEL "Document ID"
                                                 LABEL "Document ID"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the Document ID|no|yes"
    FIELD   d_act_qty                        AS  DECIMAL             
                                                 COLUMN-LABEL "CF-Act Qty"
                                                 LABEL "CF-Act Qty"
                                                 FORMAT "->>>,>>9.99"
                                                 HELP "Enter the CF-Act Qty|no|yes"
    FIELD   d_exp_qty                        AS  DECIMAL             
                                                 COLUMN-LABEL "CF-Exp Qty"
                                                 LABEL "CF-Exp Qty"
                                                 FORMAT "->>>,>>9.99"
                                                 HELP "Enter the CF-Exp Qty|no|yes"
    FIELD   emp_num                          AS  CHARACTER           
                                                 COLUMN-LABEL "Employee Number"
                                                 LABEL "Employee Number"
                                                 FORMAT "x(12)"
                                                 INIT ""
                                                 HELP "Enter the Employee Number|no|yes"
    FIELD   exp_abs                          AS  CHARACTER           
                                                 COLUMN-LABEL "Expected Item"
                                                 LABEL "Expected Item"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Expected Item|no|yes"
    FIELD   extdCost                         AS  DECIMAL             
                                                 COLUMN-LABEL "Extended Cost"
                                                 LABEL "Extended Cost"
                                                 FORMAT "$>>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Extended Cost|no|yes"
    FIELD   GUID                             AS  DECIMAL             
                                                 COLUMN-LABEL "GUID"
                                                 LABEL "GUID"
                                                 FORMAT "999999999.999999999"
                                                 INIT 0
                                                 HELP "Enter the GUID|no|yes"
    FIELD   ifaces_file                      AS  CHARACTER           
                                                 COLUMN-LABEL "Ifaces File"
                                                 LABEL "Ifaces File"
                                                 FORMAT "X(20)"
                                                 INIT ""
                                                 HELP "Enter the Ifaces File|no|yes"
    FIELD   ifaces_time                      AS  CHARACTER           
                                                 COLUMN-LABEL "ifaces_time"
                                                 LABEL "ifaces_time"
                                                 FORMAT "9999-99-99 99:99"
                                                 INIT ""
                                                 HELP "Enter the ifaces_time|no|yes"
    FIELD   item_desc                        AS  CHARACTER           
                                                 COLUMN-LABEL "Item Description"
                                                 LABEL "Item Description"
                                                 FORMAT "x(30)"
                                                 INIT ""
                                                 HELP "Enter the Item Description|no|yes"
    FIELD   item_num                         AS  CHARACTER           
                                                 COLUMN-LABEL "item num"
                                                 LABEL "Catalog Number"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Catalog Number|no|yes"
    FIELD   item_qty                         AS  DECIMAL             
                                                 COLUMN-LABEL "Quantity"
                                                 LABEL "Quantity"
                                                 FORMAT "->>>,>>9.99"
                                                 HELP "Enter the Quantity|no|yes"
    FIELD   item_type                        AS  CHARACTER           
                                                 COLUMN-LABEL "Item Type"
                                                 LABEL "Item Type"
                                                 FORMAT "X"
                                                 INIT "S"
                                                 HELP "Enter the Item Type|no|yes"
    FIELD   line_sequence                    AS  INTEGER             
                                                 COLUMN-LABEL "line_sequence"
                                                 LABEL "line_sequence"
                                                 FORMAT ">9"
                                                 HELP "Enter the line_sequence|no|yes"
    FIELD   lot                              AS  CHARACTER           
                                                 COLUMN-LABEL "Lot"
                                                 LABEL "Lot"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Lot|no|yes"
    FIELD   mach_type                        AS  CHARACTER           
                                                 COLUMN-LABEL "Machine Type"
                                                 LABEL "Machine Type"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Machine Type|no|yes"
    FIELD   msg_status                       AS  CHARACTER           
                                                 COLUMN-LABEL "msg_status"
                                                 LABEL "msg_status"
                                                 FORMAT "x"
                                                 INIT ""
                                                 HELP "Enter the msg_status|no|yes"
    FIELD   ns_comment                       AS  CHARACTER           
                                                 COLUMN-LABEL "Non-Stock Comment"
                                                 LABEL "Non-Stock Comment"
                                                 FORMAT "X(30)"
                                                 INIT ""
                                                 HELP "Enter the Non-Stock Comment|no|yes"
    FIELD   oldextdCost                      AS  DECIMAL             
                                                 COLUMN-LABEL "Old Ex Cost"
                                                 LABEL "Old Ex Cost"
                                                 FORMAT "$>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Old Ex Cost|no|yes"
    FIELD   oldUnitCost                      AS  DECIMAL             
                                                 COLUMN-LABEL "Prev Unit Cost"
                                                 LABEL "Previous Unit Cost"
                                                 FORMAT "$>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Previous Unit Cost|no|yes"
    FIELD   old_stock_stat                   AS  CHARACTER           
                                                 COLUMN-LABEL "Old Stock Status"
                                                 LABEL "Old Stock Status"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Old Stock Status|no|yes"
    FIELD   packer                           AS  CHARACTER           
                                                 COLUMN-LABEL "packer"
                                                 LABEL "packer"
                                                 FORMAT "X(14)"
                                                 INIT ""
                                                 HELP "Enter the packer|no|yes"
    FIELD   pallet_id                        AS  CHARACTER           
                                                 COLUMN-LABEL "Pallet Id"
                                                 LABEL "Pallet Id"
                                                 FORMAT "x(17)"
                                                 INIT ""
                                                 HELP "Enter the Pallet Id|no|yes"
    FIELD   pallet_id_from                   AS  CHARACTER           
                                                 COLUMN-LABEL "From Pallet"
                                                 LABEL "From Pallet"
                                                 FORMAT "x(17)"
                                                 INIT ""
                                                 HELP "Enter the From Pallet|no|yes"
    FIELD   pool                             AS  CHARACTER           
                                                 COLUMN-LABEL "Pool"
                                                 LABEL "Pool"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the Pool|no|yes"
    FIELD   po_line                          AS  INTEGER             
                                                 COLUMN-LABEL "po_line"
                                                 LABEL "po_line"
                                                 FORMAT ">>>9"
                                                 HELP "Enter the po_line|no|yes"
    FIELD   po_number                        AS  CHARACTER           
                                                 COLUMN-LABEL "PO Number"
                                                 LABEL "PO Number"
                                                 FORMAT "X(10)"
                                                 INIT ""
                                                 HELP "Enter the PO Number|no|yes"
    FIELD   po_suffix                        AS  CHARACTER           
                                                 COLUMN-LABEL "PO Suffix"
                                                 LABEL "PO Suffix"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Enter the PO Suffix|no|yes"
    FIELD   proc_created                     AS  CHARACTER           
                                                 COLUMN-LABEL "Created by"
                                                 LABEL "Created by"
                                                 FORMAT "x(50)"
                                                 INIT ""
                                                 HELP "Enter the Created by|no|yes"
    FIELD   qa_release_id                    AS  INTEGER             
                                                 COLUMN-LABEL "QA Release ID"
                                                 LABEL "QA Release ID"
                                                 FORMAT ">>>,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the QA Release ID|no|yes"
    FIELD   record_type                      AS  CHARACTER           
                                                 COLUMN-LABEL "Record Type"
                                                 LABEL "Record Type"
                                                 FORMAT "X(6)"
                                                 INIT ""
                                                 HELP "Enter the Record Type|no|yes"
    FIELD   release_id                       AS  CHARACTER           
                                                 COLUMN-LABEL "release_id"
                                                 LABEL "release_id"
                                                 FORMAT "x(24)"
                                                 INIT ""
                                                 HELP "Enter the release_id|no|yes"
    FIELD   result_code                      AS  CHARACTER           
                                                 COLUMN-LABEL "Result Code"
                                                 LABEL "Result Code"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Result Code|no|yes"
    FIELD   result_msg                       AS  CHARACTER           
                                                 COLUMN-LABEL "Result Message"
                                                 LABEL "Result Message"
                                                 FORMAT "x(70)"
                                                 INIT ""
                                                 HELP "Enter the Result Message|no|yes"
    FIELD   row_status                       AS  CHARACTER           
                                                 COLUMN-LABEL "Status"
                                                 LABEL "Status"
                                                 FORMAT "X"
                                                 INIT "O"
                                                 HELP "Enter the Status|no|yes"
    FIELD   rt_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "RT Number"
                                                 LABEL "RT Number"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the RT Number|no|yes"
    FIELD   serial_num                       AS  CHARACTER           
                                                 COLUMN-LABEL "Serial Number"
                                                 LABEL "Serial Number"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Serial Number|no|yes"
    FIELD   shf_num                          AS  INTEGER             
                                                 COLUMN-LABEL "Shf #"
                                                 LABEL "Shift Number"
                                                 FORMAT ">>>>>9"
                                                 HELP "Enter the Shift Number|no|yes"
    FIELD   stock_stat                       AS  CHARACTER           
                                                 COLUMN-LABEL "Stock Status"
                                                 LABEL "Stock Status"
                                                 FORMAT "X"
                                                 INIT ""
                                                 HELP "Enter the Stock Status|no|yes"
    FIELD   substat_code                     AS  INTEGER             
                                                 COLUMN-LABEL "Substatus Code"
                                                 LABEL "Substatus Code"
                                                 FORMAT ">9"
                                                 INIT 0
                                                 HELP "Enter the Substatus Code|no|yes"
    FIELD   sugg_qty                         AS  DECIMAL             
                                                 COLUMN-LABEL "Adjusted Quantity"
                                                 LABEL "Adjusted Quantity"
                                                 FORMAT "->>>,>>9.99"
                                                 HELP "Enter the Adjusted Quantity|no|yes"
    FIELD   task_id                          AS  INTEGER             
                                                 COLUMN-LABEL "Task Id"
                                                 LABEL "Task Id"
                                                 FORMAT ">>>,>>>,>>9"
                                                 HELP "Enter the Task Id|no|yes"
    FIELD   transmission                     AS  INTEGER             
                                                 COLUMN-LABEL "Transmission Number"
                                                 LABEL "Transmission Number"
                                                 FORMAT ">,>>>,>>9"
                                                 HELP "Enter the Transmission Number|no|yes"
    FIELD   TransTypDesc                     AS  CHARACTER           
                                                 COLUMN-LABEL "CF-Transaction Name"
                                                 LABEL "CF-Transaction Name"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the CF-Transaction Name|no|yes"
    FIELD   trans_link                       AS  INTEGER             
                                                 COLUMN-LABEL "Transaction Link"
                                                 LABEL "Transaction Link"
                                                 FORMAT ">>>>>>>>9"
                                                 HELP "Enter the Transaction Link|no|yes"
    FIELD   trans_num                        AS  INTEGER             
                                                 COLUMN-LABEL "trans #"
                                                 LABEL "Transaction Number"
                                                 FORMAT ">>>>>>>>9"
                                                 HELP "Enter the Transaction Number|no|yes"
    FIELD   trans_sec_time                   AS  INTEGER             
                                                 COLUMN-LABEL "Seconds Since Midnight"
                                                 LABEL "Seconds Since Midnight"
                                                 FORMAT ">,>>>,>>9"
                                                 INIT 0
                                                 HELP "Enter the Seconds Since Midnight|no|yes"
    FIELD   trans_type                       AS  CHARACTER           
                                                 COLUMN-LABEL "Transaction Type"
                                                 LABEL "Transaction Type"
                                                 FORMAT "x(2)"
                                                 INIT ""
                                                 HELP "Enter the Transaction Type|no|yes"
    FIELD   truck_id                         AS  CHARACTER           
                                                 COLUMN-LABEL "Truck Id"
                                                 LABEL "Truck Id"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Truck Id|no|yes"
    FIELD   UnitCost                         AS  DECIMAL             
                                                 COLUMN-LABEL "Unit Cost"
                                                 LABEL "Unit Cost"
                                                 FORMAT "$>>>,>>>,>>9.99"
                                                 INIT 0
                                                 HELP "Enter the Unit Cost|no|yes"
    FIELD   uom                              AS  CHARACTER           
                                                 COLUMN-LABEL "Unit of Measure"
                                                 LABEL "Unit of Measure"
                                                 FORMAT "X(4)"
                                                 INIT ""
                                                 HELP "Enter the Unit of Measure|no|yes"
    FIELD   void                             AS  LOGICAL             
                                                 COLUMN-LABEL "Void"
                                                 LABEL "Void"
                                                 FORMAT "yes/no"
                                                 INIT no
                                                 HELP "Enter the Void|no|yes"
    FIELD   wh_num                           AS  CHARACTER           
                                                 COLUMN-LABEL "Warehouse"
                                                 LABEL "Warehouse"
                                                 FORMAT "x(4)"
                                                 INIT ""
                                                 HELP "Enter the Warehouse|no|yes"

                    .
TEMP-TABLE AuditLog:TRACKING-CHANGES = YES.

/*************** CONTEXT TEMP-TABLES **************/
DEF TEMP-TABLE ds_Filter NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "X(15)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 COLUMN-LABEL "Seq"
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   isAnd                            AS  LOGICAL             
                                                 COLUMN-LABEL "And/Or"
                                                 FORMAT "AND/OR"
                                                 INIT YES
                                                 HELP "Enter AND / OR"
    FIELD   OpenParen                        AS  LOGICAL             
                                                 COLUMN-LABEL "("
                                                 FORMAT "(/."
                                                 INIT NO
                                                 HELP "Open-parentheses : Enter  ( or ."
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Operand                          AS  CHARACTER           
                                                 COLUMN-LABEL "Operand"
                                                 FORMAT "x(10)"
                                                 INIT ""
                                                 HELP "Enter the Operand (=, <, >, <=, >=, <>, BEGINS, MATCHES, CONTAINS)"
    FIELD   FieldValue                       AS  CHARACTER           
                                                 COLUMN-LABEL "Field Value"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Value"
    FIELD   CloseParen                       AS  LOGICAL             
                                                 COLUMN-LABEL ")"
                                                 FORMAT ")/."
                                                 INIT NO
                                                 HELP "Close-parentheses : Enter ) or ."
    INDEX   idxFilterDtl IS PRIMARY TableName Seq 
    .
DEF TEMP-TABLE ds_Sort NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   Seq                              AS  INTEGER             
                                                 FORMAT "999"
                                                 HELP "Enter the Sequence #"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   isAscending                      AS  LOGICAL             
                                                 COLUMN-LABEL "Ascending/Descending"
                                                 FORMAT "ASCENDING/DESCENDING"
                                                 INIT YES
                                                 HELP "Enter Ascending / Descending"
    INDEX   idxSort IS PRIMARY  TableName Seq 
    .
DEF TEMP-TABLE ds_Error NO-UNDO
    FIELD   Type                             AS  CHARACTER           
                                                 INIT ""
                                                 HELP "Enter W=Warning, I=Informational, E=Error"
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   TableKey                         AS  CHARACTER           
                                                 COLUMN-LABEL "Table Key"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   Error#                           AS  INTEGER             
                                                 COLUMN-LABEL "Msg #"
                                                 FORMAT "9999"
                                                 HELP "Enter the Message #"
    FIELD   ErrorMsg                         AS  CHARACTER           
                                                 COLUMN-LABEL "Message"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Message"
    .
DEF TEMP-TABLE ds_Control NO-UNDO
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    INDEX   PropName   PropName
    .
DEF TEMP-TABLE ds_SchemaAttr NO-UNDO
    FIELD   TableName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Table Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   PropName                         AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Property Name"
    FIELD   PropValue                        AS  CHARACTER           
                                                 COLUMN-LABEL "Property Name"
                                                 FORMAT "x(300)"
                                                 INIT ""
                                                 HELP "Enter the Property Value"
    .
DEF TEMP-TABLE ds_ExtFields NO-UNDO
    FIELD   GUID                             AS  DECIMAL             
                                                 FORMAT "999999999.999999999"
                                                 HELP "Enter the GUID"
    FIELD   DBTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Database Table Name"
    FIELD   DSTableName                      AS  CHARACTER           
                                                 FORMAT "x(35)"
                                                 INIT ""
                                                 HELP "Enter the Dataset Table Name"
    FIELD   FieldName                        AS  CHARACTER           
                                                 COLUMN-LABEL "Field Name"
                                                 FORMAT "x(40)"
                                                 INIT ""
                                                 HELP "Enter the Temp-Table Field Name"
    FIELD   DataType                         AS  CHARACTER           
                                                 COLUMN-LABEL "Data Type"
                                                 FORMAT "x(20)"
                                                 INIT ""
                                                 HELP "Enter the Field Data Type"
    .


/********************************************************
* PRO-DATA-SET 
********************************************************/
DEF DATASET dsAudit
    FOR AuditLog,
        ttExtValues  /* Extention Field Values */
        .


DEF DATASET ds_Context
    FOR
        ds_Filter,     /* Filtering parameters */
        ds_Sort,       /* Sorting parameters   */
        ds_Error,      /* Returned Messages    */
        ds_Control     /* Control settings     */
        .


DEF DATASET ds_Schema
    FOR
        ds_SchemaAttr,   /* Schema Attributes   */
        ds_ExtFields     /* Extended-Fields     */
        .


/**************************** END OF FILE ****************************/


