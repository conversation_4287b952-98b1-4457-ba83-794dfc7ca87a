"ttTablePropsCREATE"
"ttTablePropsTblTableNamereturnaction"
"ttTablePropsTblBatchSize50"
"ttTablePropsTblFILLyes"
"ttTablePropscanReadyes"
"ttTablePropscanCreateyes"
"ttTablePropscanUpdateyes"
"ttTablePropscanDeleteyes"
"ttTablePropsUniqueKeyGUID"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamereturnaction"
"ttFieldPropsFldNamecode"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(2)"
"ttFieldPropsFldSideLabelAction Code"
"ttFieldPropsFldColLabelAction Code"
"ttFieldPropsFldHelpEnter the Action Code"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamereturnaction"
"ttFieldPropsFldNameco_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelCompany"
"ttFieldPropsFldColLabelCompany"
"ttFieldPropsFldHelpEnter the Company"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamereturnaction"
"ttFieldPropsFldNameCustomFields"
"ttFieldPropsFldDataTypeLogical"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInitNo"
"ttFieldPropsFldFormatYes/No"
"ttFieldPropsFldSideLabelCustom Fields"
"ttFieldPropsFldColLabelCustom Fields"
"ttFieldPropsFldHelpEnter the Custom Fields"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamereturnaction"
"ttFieldPropsFldNamedescription"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatX(30)"
"ttFieldPropsFldSideLabelDescription"
"ttFieldPropsFldColLabelDescription"
"ttFieldPropsFldHelpEnter the Description"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamereturnaction"
"ttFieldPropsFldNameGUID"
"ttFieldPropsFldDataTypedecimal"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit0"
"ttFieldPropsFldFormat*********.*********"
"ttFieldPropsFldSideLabelGUID"
"ttFieldPropsFldColLabelGUID"
"ttFieldPropsFldHelpEnter the GUID"
"ttFieldPropsCREATE"
"ttFieldPropsFldTableNamereturnaction"
"ttFieldPropsFldNamewh_num"
"ttFieldPropsFldDataTypecharacter"
"ttFieldPropsFldExt0"
"ttFieldPropsFldInit"
"ttFieldPropsFldFormatx(4)"
"ttFieldPropsFldSideLabelWarehouse"
"ttFieldPropsFldColLabelWarehouse"
"ttFieldPropsFldHelpEnter the Warehouse"
"ttDataSourceCREATE"
"ttDataSourceDSrcNamereturnaction"
"ttDataSourcecDSTable"
"ttDataSourcelUseQueryyes"
"ttDataSourcecPostTablereturn_act_1"
"ttDataSourcePreferDataSetno"
"ttDataSourceMergeByFieldyes"
"ttDataSourceJoinsCREATE"
"ttDataSourceJoinsDSrcNamereturnaction"
"ttDataSourceJoinscDSTablereturnaction"
"ttDataSourceJoinscDBTableirms.return_action"
"ttDataSourceJoinscBufNamereturn_act_1"
"ttDataSourceJoinscDBWhere"
"ttDataSourceJoinscDBSort"
"ttDataSourceJoinscDBTableFldsco_num,wh_num,code"
"ttBLPCREATE"
"ttBLPBLPOrder1"
"ttBLPBLPNamey:\BE_Area\src\blp\ActionCodes_blp.p"
"ttOptionsCREATE"
"ttOptionsmakeProxyno"
"ttOptionsmakeFirstno"
"ttOptionsmakeNextno"
"ttOptionsmakePrevno"
"ttOptionsmakeLastno"
"ttOptionsmakepostyes"
"ttOptionsmakeLoadyes"
"ttOptionsmakeSchemayes"
"ttOptionsOneTransactionno"
"ttOptionsttDirtt_def"
"ttOptionsGenTTno"
"ttOptionsUseTTDefno"
"ttAttachSourceCREATE"
"ttAttachSourcecDSTablereturnaction"
"ttAttachSourcecSrcNamereturnaction"
"ttAttachSourcelDefaultyes"
"ttAttachSourcecCreateFieldGUID"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablereturnaction"
"ttAttachDtlcSrcNamereturnaction"
"ttAttachDtlMappedFieldscode,return_act_1.code"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablereturnaction"
"ttAttachDtlcSrcNamereturnaction"
"ttAttachDtlMappedFieldsco_num,return_act_1.co_num"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablereturnaction"
"ttAttachDtlcSrcNamereturnaction"
"ttAttachDtlMappedFieldsdescription,return_act_1.description"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablereturnaction"
"ttAttachDtlcSrcNamereturnaction"
"ttAttachDtlMappedFieldsGUID,return_act_1.GUID"
"ttAttachDtlnoPostno"
"ttAttachDtlCREATE"
"ttAttachDtlcDSTablereturnaction"
"ttAttachDtlcSrcNamereturnaction"
"ttAttachDtlMappedFieldswh_num,return_act_1.wh_num"
"ttAttachDtlnoPostno"
"ttNotesCREATE"
"ttNotesseq0"
"ttNotesnote"