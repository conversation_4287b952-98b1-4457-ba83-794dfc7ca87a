/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\AssignRMA\AssignRMA_getschema.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:38 PM
**=================================================================**/


/* Business Entity Definintions */
{AssignRMA/AssignRMA/AssignRMA_ds.i}
{AssignRMA/AssignRMA/AssignRMA_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Schema.


    RUN Schema_Fill .


/**************************** END OF FILE ****************************/


