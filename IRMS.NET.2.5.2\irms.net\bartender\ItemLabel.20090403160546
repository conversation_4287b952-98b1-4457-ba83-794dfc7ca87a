<?xml version="1.0" encoding="utf-8"?>
<XMLScript Version="2.0">
   <Command>
       <Print JobName="Job1">
           <PrintSetup>
               <Printer>\\panther\zebra220</Printer>
           </PrintSetup>
           <Format CloseAtEndOfJob="true">itemlabel.btw</Format>
           <RecordSet Name="Text File 1" Type="btTextFile">
               <Delimitation>btDelimCustom</Delimitation>
               <FieldDelimiter>|</FieldDelimiter>
               <UseFieldNamesFromFirstRecord>true</UseFieldNamesFromFirstRecord>
               <TextData>
               <![CDATA[ItemNumber|ItemDescription|RcdDate|ReceivedBy|PONumber|POSuffix|Lot|Expiry|Serial|Quantity|Status|ItemType|MSRP|UOM|Company|Warehouse|Zone|AltNum|UPC|Group|Line|SubLine|Class|MSDS|Country|SecDesc|LongDesc|NumofLabels|Sell Price
               1001VS|Test item 123 1232131111111111|04/03/09|IRMS||  || |0||S|0|EA|MDC|CRP|02|444||LIMU|A|2222||iii|Test item|1|$345.0000
               ]]>
               </TextData>
           </RecordSet>
       </Print>
   </Command>
</XMLScript>
