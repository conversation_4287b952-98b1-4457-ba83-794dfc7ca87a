/**=================================================================**
* Y:\BE_Area\src\be\AssignRMA\SearchReturnQuantity\SearchReturnQuantity_getschema.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 01/11/06, 09:39 PM
**=================================================================**/


/* Business Entity Definintions */
{AssignRMA/SearchReturnQuantity/SearchReturnQuantity_ds.i}
{AssignRMA/SearchReturnQuantity/SearchReturnQuantity_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF       OUTPUT PARAM DATASET FOR ds_Schema.


    RUN Schema_Fill .


/**************************** END OF FILE ****************************/


