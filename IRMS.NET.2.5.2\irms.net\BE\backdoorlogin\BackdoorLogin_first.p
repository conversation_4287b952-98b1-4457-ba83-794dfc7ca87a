/**=================================================================**
* Y:\irms.net.2.5.1\irms.net\BE\backdoorlogin\BackdoorLogin_first.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 03/25/08, 08:19 PM
**=================================================================**/


/* Business Entity Definintions */
{backdoorlogin/BackdoorLogin_ds.i}
{backdoorlogin/BackdoorLogin_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF       OUTPUT PARAM DATASET FOR dsBackdoorLogin .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN
        CREATE ds_Control.
    ASSIGN ds_Control.PropName = 'COMMAND'
           ds_Control.PropValue = 'FILL'.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


